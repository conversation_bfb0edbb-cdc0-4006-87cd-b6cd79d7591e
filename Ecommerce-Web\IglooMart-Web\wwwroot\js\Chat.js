﻿let connection;
let isConnected = false;

$(function () {
    const userId = $("#currentUserId").val();
    const userType = $("#currentUserType").val().toLowerCase();



    let senderType;
    switch (userType) {
        case "admin":
            senderType = 0;
            break;
        case "customer":
            senderType = 1;
            break;
        case "user":
            senderType = 2;
            break;
        default:
            senderType = -1; 
    }

    connection = new signalR.HubConnectionBuilder()
        .withUrl(BASE_URL + "ChatHub?userId=" + userId + "&userType=" + senderType)
        .build();

    connection.start().then(function () {
        isConnected = true;
        console.log("Connected as admin");

    }).catch(function (err) {
        console.error("❌ Failed to connect:", err.toString());
    });


    connection.on("ReceiveMessage", function (fromUserId, message) {
        if (fromUserId == selectedUserId) {
            const msgHtml = `
                <div class="media incoming-message">
                    <div class="media-body">
                        <div class="chat-msg">${message}</div>
                    </div>
                </div>
            `;
            $('#chat-messages').append(msgHtml);
            $('.chat-detail').scrollTop($('#chat-messages')[0].scrollHeight);
        } else {
        }
    });

    $("#btnsend").on("click", function () {
        const fromUser = $("#currentUserId").val();
        const toUser = $("#receiverId").val();
        const message = $("#chat-input").val();
        if (!message) {
            console.error("❌ Message cannot be empty!");
            return;
        }
        if (senderType === 0) {
            connection.invoke("SendMessage", fromUser, toUser, message)
                .then(() => {
                    const chatBox = document.getElementById("chat-messages");

                    const messageContainer = document.createElement("div");
                    messageContainer.classList.add("media", "reverse");

                    const mediaBody = document.createElement("div");
                    mediaBody.classList.add("media-body");

                    const chatMessage = document.createElement("div");
                    chatMessage.classList.add("chat-msg");
                    chatMessage.textContent = message;

                    mediaBody.appendChild(chatMessage);
                    messageContainer.appendChild(mediaBody);

                    chatBox.appendChild(messageContainer);
                    $("#chat-input").val('');
                    chatBox.scrollTop = chatBox.scrollHeight;
                })
                .catch(function (err) {
                    console.error("Error sending message: ", err.toString());
                });

        } else if (senderType === 1 || senderType === 2) {
            connection.invoke("SendMessageToAllAdmins", fromUser, message)
                .then(() => {
                    const chatBox = document.getElementById("chat-messages");

                    const messageContainer = document.createElement("div");
                    messageContainer.classList.add("media", "reverse");

                    const mediaBody = document.createElement("div");
                    mediaBody.classList.add("media-body");

                    const chatMessage = document.createElement("div");
                    chatMessage.classList.add("chat-msg");
                    chatMessage.textContent = message;

                    mediaBody.appendChild(chatMessage);
                    messageContainer.appendChild(mediaBody);

                    chatBox.appendChild(messageContainer);
                    $("#chat-input").val('');
                    chatBox.scrollTop = chatBox.scrollHeight;
                })
                .catch(function (err) {
                    console.error("Error sending message: ", err.toString());
                });
        }

    });
    $(window).on('beforeunload', function () {
        if (isConnected) {
            connection.stop();
        }
    });

});

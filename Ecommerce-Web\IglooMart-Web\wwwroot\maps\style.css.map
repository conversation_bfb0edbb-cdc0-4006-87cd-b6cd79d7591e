{"version": 3, "sources": ["style.scss", "default/_variables.scss", "default/_shortcode.scss", "default/_reset.scss", "default/_typography.scss", "default/_extend.scss", "default/_animations.scss", "default/_mixins.scss", "default/_common.scss", "default/_forms.scss", "elements/_about.scss", "elements/_back-to-top.scss", "elements/_breadcrumb.scss", "elements/_button.scss", "elements/_categories.scss", "elements/_contact.scss", "elements/_countdown.scss", "elements/_error.scss", "elements/_newsletter.scss", "elements/_pagination.scss", "elements/_poster.scss", "elements/_price-slider.scss", "elements/_privacy-policy.scss", "elements/_section.scss", "elements/_service.scss", "elements/_slick.scss", "elements/_slider.scss", "elements/_social.scss", "elements/_team.scss", "elements/_testimonial.scss", "elements/_video.scss", "elements/_splash.scss", "header/_header-top.scss", "header/_header.scss", "header/_nav.scss", "shop/_header-cart.scss", "shop/_shop.scss", "shop/_product-details.scss", "shop/_checkout.scss", "shop/_my-account.scss", "blog/_blog-list.scss", "blog/_blog-single.scss", "blog/_comment.scss", "blog/_sidebar.scss", "footer/_footer.scss", "default/_spacing.scss"], "names": [], "mappings": "AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAYA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAkDA;AAAA;AAAA;AC9DA;AAAA;AAAA;ACOQ;ADJR;EAEI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;EAEA;EACA;EAEA;EACA;EAEA;EACA;EAEA;EAEA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EAEA;EAEA;EACA;EACA;EAEA;EACA;EACA;EAEA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;;;AE1FJ;AAAA;AAAA;AAIC;EACG;EACA;EACA;;;AAIJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUI;;;AAGJ;AAAA;AAAA;EAGI;;;AAGJ;EACI;EACA;;;AAGJ;EACI;;;AAIJ;EACI;EACA;EACA;;;AAIJ;AAAA;AAAA;EAGC;EACA;EACA;;;AAGD;EACI;;;AAEJ;EACI;;;AAGJ;EACI;;;AAGJ;AAAA;EAEI;;;AAEJ;EACI;EACA;;;AAEJ;AAAA;AAAA;AAAA;EAII;EACA;EACA;EACA;EACA;EACA;;;AAEJ;AAAA;EAEI;;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAIJ;EACI;;;AAGJ;AAAA;EAEI;EACA;EACA;EACA;;;AAEJ;EACI;;;AAEJ;EACI;;;AAGJ;EACI;EACA;;;AAGJ;EACI;;;AAEJ;EACI;EACA;;;AAGJ;AAAA;AAAA;EAGI;EACA;;;AAGJ;AAAA;EAEI;EACA;;;AAEJ;AAAA;EAEI;;;AAIA;EACI;;;AAIR;EACI;EACA;EACA;EACA;EACA;;;AAGJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;;;AAGJ;AAAA;AAAA;AAAA;EAII;EACA;EACA;EACA;;;AAGJ;AAAA;EAEI;;;AAGJ;AAAA;AAAA;AAAA;EAII;EACA;EACA;EACA;;;AAGJ;AAAA;EAEI;;;AAGJ;AAAA;EAEI;;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGJ;EACI;EACA;;;AAGJ;AAAA;EAEI;EACA;;;AAEJ;EACI;EACA;;;AAEJ;AAAA;AAAA;EAGI;;;AAGJ;EACI;EACA;;;AAEJ;AAAA;EAEI;EACA;;;AAEJ;EACI;;;AAEJ;EACI;EACA;;;AAEJ;EACI;EACA;EACA;EACA;;;AAGJ;AAAA;AAAA;EAGI;;;AAGJ;EACI;EACA;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;AAAA;AAAA;EAGI;;;AAIJ;AACA;AAAA;EAEI;EACA;EACA;EACA;EACA;;;AAGJ;AAAA;AAAA;AAAA;EAII;EACA;;;AAGJ;EACI;EACA;EACA;EACA;;;AAGJ;EACI;;;AAGJ;AAAA;EAEI;EACA;;;AAGJ;AAAA;EAEI;;;ACvVJ;AAAA;AAAA;AAIC;EACA;;;AAGD;EACC;EACA;EACA;EACA;EACA;;;AAED;EACC;IACC;IACA;;;AAKF;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAqBC;EACA;;;AAGD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAYC;EACA;EACA;EACA;;;AAGD;AAAA;EAEC;;;AAGD;AAAA;EAEC;;;AAGD;AAAA;EAEC;;;AAGD;AAAA;EAEC;;;AAGD;AAAA;EAEC;;;AAGD;AAAA;EAEC;;;AAeA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACC;;;AAIF;EACC;AAAA;IAEC;;EAED;AAAA;IAEC;;EAGD;AAAA;IAEC;;EAGD;AAAA;IAEC;;;AAKF;EACC;AAAA;IAEC;;EAED;AAAA;IAEC;;EAGD;AAAA;IAEC;;EAGD;AAAA;IAEC;;;AAKF;AAAA;AAAA;AAAA;AAAA;AAAA;EAMC;;;AAGD;AAAA;AAAA;AAAA;EAIC;;;AAGD;AAAA;EAEC;;;AAUA;AAAA;AAAA;AAAA;AAAA;AAAA;EACC;EACA;;AAGD;AAAA;AAAA;AAAA;AAAA;AAAA;EACC;EACA;;AAGD;AAAA;AAAA;AAAA;AAAA;AAAA;EACC;EACA;;;AAIF;EACC;EACA;EACA;EACA;EACA;;AACA;EACC;EACA;;AAED;EACC;EACA;;AAGD;EACC;;AAGD;EACC;;AAGD;EACC;;AAGD;EACC;EACA;;AAGD;EACC;EACA;;AAGD;EACC;EACA;;AAGD;EACC;;;AAIF;EACC;EACA;;;AAGD;EACC;EACA;;;AAGD;EACC;EACA;;;AAID;EACC;EACA;EACA;EACA;;;AAGD;AAAA;AAAA;EAGC;;;AAGD;AAAA;AAAA;AAAA;EAIC;;;AAGD;EACC;;;AAGD;AAAA;AAAA;AAGA;AAAA;EAEC;;;AAGD;EACI;EACH;EACA;;AAGE;EACC;EACA;EACA;EACA;EACA;;AAEA;EAPD;IAQE;;;AAGD;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGD;EACC;;AAKJ;EACC;EACA;EACA;EACA;EACA;EACA;;AACA;EACC;EACA;;AAGA;EACC;;AAGF;EACC;;AAGF;EACC;;;AAIF;EACC;;AACA;EACC;EACA;EACA;EACA;EACA;;AAEA;EACC;EAEA;;AAEA;EACC;;AAIH;EACC;;;ACnYF;AAAA;AAAA;AASA;EACI;;;AAQJ;AAEA;EACI;EACA;EACA;;;AAEJ;EACI;EACA;EACA;;;AAGJ;EACI;;;AAGJ;EACI;EACA;EACA;;;ACtCJ;AAAA;AAAA;AAGA;EACI;;AAEA;EACI;;AAGA;EACI;;;AAKZ;EACI;IACI;;EAEJ;IACI;;;AAIR;EACI;IACI;;EAGJ;IACI;;EAGJ;IACI;;;AAIR;EACI;IACI;;EAGJ;IACI;;EAGJ;IACI;;;AAIR;EACI;;;AAGJ;EACI;IACI;;EAGJ;IACI;;EAGJ;IACI;;;AAMR;AAAA;AAAA;AAIA;EACI;IACI;;EAGJ;IACG;;;AAIP;EACI;IACI;;EAGJ;IACG;;;AAKP;AAAA;AAAA;AAIA;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;AAAA;AAAA;AAIA;EACI;IACI;IACA;;;AAIR;EACI;IACI;IACA;IACA;;;AAKR;AAAA;AAAA;AAGA;EACI;IACI;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;;AAIR;EACI;IACI;;EAGJ;IACI;IACA;;EAEJ;IACI;IACA;;EAGJ;IACI;IACA;;;AAIR;EACI;IACI;;EAGJ;IACI;IACA;;EAEJ;IACI;IACA;;EAGJ;IACI;IACA;;;AAIR;EACI;IACI;;EAGJ;IACI;IACA;;EAEJ;IACI;IACA;;EAGJ;IACI;IACA;;;AJtOR;AAAA;AAAA;AAGA;AAAA;AAAA;AAMA;AAAA;EAEI;EACA;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAIJ;AAAA;AAAA;AKlBE;EACC;;;AADD;EACC;;;AADD;EACC;;;AADD;EACC;;;AADD;EACC;;;AADD;EACC;;;AADD;EACC;;;AL8BH;AAAA;AAAA;AASI;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AAKR;AAEA;EACI;EACA;;;AAGJ;EACI;EACA;;;AAGJ;EACI;;;AAIJ;AAAA;AAAA;AAIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAOI;EACA;;;AAGJ;EACI;EACA;;AACA;EACI;EACA;;;AAKR;EACI;EACA;;AACA;EACI;EACA;;;AAIR;EACI;EACA;;AACA;EACI;EACA;;;AAIR;EACI;EACA;;AACA;EACI;EACA;;;AAIR;EACI;EACA;;AAEA;EAJJ;IAKQ;IACA;;;AAEJ;EARJ;IASQ;IACA;;;AAEJ;EAZJ;IAaQ;IACA;;;AAEJ;EAhBJ;IAiBQ;IACA;;;AAEJ;EAEI;EACA;;AAEA;EALJ;IAMQ;IACA;;;AAEJ;EATJ;IAUQ;IACA;;;AAEJ;EAbJ;IAcQ;IACA;;;AAEJ;EAjBJ;IAkBQ;IACA;;;;AAKZ;EACI;EACA;;AAEA;EAJJ;IAKQ;IACA;;;AAEJ;EARJ;IASQ;IACA;;;AAEJ;EAZJ;IAaQ;IACA;;;AAEJ;EAhBJ;IAiBQ;IACA;;;AAEJ;EAEI;EACA;;AAEA;EALJ;IAMQ;IACA;;;AAEJ;EATJ;IAUQ;IACA;;;AAEJ;EAbJ;IAcQ;IACA;;;AAEJ;EAjBJ;IAkBQ;IACA;;;;AAKZ;EACI;EACA;;AAEA;EAJJ;IAKQ;IACA;;;AAEJ;EARJ;IASQ;IACA;;;AAEJ;EAZJ;IAaQ;IACA;;;AAEJ;EAhBJ;IAiBQ;IACA;;;AAEJ;EAEI;EACA;;AAEA;EALJ;IAMQ;IACA;;;AAEJ;EATJ;IAUQ;IACA;;;AAEJ;EAbJ;IAcQ;IACA;;;AAEJ;EAjBJ;IAkBQ;IACA;;;;AAKZ;EACI;EACA;;AAEA;EAJJ;IAKQ;IACA;;;AAEJ;EARJ;IASQ;IACA;;;AAEJ;EAZJ;IAaQ;IACA;;;AAEJ;EAhBJ;IAiBQ;IACA;;;AAEJ;EAEI;EACA;;AAEA;EALJ;IAMQ;IACA;;;AAEJ;EATJ;IAUQ;IACA;;;AAEJ;EAbJ;IAcQ;IACA;;;AAEJ;EAjBJ;IAkBQ;IACA;;;;AAKZ;EACI;EACA;;AAEA;EAJJ;IAKQ;IACA;;;AAEJ;EARJ;IASQ;IACA;;;AAEJ;EAZJ;IAaQ;IACA;;;AAEJ;EAhBJ;IAiBQ;IACA;;;AAEJ;EAEI;EACA;;AAEA;EALJ;IAMQ;IACA;;;AAEJ;EATJ;IAUQ;IACA;;;AAEJ;EAbJ;IAcQ;IACA;;;AAEJ;EAjBJ;IAkBQ;IACA;;;;AAKZ;EACI;EACA;;AAEA;EAJJ;IAKQ;IACA;;;AAEJ;EARJ;IASQ;IACA;;;AAEJ;EAZJ;IAaQ;IACA;;;AAEJ;EAhBJ;IAiBQ;IACA;;;AAEJ;EApBJ;IAqBQ;IACA;;;AAEJ;EAEI;EACA;;AAEA;EALJ;IAMQ;IACA;;;AAEJ;EATJ;IAUQ;IACA;;;AAEJ;EAbJ;IAcQ;IACA;;;AAEJ;EAjBJ;IAkBQ;IACA;;;AAEJ;EArBJ;IAsBQ;IACA;;;;AAKZ;EACI;EACA;;AAEA;EAJJ;IAKQ;IACA;;;AAEJ;EARJ;IASQ;IACA;;;AAEJ;EAZJ;IAaQ;IACA;;;AAEJ;EAhBJ;IAiBQ;IACA;;;AAEJ;EAEI;EACA;;AAEA;EALJ;IAMQ;IACA;;;AAEJ;EATJ;IAUQ;IACA;;;AAEJ;EAbJ;IAcQ;IACA;;;AAEJ;EAjBJ;IAkBQ;IACA;;;;AAMZ;AAAA;AAAA;AAIA;AAAA;EAEI;EACA;;;AAGJ;AAAA;EAEI;EACA;;;AAGJ;AAAA;EAEI;EACA;;;AAGJ;AAAA;EAEI;EACA;;;AAIJ;AAAA;AAAA;AAIA;AAAA;AAAA;EAGI;;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;EAMI;EACA;;;AAGJ;AAAA;AAAA;EAGI;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAIA;AAAA;AAAA;EAGI;;;AAHJ;AAAA;AAAA;EAGI;;;AAHJ;AAAA;AAAA;EAGI;;;AAHJ;AAAA;AAAA;EAGI;;;AAHJ;AAAA;AAAA;EAGI;;;AAHJ;AAAA;AAAA;EAGI;;;AAHJ;AAAA;AAAA;EAGI;;;AAHJ;AAAA;AAAA;EAGI;;;AAHJ;AAAA;AAAA;EAGI;;;AAHJ;AAAA;AAAA;EAGI;;;AAKR;AAAA;AAAA;AAIA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;;AACA;EAhBJ;IAiBQ;IACA;;;AAEJ;EApBJ;IAqBQ;IACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EAnDJ;IAoDQ;IACA;IACA;IACA;IACA;;;AAGA;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAMJ;EACI;;;AAOhB;AAAA;AAAA;AAIA;EACI;EACA;;AACA;EACI;EACA;;AAEJ;EACI;EACA;;;AMvnBR;AAAA;AAAA;AAIA;AAOA;EACI;;;AAGJ;EACI;;;AAIJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAEJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAEJ;EACI;;;AAGJ;EACI;;;AAEJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;EACA;EACA;;;AAIA;EADJ;IAEQ;;;;ACrGR;AAAA;AAAA;AAIA;AAAA;AAAA;AAAA;EAII;EACA;EACA;EACA;EACA;;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAEI;EACA;;;AAIR;AAAA;AAAA;AAAA;EAII;;;AAGJ;EACI;EACA;;;AAGJ;AAAA;EAEI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;EAMI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACA;;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;AACA;EACA;;AAEJ;AAAA;AAAA;AAAA;AAAA;AAAA;AACI;EACA;;AAEJ;AAAA;AAAA;AAAA;AAAA;AAAA;AACI;EACA;;AAEJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;AACA;;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;AACA;EACA;;AAEJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACI;EACA;;AAEJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACI;EACA;;AAGR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;AACA;;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;AACA;EACA;;AAEJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACI;EACA;;AAEJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACI;EACA;;AAEJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;;AAGR;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;;;AAmBR;AAAA;EAEI;EACA;;AACA;AAAA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;AAAA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;AAAA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAKA;AAAA;EACI;EACA;;AAEJ;AAAA;EACI;;;AAQR;EACI;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;;;AAKZ;EACI;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;EAEA;EACA;EACA;;AAEA;EACI;EACA;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;;AACA;EACI;;;AAKZ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;EACI;EACA;;;AAMJ;AAAA;EACI;EACA;;;AAIJ;EACI;;;AAKJ;EACI;;;ATrNR;AAAA;AAAA;AU5EA;AAAA;AAAA;AAKE;EADD;IAEE;IACA;;;AAED;EALD;IAME;;;AAED;EACC;EACA;;AAID;EACC;EACA;;AACA;EAHD;IAIE;;;AAED;EAND;IAOE;;;AAGF;EACC;EACA;EACA;;AAED;EACC;EACA;;AAED;EACC;;AACA;EAFD;IAGE;;;AAGF;EACC;;AACA;EAFD;IAGE;;;AAIH;EACC;;AACA;EAFD;IAGE;;;AAGA;EACC;EACA;EACA;;AAED;EACC;;AACA;EAFD;IAGE;;;AAED;EALD;IAME;;;AAGF;EACC;;AACA;EACC;;;AAOL;EACC;EACA;;AACA;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAIF;EACC;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EARD;IASE;;;AAED;EACC;;AAGA;EACC;EACA;;AAED;EACC;;AAGF;EACC;;;AAIF;EACC;EACA;;AACA;EAHD;IAIE;;;AAED;EACC;;AACA;EACC;;;AAKH;EACC;;AACA;EAFD;IAGE;;;AAED;EACC;EACA;EACA;EACA;;AAED;EACC;;;AAKD;EACC;;AACA;EACC;EACA;;AAED;EACC;;AACA;EAFD;IAGE;;;;ACpKJ;AAAA;AAAA;AAGA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EAlBJ;IAmBQ;IACA;IACA;;;AAEJ;EACC;;AAED;EACD;EACE;EACA;EACA;;AACG;EALJ;IAMQ;IACA;;;AAEJ;EACA;EACG;EACA;;AACA;EAJH;IAKO;;;;AC3Cf;AAAA;AAAA;AAGA;EACI;EACA;EACA;;AAEI;EACI;EACA;;AACA;EAHJ;IAIO;;;AAEH;EANJ;IAOO;;;AAEH;EATJ;IAUO;;;AAGP;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAKhB;EACI;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;;AAGA;EACI;;AAGR;EACI;EACA;EACA;EACA;;;ACnEZ;AAAA;AAAA;AAMI;AAAA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;AAAA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EAvBJ;AAAA;IAwBQ;IACA;;;AAEJ;AAAA;EACI;EACA;EACA;EACA;;AAGA;AAAA;EACI;;AAIJ;AAAA;EACI;EACA;EACA;EACA;EACA;;AAGR;AAAA;EACI;EACA;EACA;;AACA;AAAA;EACI;;AAGR;AAAA;EACI;EACA;;AACA;AAAA;EACI;;AAGR;AAAA;EACI;EACA;;AACA;AAAA;EACI;;AAEJ;AAAA;EACI;;AAIA;AAAA;EACI;;AAIZ;AAAA;EACI;EACA;;AACA;AAAA;EACI;;AAEJ;AAAA;EACI;;AAGR;AAAA;EACI;;AACA;AAAA;EACI;EACA;EACA;;AAGR;AAAA;EACI;EACA;;AACA;EAHJ;AAAA;IAIQ;;;AAEJ;AAAA;EACI;EACA;EACA;;AACA;EAJJ;AAAA;IAKQ;;;AAGR;AAAA;EACI;EACA;EACA;EACA;EACA;;AAEJ;AAAA;EACI;;AACA;AAAA;EACI;;AAGJ;AAAA;EACI;EACA;EACA;;AAIZ;AAAA;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;AAAA;EACI;;;AChJhB;AAAA;AAAA;AAGA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;EACA;;AAEJ;EACI;EACA;;AAEA;EACI;;;AAKZ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;;AAGJ;EACI;EACA;;AAEJ;EACI;EACA;EACA;;AAEJ;EACI;EACA;;;AAIR;EACI;;AACA;EACI;;AAEJ;EACI;;AACA;EACI;EACA;;;AAKZ;EACI;EACA;;AACA;EACI;;AAEJ;EACI;;AACA;EACI;EACA;;AAGR;EACI;EACA;EACA;;AAEJ;EACI;;;AAKJ;EACI;;;AAKJ;EACI;;AACA;EACI;;AAEJ;EALJ;IAMQ;;;AAGR;EACI;;;ACtIR;AAAA;AAAA;AAGA;EACC;EACA;EACA;;;AAGA;EACC;EACA;;AAGA;EADD;IAEE;;;AAED;EACC;EACA;;AAKA;EACC;;AAGF;EACC;;AAED;EACC;;AAID;EACC;;AACA;EAFD;IAGE;;;;ACvCJ;AAAA;AAAA;AAGA;EACI;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EAVJ;IAWQ;IACA;;;AAEJ;EAdJ;IAeQ;IACA;IACA;;;AAEJ;EAnBJ;IAoBQ;;;AAEJ;EACI;;AACA;EACI;;AAIZ;EACI;EACA;EACA;EACA;EACA;;AACA;EANJ;IAOQ;;;AAEJ;EATJ;IAUQ;;;AAGR;EACI;EACA;EACA;;;AAKJ;EACI;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;;AAGR;EACI;EACA;EACA;;AAEJ;EACI;;;AC5ER;AAAA;AAAA;AAGA;EACI;EACA;EACA;EACA;EACA;;AACA;EANJ;IAOQ;IACA;;;AAEJ;EAVJ;IAWQ;IACA;;;AAEJ;EACI;;AACA;EAFJ;IAGQ;IACA;;;AAEJ;EACI;;AAEJ;EACI;IACI;;;AAGR;EACI;;;AAQZ;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACG;EATJ;IAUQ;;;;AAIR;EACC;EACA;EACA;;AACA;EAJD;IAKE;;;AAED;EAPD;IAQE;;;;AAIF;EACI;;AACA;EACI;;AAEJ;EACI;;AAEJ;EACI;EACA;;AAGA;EACI;EACA;EACA;EACA;EACA;;AACA;EANJ;IAOQ;IACA;;;AAGR;EACI;EACA;;AACA;EAHJ;IAIQ;;;AAIZ;EACI;;AACA;EACI;;;ACrGZ;AAAA;AAAA;AAGA;EACE;EACA;;AACA;EAHF;IAII;;;AAGF;EAPF;IAQI;;;;AAMA;EADF;IAEI;IACA;;;;AAKN;EACE;EACA;EACA;;AACA;EAJF;IAKI;;;AAEF;EACE;EACA;EACA;EACA;EACA;;AACA;EACE;;AAEF;EACE;;AAEF;EAZF;IAaI;;;;AAIN;EACE;;AACA;EAFF;IAGI;;;AAEF;EACE;EACA;;;AAGJ;EACE;EACA;EACA;EACA;;;AC9DF;AAAA;AAAA;AAGA;EACE;EACA;;AACA;EAHF;IAIM;;;AAEJ;EANF;IAOM;;;AAEJ;EATF;IAUM;;;AAGA;EACI;;AACA;EACI;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;;;AC9D1B;AAAA;AAAA;AAGA;EACI;EACA;EACA;;AACA;EACI;;AACA;EACI;EACA;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EARJ;IASQ;;;AAEJ;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAKR;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMO;;;AAEH;EARJ;IASO;;;AAEH;EAXJ;IAYQ;;;AAGR;EACI;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;;AAKR;EACI;;AACA;EAFJ;IAGQ;;;AAIJ;EACI;;;AAMhB;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;AAEJ;EAPJ;IAQQ;;EACA;IACI;;;AAGR;EAbJ;IAcQ;;;AAEJ;EAhBJ;IAiBQ;;;AAGJ;EACI;EACA;;AACA;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EALJ;IAMQ;;;AAKJ;EADJ;IAEQ;;;;AAMhB;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;AAEJ;EAPJ;IAQQ;;;;AAIR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EARJ;IASQ;IACA;;;AAEJ;EAZJ;IAaQ;;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;EACI;;AAEJ;EACI;EACA;EACA;;AAEJ;EACI;EACA;EACA;;AAEJ;EACI;EACA;EACA;;AAEJ;EACI;EACA;EACA;;;AAOR;EACI;;;AAMR;EACI;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EAPJ;IAQQ;;;AAEJ;EACI;EACA;;AACA;EAHJ;IAIQ;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;;AAEJ;EACI;EACA;;AAIJ;EADJ;IAEQ;;;AAGR;EACI;;;AC1PR;AAAA;AAAA;AAIA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGJ;EACI;EACA;EACA;;;AAGJ;EACI;EACA;EACA;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;;;AAGJ;AAAA;EAEI;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGJ;EACI;EACA;;;AAGJ;EACI;;;AC9DJ;AAAA;AAAA;AAIC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGF;EACC;EACA;EACA;;AAGD;EACC;;AAED;EACC;EACA;;AACA;EACC;;AACA;EACC;;;ACrCJ;AAAA;AAAA;AAGA;EACI;EACA;EACA;EACA;EACH;EACA;;AACG;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;AAGL;EACC;;AACA;EACA;EACA;;AAGD;EACC;;AACA;EACA;EACA;;AAGD;EACC;;AACA;EACC;EACA;;;AAKH;EACG;EACA;;AACA;EAHH;IAII;;;AAGD;EACC;EACA;;AACA;EACC;;;AAKL;EACC;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACC;EACA;EACA;;AAGA;EACC;EACA;EACA;EACA;;AACA;EACC;;AAKF;EACC;;;AAKH;EACC;;AACA;EAFD;IAGE;;;AAED;EACC;;AACA;EAFD;IAGE;;;AAED;EALD;IAME;IACA;;;AAED;EACC;;;AC1GH;AAAA;AAAA;AAGA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EATJ;IAUQ;;;AAEJ;EAZJ;IAaQ;;;AAEJ;EAfJ;IAgBQ;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;;AACA;EACI;;AAGR;EACI;EACA;EACA;EACA;;AAEJ;EACI;;AACA;EACI;EACA;EACA;;AAGR;EACI;EACA;EACA;EACA;EACA;;AACA;EACI;;AAEJ;EACI;EACA;EACA;EACA;;AAEJ;EACI;;AACA;EACI;;AAGR;EACI;;AAGR;EACI;EACA;EACA;EACA;;AACA;EACI;;AAEJ;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;EACA;;AAIJ;EACI;EACA;;AAEJ;EACI;;;AAQhB;EACI;;AACA;EACI;EACA;;AAEJ;EACI;;;ACtIR;AAAA;AAAA;AAKI;EACI;;AACA;EAFJ;IAGQ;;;AAGR;EACI;;AACA;EAFJ;IAGQ;;;;AAMR;EACI;;AAEJ;AAAA;EAEI;;;AAKJ;EACI;;AAEJ;AAAA;EAEI;;;AAKJ;EACI;;AACA;EAFJ;IAGQ;;;AAGR;EACI;;AACA;EAFJ;IAGQ;;;;AAKZ;EACI;;AAEI;EACI;EACA;;AAIJ;EACI;;AACA;EACI;EACA;;AAIJ;EACI;;AACA;EACI;EACA;;;AAOpB;AAGI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGA;EACI;;AAGR;EA5BJ;IA6BQ;IACA;IACA;;;AAII;EACI;;AAMJ;EACI;;AAMZ;EACI;;AAEA;EACI;;AAKR;EACI;EACA;;AACA;EACI;;AAEJ;EACI;;AAGR;EACI;;AACA;EAFJ;IAGQ;;;;AAOZ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;;AACA;EACI;;AAGR;EA7BJ;IA8BQ;IACA;IACA;;;AAII;EACI;;AAMJ;EACI;;;AAQhB;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AACA;EAHJ;IAIQ;;;;AAOhB;AAGI;EACI;EACA;EACA;EACA;;AAEA;EACI;EACA;;AAIJ;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EACI;EACA;;AACA;EAHJ;IAIQ;;;;AAQhB;EACI;EACA;EACA;EACA;;AAEA;EANJ;IAOQ;;;AAEJ;EATJ;IAUQ;;;AAGJ;EACI;EACA;;AACA;EAHJ;IAIQ;;;AAEJ;EANJ;IAOQ;;;;AAOZ;EACI;EACA;EACA;EACA;;AAEA;EANJ;IAOQ;;;AAEJ;EATJ;IAUQ;;;AAGJ;EACI;EACA;;AACA;EAHJ;IAIQ;;;AAEJ;EANJ;IAOQ;;;;AAOZ;EACI;EACA;EACA;EACA;;AAEA;EANJ;IAOQ;;;AAEJ;EATJ;IAUQ;;;AAGJ;EACI;EACA;;AACA;EAHJ;IAIQ;;;AAEJ;EANJ;IAOQ;;;;AAOZ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;EACI;EACA;;AAEJ;EACI;EACA;;;AAKZ;AAGI;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AACA;EAHJ;IAIQ;;;;AASJ;EACI;EACA;EACA;EACA;EACA;;;AAMhB;EACI;EACA;;;AAGJ;EACI;;;AAIA;EACI;EACA;;AACA;EACI;;AAEJ;EACI;;;AAOR;EACI;;AACA;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;;AACA;EACI;;AAGR;EACI;;AACA;EACI;EACA;;AAMZ;EACI;EACA;;AAGO;EACI;;AAOf;EACI;;AAEI;EACI;;AACA;EACI;EACA;;AAIJ;EACI;;AACA;EACI;;;ACte5B;AAAA;AAAA;AAII;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGR;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;;;AAEJ;EARJ;IASQ;;;AAEJ;EAXJ;IAYQ;;;AAGR;EACI;EACA;;AACA;EAHJ;IAIQ;;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EAPJ;IAQQ;IACA;;;AAEJ;EACI;;AACA;EACI;EACA;;AACA;EAEI;EACA;;AACA;EACI;EACA;;AAKhB;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;;AAEJ;EACI;;AACA;EACI;EACA;;AAMZ;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EACI;;AAKR;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;;AAKZ;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EACI;EACA;;AAGJ;EACI;;AACA;EACI;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EAjBJ;IAkBQ;IACA;;;AAGA;EACI;;AAEJ;EACI;EACA;EACA;;AAMR;EACI;EACA;EACA;;AAKR;EACI;;AAIJ;EACI;EACA;;AACA;EAHJ;IAIQ;IACA;;;AAGA;EACI;EACA;;AAGA;EACI;EACA;;;AAQxB;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EAPJ;IAQQ;;;AAEJ;EAVJ;IAWQ;IACA;;;AAEJ;EAdJ;IAeQ;IACA;;;AAEJ;EACI;;AACA;EAFJ;IAGQ;;;AAGA;EADJ;IAEO;;;AAIH;EADJ;IAEQ;;;AAEJ;EAJJ;IAKQ;IACA;;;AAEJ;EARJ;IASQ;IACA;;;AAKJ;EADJ;IAEQ;;;AAEJ;EAJJ;IAKQ;;;AAEJ;EACI;;AAGR;EACI;;AAIJ;EADJ;IAEQ;IACA;;;AAII;EADJ;IAEQ;IACA;IACA;IACA;;EACA;IACI;;EACA;IACI;;;AAIZ;EAbJ;IAcQ;;;AAMZ;EACI;EACA;;AAEA;EACI;EACA;;AACA;EAHJ;IAIQ;;;AAEJ;EANJ;IAOQ;;;AAEJ;EATJ;IAUQ;;;AAGR;EACI;EACA;;;AAOZ;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;AAGR;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;AAGJ;EACI;EACA;;AAGA;EACI;;AACA;EAFJ;IAGQ;;;AAGA;EACI;EACA;;AAGA;EACI;EACA;;AAQxB;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;AAEJ;EACI;EACA;;AACA;EAHJ;IAIQ;;;AAEJ;EANJ;IAOQ;IACA;;;AAEJ;EAVJ;IAWQ;IACA;;;AAGR;EACI;EACA;;AACA;EACI;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;;AACA;EACI;EACA;;AAEJ;EACI;;AAKhB;EACI;EACA;EACA;EACA;EACA;;AACA;EANJ;IAOQ;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EAVJ;IAWQ;IACA;IACA;IACA;;;AAEJ;EAhBJ;IAiBQ;;;AAGR;EACI;EACA;;AAIR;EACI;EACA;EACA;EACA;EACA;;AACA;EANJ;IAOQ;;;AAEJ;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EAVJ;IAWQ;IACA;;;AAIZ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;;;AAKZ;EACI;EACA;EACA;EACA;EACA;;AACA;EANJ;IAOQ;;;AAEJ;EATJ;IAUQ;;;AAEJ;EACI;;AACA;EAFJ;IAGQ;IACA;;;AAEJ;EANJ;IAOQ;;;AAEJ;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;AAEJ;EAPJ;IAQQ;;;AAEJ;EAVJ;IAWQ;;;AAEJ;EAbJ;IAcQ;;;AAKZ;EACI;EACA;EACA;;AACA;EACI;;AACA;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;;;AAEJ;EARJ;IASQ;;;AAKJ;EACI;;AACA;EACI;EACA;;AACA;EAHJ;IAIQ;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAQhB;EACI;;AAIJ;EACI;EACA;;AACA;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;;AAGA;EACI;EACA;;;AAU5B;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EAPJ;IAQQ;;;AAEJ;EAVJ;IAWQ;;;AAEJ;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;;AAEJ;EACI;;AAEI;EADJ;IAEQ;;;AAKhB;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;;;AAEJ;EARJ;IASQ;IACA;IACA;;;AAGJ;EACI;;AAEJ;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;;AAEI;EACI;EACA;EACA;;AAIZ;EACI;EACA;;AACA;EAHJ;IAIQ;;;AAGA;EACI;EACA;EACA;;AAMpB;EACI;EACA;EACA;EAGA;;AACA;EAPJ;IAQQ;;;AAGA;EACI;EACA;EACA;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;;AAIR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;;AACA;EACI;;AAGR;EACI;EACA;EACA;;AAEJ;EACI;;AACA;EACI;EACA;;AAEJ;EACI;EACA;EACA;EACA;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAKR;EACI;EACA;;AAOA;EACI;EACA;;AAGQ;EACI;EACA;;AAMJ;EACI;EACA;;;AAY5B;EACI;EACA;EACA;EACA;EACA;;AACA;EANJ;IAOQ;;;AAEJ;EATJ;IAUQ;;;AAEJ;EAZJ;IAaQ;;;AAEJ;EAfJ;IAgBQ;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EAVJ;IAWO;IACA;IACA;;;AAEH;EAfJ;IAgBQ;;;AAGR;EACI;;AAEJ;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;AAIJ;EACI;;AAIZ;EACI;;AACA;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EALJ;IAMQ;;;AAEJ;EARJ;IASQ;;;AAIZ;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;IACA;;;AAEJ;EATJ;IAUQ;IACA;;;AAIR;EAnBJ;IAoBQ;IACA;IACA;;;;AAKZ;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;;;AAEJ;EARJ;IASQ;;;AAEJ;EAXJ;IAYQ;;;AAEJ;EACI;EACA;EACA;;AACA;EACI;;AAEJ;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;AAGR;EACI;EACA;;AACA;EAHJ;IAIQ;;;AAGR;EACI;;;AAMR;EACI;;AAEJ;EAEI;;AACA;EAHJ;IAIQ;;;AAKJ;EACI;;;AAQJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAIJ;EACI;;;AAOZ;EACI;IACI;IACA;IACA;;EAEJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;IACA;;EAEJ;IACI;IACA;;;AAIR;EACI;;;AAGJ;EACI;;;AChgCJ;AAAA;AAAA;AAIA;EAEI;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;;AACA;EACI;;;ACjChB;AAAA;AAAA;AAGA;EACC;;;AAED;EACC;EACG;;AACH;EAHD;IAIE;;;AAIA;EACC;;AACA;EAFD;IAGE;;;;AAQH;EADD;IAEE;;;AAED;EACC;EACA;EACA;EACA;;AACA;EACC;EACA;;AAGF;EACC;EACA;;AAED;EACC;;AAIC;EACC;;;AChDJ;AAAA;AAAA;AAII;EACI;;AAKJ;EACI;;;AAIJ;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;EACA;;AAGR;EACI;EACA;;AACA;EACI;;AACA;EACI;;AAGR;EACI;;AACA;EACI;;AAEJ;EACI;;;AAMhB;EACI;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;;AAKJ;EACI;;AAEJ;EACI;EACA;;AACA;EAHJ;IAIQ;;;AAOJ;EAII;;AAHA;EACI;;;AASpB;EACI;EACA;;;AAEJ;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;AAEJ;EACI;;AACA;EACI;EACA;EACA;;AACA;EAJJ;IAKO;;;AAIX;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;;;AAMhB;EACI;EACA;EACA;EACA;EACA;;AACA;EANJ;IAOQ;;;AAEJ;EATJ;IAUQ;IACA;;;AAEJ;EAbJ;IAcQ;IACA;;;AAEJ;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EARJ;IASQ;;;AAGR;EACI;EACA;;AACA;EAHJ;IAIQ;;;;AAOZ;EACI;EACA;EACA;;AAEJ;EACI;;AAEJ;EACI;;;AAIR;EACI;EACA;;AACA;EAHJ;IAIQ;;;AAEJ;EACI;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;;AAEJ;EACI;;AACA;EACI;EACA;;AAGR;EACI;;AAKZ;EACI;;;ACjQR;AAAA;AAAA;AAIA;EACI;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGA;EACI;;AAEJ;EACI;;;ACjEpB;AAAA;AAAA;AAII;EACI;;;AAMR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EATJ;IAUQ;;;;AAKR;EACI;EACA;EACA;EACA;EACA;;AACA;EANJ;IAOQ;;;AAGJ;EAVJ;IAWQ;IACA;;;AAEJ;EAdJ;IAeQ;IACA;IACA;;;AAEJ;EAnBJ;IAoBQ;IACA;;;AAEJ;EACI;;AAGA;EADJ;IAEQ;;;AAEJ;EAJJ;IAKQ;;;AAGA;EADJ;IAEQ;;;AAGR;EACI;;AACA;EAFJ;IAGM;;;AAGN;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;AAEJ;EAPJ;IAQQ;;;AAEJ;EAVJ;IAWQ;;;AAEJ;EACI;IACI;;;AAGR;EAlBJ;IAmBQ;;;AAKZ;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;;;AAEJ;EACI;;AACA;EAFJ;IAGQ;;;AAIA;EACI;EACA;EACA;EACA;EACA;;AAKJ;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;;AAEJ;EAXJ;IAYQ;;;AAGJ;EAfJ;IAgBQ;IACA;;;AAGJ;EApBJ;IAqBQ;IACA;;;AAOpB;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EATJ;IAUQ;;;AAEJ;EACI;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;;AAMhB;EACI;;AACA;EAFJ;IAGQ;IACA;;;;AAMZ;EACI;EACA;;AACA;EACI;;AACA;EACI;;AAGR;EATJ;IAUQ;;;AAEJ;EAZJ;IAaQ;;;;AAKJ;EACI;;AACA;EACI;;;AAKZ;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;;;AAEJ;EARJ;IASQ;;;AAEJ;EAXJ;IAYQ;;;AAEJ;EAdJ;IAeQ;;;AAEJ;EACI;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAIR;EACI;EACA;EACA;;AAEA;EACI;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AAGR;EACI;;AAEJ;EApBJ;IAqBQ;IACA;;;AAMA;EACI;EACA;;AAEJ;EACI;EACA;EACA;;AAOF;EACI;;;AASlB;EACI;;;AAEJ;EACI;EACA;;;AAGJ;EACI;EACA;EACA;;AACA;EACI;EACA;;AACA;EACI;EACA;EACA;;;AASZ;EACI;;;AAEJ;EACI;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EAPJ;IAQQ;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;;AAEJ;EACI;;AAIR;EACG;;AACC;EACI;EACA;EACA;;AAEJ;EACI;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;;AAGA;EACI;;AAQhB;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;;AAMR;EACI;EACA;EACA;;AACA;EACI;;;AAUhB;EACI;;;AAGJ;EACI;EACA;;AAEA;EACI;;AAEJ;EACI;;AAEJ;EACI;EACA;;AACA;EACI;;;AAQZ;EACI;EACA;EACA;EACA;;AACA;EACI;;;A/BpYR;AAAA;AAAA;AgCvGA;AAAA;AAAA;AAGA;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EAPJ;IAQQ;;;AAEJ;EACI;EACA;;AACA;EACI;EACA;EACA;EACA;;AACA;EACI;;AAKhB;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMO;IACA;;;AAEH;EACI;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGA;EACI;;AAGR;EACI;EACA;;AAEJ;EACI;EACA;EACA;;AAIZ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;;AAEJ;EAXJ;IAYQ;;;;AAKZ;AAAA;AAAA;AAGA;EACI;;;AAGJ;EACI;EACA;;AACA;EAHJ;IAIQ;;;AAEJ;EACI;EACA;;AACA;EAHJ;IAIQ;;;AAEJ;EACI;;AAEJ;EACI;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;;AAEJ;EACI;EACA;;AACA;EACI;EACA;;AAGR;EACI;EACA;EACA;;AAKR;EACI;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;;AACD;EACI;;AAEJ;EACI;;;AAQf;EAEI;EACA;EACA;EACA;;AACA;EANJ;IAOQ;IACA;;;AAEJ;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;;AACA;EACI;;;AAQhB;EACI;;AAEI;EACI;;;AAOZ;EACI;;AACA;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;;;AAEJ;EARJ;IASO;;;AAEH;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;;AAIR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;;AAMZ;EADJ;IAEQ;;;;AAMR;EACI;EACA;;AACA;EAHJ;IAIQ;IACA;;;AAEJ;EACI;;AACA;EAFJ;IAGQ;;;AAIJ;EACI;;AAKJ;EADJ;IAEQ;IACA;IACA;IACA;;;;AAOZ;EACI;EACA;;AAII;EACI;;AACA;EACI;;AAQJ;EACI;;AACA;EACI;;AAMpB;EACI;EACA;;AAEI;EACI;EACA;EACA;;AAEI;EACI;;AAGR;EATJ;IAUQ;;;;AAQhB;EACI;EACA;;AACA;EAHJ;IAIQ;;;AAEJ;EANJ;IAOO;;;AAIH;EACI;EACA;;AACA;EACI;EACA;;AAMJ;EADJ;IAEQ;;;AAGA;EACI;;AACA;EACI;;;ACnXxB;AAAA;AAAA;AAII;EACI;;AAEI;EADJ;IAEO;;;AAEH;EAJJ;IAKO;;;;AAMf;EACI;;;AAIA;EACI;EACA;EACA;;AACA;EACI;EACA;;AACA;EAHJ;IAIQ;;;AAIZ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGQ;EACI;EACA;;AACA;EACI;;;AAQxB;EACI;EACA;;AACA;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;AAEJ;EAPJ;IAQQ;;;AAEJ;EAVJ;IAWQ;;;AAIJ;EADJ;IAEQ;;;AAIJ;EACI;EACA;EACA;;AACA;EACI;;AAKR;EADJ;IAEO;;;;AAKX;EACI;;AACA;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;;AACA;EACI;;AAIZ;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;AAEJ;EAPJ;IAQQ;;;AAGR;EACI;EACA;;AAEI;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;;AAUR;EACI;EACA;;AACA;EACI;;;AASZ;EADJ;IAEQ;;;AAIR;EAEQ;IACI;;;AACA;EAFJ;IAGQ;;;AALhB;EASI;IACI;;EACA;IACI;;;AAOJ;EACI;EACA;;;AAMhB;EACI;EACA;;AACA;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;IACA;;;AAGA;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EALJ;IAMQ;;;AAOZ;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EACI;;AAIZ;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EALJ;IAMQ;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;;AACA;EACI;;AAMJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAKR;EACI;;AAKZ;EACI;EACA;;AACA;EAHJ;IAIQ;;;AAEJ;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EACI;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;;AAEJ;EACI;;AAGA;EACI;;AAKR;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;;AACA;EACI;;AAEJ;EACI;;AAGR;EA7BJ;IA8BQ;IACA;;EACA;IACI;;;AAIZ;EACI;;AACA;EAFJ;IAGQ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;AAEJ;EAfJ;IAgBQ;IACA;IACA;;;AAEJ;EACI;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;;AAEJ;EACI;;AACA;EACI;;AAGR;EAtBJ;IAuBQ;;;AAIJ;EACI;;AAIZ;EACI;EACA;EACA;;;AASpB;EADJ;IAEQ;;;;AAKJ;EADJ;IAEQ;;;;AAIR;AAAA;AAAA;AAII;EADJ;IAEQ;;;AAEJ;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;;;AAEJ;EAEI;;AACA;EAHJ;IAIQ;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EATJ;IAUQ;;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EAVJ;IAWQ;IACA;;;AAGR;EACI;;AAEJ;EACI;;AACA;EACI;;AAGR;EACI;;AACA;EACI;;AAQZ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAIZ;EACI;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;;AACA;EAhBJ;IAiBQ;;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;;AACA;EACI;EACA;EACA;EACA;;AAGA;EACI;;AAIZ;EACI;EACA;EACA;EACA;;AAEJ;EACG;EACA;;AAEH;EACI;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;;AAMZ;EACI;EACA;EACA;EACA;;AAIZ;EACI;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAMZ;AAAA;AAAA;AAGA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EARJ;IASQ;IACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;;AACA;EACI;EACA;;AAEJ;EAfJ;IAgBQ;IACA;IACA;IACA;IACA;;;AAEJ;EAtBJ;IAuBQ;IACA;;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EARJ;IASQ;IACA;;;AAEJ;EAZJ;IAaQ;;;AAGR;EACI;EACA;EACA;;AACA;EACI;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;;AAEJ;EACI;EACA;;AAEJ;EACI;;AAEJ;EACI;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;EACI;;AAEJ;EACI;EACA;;AAGA;EACI;;AAKhB;EACI;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;;AACA;EACI;EACA;EACA;;AAMZ;EACI;EACA;;AACA;EAHJ;IAIQ;IACA;IACA;;EACA;IACI;IACA;;EAEJ;IACI;;;AAGR;EAfJ;IAgBQ;;EACA;IACI;;EAGA;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;IACA;;EACA;IACI;;EAGR;IACI;;;AAIZ;EACI;;AAGR;EACI;EACA;;AAGR;EACI;EACA;EACA;EACA;;;AAKR;AAAA;AAAA;AAGA;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EACI;EACA;;AACA;EAHJ;IAIQ;;;AAGR;EACI;;AACA;EAFJ;IAGQ;;;AAGR;EACI;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EAPJ;IAQQ;IACA;;;AAEJ;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;AAEJ;EAPJ;IAQQ;;;AAGR;EACI;EACA;EACA;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EAVJ;IAWQ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;;AAEJ;EAhBJ;IAiBQ;;;AAKJ;EAFJ;IAGQ;IACA;;;AAIJ;EACI;EACA;EACA;;AAEI;EACI;EACA;EACA;EACA;;AACA;EACI;EACA;;AAEJ;EACI;EACA;;AAKR;EACI;;AAKhB;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EATJ;IAUQ;IACA;;;AAEJ;EACI;EACA;EACA;;AACA;EACI;EACA;;AAIJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGR;EACI;;AACA;EACI;EACA;;AAIZ;EACI;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EAZJ;IAaQ;;;AAEJ;EAfJ;IAgBQ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;;AACA;EANJ;IAOQ;;;AAEJ;EATJ;IAUQ;IACA;IACA;IACA;;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EAPJ;IAQQ;IACA;IACA;;;AAGR;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;IACA;;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;AAGR;EACI;;AAGI;EACI;EACA;EACA;EACA;;AACA;EACI;;AAMpB;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EAPJ;IAQQ;IACA;IACA;IACA;;;AAGJ;EACI;EACA;EACA;;AAEJ;EACI;EACA;EACA;;AAEI;EACI;;AAEJ;EACI;EACA;EACA;;AACA;EACI;EACA;;AAGA;EACI;;AAMpB;EACI;EACA;EACA;;AAMhB;EACI;;AAEI;EADJ;IAEQ;;;AAEJ;EACI;;AAEJ;EACI;EACA;;AACA;EACI;;;AAQpB;AAAA;AAAA;AAKQ;EADJ;IAEQ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;EACA;IACI;IACA;IACA;IACA;;EACA;IACI;IACA;IACA;IACA;;EACA;IACI;IACA;IACA;IACA;IACA;;EACA;IACG;;EAIH;IACI;;EACA;IACI;IACA;IACA;;EAGR;IACI;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;EAEI;IACI;;EACA;IACI;;EAEJ;IACI;IACA;;;AAYhC;EACI;EACA;EACA;;AAEI;EACI;EACA;;AACA;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;;AAQxB;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EAXJ;IAYQ;;;AAEJ;EACI;EACA;;;AAIR;EACI;;AACA;EACI;;AAEJ;EALJ;IAMQ;;;;AAIR;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EALJ;IAMQ;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EAVJ;IAWQ;;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EAVJ;IAWQ;IACA;;;AAGR;EACI;;AACA;EACI;;;AAOZ;EACI;;;ACp2CR;AAAA;AAAA;AAII;EADJ;IAEQ;;;;AAGR;EAEI;EACA;EACA;EACA;EACA;;AACA;EAPJ;IAQQ;;;AAEJ;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;;AACA;EACI;EACA;;AAGR;EACI;;AACA;EACI;EACA;;AAKhB;EACI;;AACA;EACI;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAKA;EACI;EACA;;AAIZ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;;AAGJ;EACI;;AACA;EACI;;AAOhB;EACI;EACA;EACA;EACA;EACA;;;AlCtBhB;AAAA;AAAA;AmC9GA;AAAA;AAAA;AAGA;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EAPJ;IAQQ;IACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;AAQA;AAMA;AAOA;AAOA;;AA3BA;EARJ;IASQ;IACA;;;AAEJ;EAZJ;IAaQ;;;AAGJ;EACI;EACA;;AAIJ;EACI;EACA;EACA;;AAIJ;EACI;EACA;EACA;;AAIJ;EACI;;AAIR;EACI;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;AAGR;EACI;EACA;EACA;EACA;EACA;;AAEA;EACI;EACA;;AAIZ;EACI;EACA;;AAKJ;EACI;EACA;EACA;EACA;EACA;;AACA;EANJ;IAOQ;;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AACA;EAHJ;IAIQ;;;AAEJ;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;AAEJ;EACI;EACA;EACA;EACA;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EAZJ;IAaQ;IACA;IACA;;;AAEJ;EACI;EACA;;AAIZ;EACI;EACA;EACA;;AACA;EAJJ;IAKO;;;AAGP;EACI;EACA;EACA;;AACA;EACI;;AAEJ;EACI;EACA;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EATJ;IAUQ;IACA;;;AAEJ;EAbJ;IAcQ;;;AAGR;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;IACA;;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EARJ;IASQ;IACA;IACA;IACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;AAAA;EAEE;EACA;;AAEF;EACE;;AAIV;EACI;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;;AAGR;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;AAEJ;EACI;;AAEI;EACI;;AAGR;EAPJ;IAQQ;IACA;;;;AAMpB;EACI;;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;ACjSJ;AAAA;AAAA;AAGA;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EAVJ;IAWO;IACA;;;;AAMX;AAEA;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EACI;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGR;EACI;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;EACA;;AAIZ;EACI;;AAEJ;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;;AACA;EACI;;AAEJ;EACI;EACA;;AAGR;EACI;;AAEJ;EACI;;AAEJ;EACI;EACA;EACA;EACA;EACA;;AACA;EACI;;AAGR;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;AAGA;EACI;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;;AAMpB;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AAGJ;EACI;EACA;EACA;EAEA;;AACA;EACI;;AAGI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;;AAGA;EACI;;AAEJ;EACI;;AAMZ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;EACA;;AACA;EACI;;AAUZ;EACI;;AAEJ;EACI;EACA;;AAIZ;EACI;EACA;EACA;EACA;;AAGR;EACI;;AAEI;AAAA;EAEI;EACA;;AAEJ;EACI;;AAEJ;EACI;;AAIR;EACI;EACA;EACA;EACA;EACA;;AACA;EANJ;IAOQ;IACA;;;AAEJ;EAVJ;IAWQ;IACA;;;AAGR;EACI;EACA;EACA;;AAGR;EACI;;AAEI;EACI;;AAEJ;EACI;;AAIZ;EACI;EACA;;AAEI;EACI;;AACA;EACI;;AAIZ;EACI;EACA;EACA;;AACA;EACI;;AAIZ;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;;;AAGA;EACI;;AACA;EACI;EACA;;AAIZ;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEI;EACI;EACA;;AAIZ;EACI;;AAEJ;EACI;EACA;EACA;;AACA;EACI;;AAGQ;EACI;EACA;EACA;EACA;;AACA;EACI;;AAEJ;EACI;EACA;EACA;;AAS5B;EACI;EACA;;AAEI;EACI;;AAMZ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;;AAIZ;EACI;EACA;;AAEJ;EACI;EACA;;AAGA;EACI;;AAMR;EACI;EACA;;AAKA;EACI;;AAOR;EACI;;AAGR;EACI;EACA;;AACA;EACI;EACA;;AAEJ;EACI;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;;AAEJ;EACI;;AAIZ;EACI;EACA;;AAEI;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;;AAKhB;EACI;;AAEJ;EACI;;AAEI;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;;AAIJ;EACI;;AACA;EACI;EACA;;AAMpB;EACI;EACA;;AACA;EACI;;AACA;EACI;EACA;EACA;EACA;EACA;;AAGR;EACI;;AACA;EACI;;AAGR;EACI;EACA;;AAIJ;EACI;;AAIZ;EACI;EACA;EACA;EACA;EACA;;AACA;EANJ;IAOQ;IACA;;;AAEJ;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EACI;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAIZ;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;AAEJ;EACI;EACA;EACA;;AAEJ;EACI;;AAEJ;EACI;EACA;;AACA;EACI;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;;AAQA;EACI;;;AAQxB;EAEI;EACA;EACA;EACA;;AACA;EANJ;IAOQ;;;AAEJ;EACI;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;;AAKA;EACI;;AAIJ;EACI;;AAMJ;EACI;;AAIJ;EACI;;AAMJ;EACI;;AAIJ;EACI;;AAMJ;EACI;;AAIJ;EACI;;AAMJ;EACI;;AAIJ;EACI;;AAMJ;EACI;;AAIJ;EACI;;AAMJ;EACI;;AAIJ;EACI;;AAMJ;EACI;;AAIJ;EACI;;AAMJ;EACI;;AAIJ;EACI;;AAMJ;EACI;;AAIJ;EACI;;AAMJ;EACI;;AAIJ;EACI;;;AAOpB;EACI;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EAhBJ;IAiBQ;;;AAEJ;EACI;;AAEJ;EACI;;;AAKZ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EARJ;IASO;;;AAEH;EAXJ;IAYQ;IACA;;;AAEJ;EACI;;AACA;EAFJ;IAGQ;IACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;;AAEJ;EACI;EACA;;AAGR;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;IACA;;;AAEJ;EACI;EACA;EACA;;AACA;EACK;;AAGT;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;;;AAEJ;EACI;EACA;EACA;;AACA;EACI;EACA;;AAGR;EACI;;AAGR;EACI;EACA;EACA;;AAEI;EACI;EACA;EACA;;AAIZ;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;IACA;IACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EAbJ;IAcQ;IACA;;;AAEJ;EACI;EACA;EACA;;AAKhB;EACI;EACA;;AAEI;EACI;;AAIZ;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;;;AAEJ;EACI;;AAGR;EACI;;AAEJ;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;;AAIJ;EACG;;;AAMf;EACI;EACA;EACA;;;AAKA;EACI;;AAEJ;EACI;;;AAIR;AAEA;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;;AAEJ;EACI;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EAPJ;IAQQ;IACA;;;AAEJ;EACI;;AAEJ;EACI;EACA;;AAGR;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;AAGR;EACI;;AAEJ;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;;;AAEJ;EACI;EACA;;AAKA;EACI;;;AAOpB;EACI;;;AAGJ;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMO;;;;AAIP;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;;AAEJ;EAtBJ;IAuBQ;;;AAEJ;EACI;;;AAIR;EACI;EACA;;AACA;EAHJ;IAIQ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;AAGJ;EACI;EACA;;AAMQ;EACI;;AAEJ;EACI;;AAMhB;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAKJ;EACI;;AACA;EACI;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAMA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AASpB;EACI;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;;AAEJ;EACI;EACA;EACA;EACA;;AACA;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAOZ;EACI;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGA;EACI;EACA;EACA;;AAMZ;EACI;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGA;EACI;;AAIZ;AAAA;EAEI;EACA;;AAGR;EACI;EACA;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGJ;EACI;;;AAIR;EACI;;AAEI;EADJ;IAEO;;;AAIH;EADJ;IAEO;;;;AAOP;EACI;EACA;;AACA;EAHJ;IAIO;;;AAEH;EACI;;AACA;EAFJ;IAGQ;;;AAOA;EADJ;IAEQ;;;;AAQpB;EAEQ;IACI;IACA;IACA;;EAEI;IACI;;EAEJ;IACI;;;AASpB;EACI;EACA;EACA;;AACA;EAJJ;IAKM;IACA;IACA;;;AAGE;EADJ;IAEO;IACA;;;AAEH;EACI;;;AAMZ;EACI;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGA;EACI;EACA;;AAGR;EACI;;AACA;EACI;EACA;EACA;;;AAOhB;EACI;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;;AAEJ;EACI;;AACA;EACI;EACA;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;;AAGR;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;;AAKhB;EACI;EACA;;AACA;EACI;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGR;EACI;;AACA;EACI;EACA;;AACA;EAHJ;IAIQ;IACA;IACA;;;AAKR;EACI;;AACA;EACI;;AAEJ;EACI;;AACA;EACI;;AAGR;EACI;EACA;;AACA;EACI;;AACA;EACI;EACA;;;AASxB;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;;AACA;EACI;;AACA;EACI;;AAIJ;EACI;EACA;EACA;;AAEJ;EACI;EACA;EACA;;AAIZ;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;ACrqDZ;AAAA;AAAA;AAKQ;EACI;EACA;;AACA;EAHJ;IAIQ;;;AAEJ;EACI;EACA;;AACA;EACI;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AAOZ;EACI;EACA;;AAMI;EACI;;;AASZ;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;;AAGA;EACI;EACA;EACA;EACA;;AACA;EACI;;AAKhB;EACI;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EAPJ;IAQQ;;;AAEJ;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;AAEJ;EACI;EACA;;AACA;EAHJ;IAIQ;;;AAGR;EACI;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAIZ;EAEI;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;;AAIZ;EACI;;AAEJ;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;IACA;;;AAIR;EACI;;AACA;EACI;;AAEI;EACI;EACA;;AAKhB;EACI;EACA;;AAKY;EACI;EACA;EACA;EACA;;AAMpB;EACI;;AACA;EACI;;AAKZ;EACI;;AAGQ;EACI;;AAGR;EACI;EACA;;AACA;EAHJ;IAIQ;;;AAEJ;EACI;;AAEJ;EACI;EACA;;AAKR;EACI;;AAGR;EACI;EACA;;AACA;EAHJ;IAIQ;;;AAGR;EACI;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGR;EACI;EACA;EACA;EACA;;AAEJ;AAAA;EAEI;EACA;;AACA;AAAA;EACI;EACA;EACA;;AAGA;AAAA;EACI;;AAKR;EACI;EACA;EACA;;AAGR;EACI;;AACA;EAFJ;IAGQ;IACA;;;AAEJ;EACI;EACA;;AAMJ;EACI;;AACA;EACI;;AAGR;EACI;;AAIJ;EADJ;IAEQ;;;AAGA;EACI;;;AAQhB;EADJ;IAEQ;;EACA;IACI;;;AAGR;EACI;;AACA;EACI;;AACA;EACI;EACA;EACA;;AAEI;EACI;;AAKhB;EACI;EACA;;AACA;EAHJ;IAIQ;;;AAEJ;EANJ;IAOQ;;;;AAMhB;EACI;EACA;;AACA;EACI;;AAEI;EADJ;IAEQ;;;;AAMhB;EACI;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;;AAGR;AAAA;EAEE;EACA;;AAEF;EACE;;;AAKF;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;;;AAKZ;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;IACA;;;;AAKJ;EADJ;IAEQ;;EACA;IACI;;EACA;IACI;;;AAIZ;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;;AAIA;EACI;;;AAQR;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;IACA;;;;AAUA;EACI;;;AAOpB;EACI;;AACA;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAKR;EACI;EACA;;;AAMJ;EACI;;AACA;EAFJ;IAGQ;;;AAGR;EACI;;AACA;EAFJ;IAGQ;IACA;;;AAEJ;EANJ;IAOQ;IACA;IACA;;;AAGJ;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EAEI;;AACA;EACI;EACA;EACA;;AAGR;EA3BJ;IA4BQ;;;AAEJ;EA9BJ;IA+BQ;;;AAKhB;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;AAEH;EACG;;AACA;EAFH;IAGO;;;AAEH;EACG;;AACA;EAFH;IAGO;;;AAEH;EACG;EACA;EACA;EACA;EACA;;AACA;EANH;IAOO;;;AAEJ;EATH;IAUO;IACA;;;AAEJ;EAbH;IAcO;IACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EAZJ;IAaQ;;;AAGR;EACI;EACA;;AACA;EAHJ;IAIQ;;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;;AAMpB;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;AAGR;EACI;EACA;;AACA;EACI;EACA;;AAIZ;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EACI;EACA;EACA;EACA;;AACA;EACI;EACA;;AACA;EACI;EACA;;AACA;EACI;EACA;EACA;;AACA;EACI;;AAMZ;EACI;;AAEJ;EACI;;AACA;EACI;;AAMhB;EACI;;;AAMR;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGJ;EACI;;AAEJ;EACI;;AAGR;EACI;;AACA;EACI;EACA;;AAEJ;EACI;;;AASR;EADJ;IAEQ;;;;AAMR;EACI;;;AAIR;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;;AACA;EANJ;IAOQ;;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;;;AAKZ;EACI;;AACA;EACI;;;AAIR;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;;;AAEJ;EACI;;AAGQ;EACI;;AAEJ;AAAA;EAEI;EACA;EACA;EACA;EACA;;AACA;EAPJ;AAAA;IAQQ;IACA;;;AAGR;EACI;EACA;EACA;;;AAOpB;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EALJ;IAMM;;;AAKM;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;;;AAEJ;AAAA;EAEI;;AACA;AAAA;EACI;;AACA;AAAA;EACI;;AACA;AAAA;EACI;;AAKhB;EACI;;AACA;EAFJ;IAGQ;IACA;IACA;;;;AAUhB;AAAA;EAEI;;AAEJ;EACI;;AAEJ;EACI;EACA;EACA;EACA;;;AAMR;EACI;;;AAIR;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EALJ;IAMQ;;;AAGJ;EACI;;;AAMR;AAEI;EACI;;AAEJ;EACI;;AAEJ;EACI;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;;AACA;EACI;;AAKhB;EACI;;;AAIR;EACI;;;AAEJ;EACI;;;AASQ;EACI;EACA;EACA;;AAEJ;EACI;EACA;EACA;;AACA;EACI;;AAEJ;EACI;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;;AAGR;EACI;;AACA;EACI;;AAGR;EACI;EACA;;AACA;EACI;EACA;;AAIJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;;AAKR;EADJ;IAEQ;;;AAGA;EACI;EACA;EACA;;AACA;EACI;EACA;;AAKA;EACI;EACA;;AAKR;EACI;;AAIJ;EACI;;AAIJ;EACI;;AAIJ;EACI;;AAKhB;EACI;;AACA;EACI;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;;AAMR;EACI;EACA;;AACA;EAHJ;IAIQ;;;AAEJ;EANJ;IAOQ;;;AAEJ;EACI;;AACD;EACC;;AACA;EACI;;AAQxB;EACI;;AAEI;EACI;;AAIZ;EACI;;AACA;EACI;EACA;;AACA;EACI;EACA;EACA;;AAIJ;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;;AAEJ;EACI;EACA;;;AC5pCpB;AAAA;AAAA;AAGA;EACC;EACA;;AACA;EACC;EACA;EACA;;AAED;EACC;EACA;EACA;EACA;;AACA;EACC;;;AAMH;EACC;EACA;;AACA;EAHD;IAIE;;;AAGA;EACC;;AACA;EAFD;IAGE;;;AAIH;EACC;;AACA;EAFD;IAGE;;;AAED;EACC;EACA;EACA;EACA;EACA;;AACA;EAND;IAOE;IACA;;;AAED;EACC;;AAED;EACC;;AAIH;EACC;;AAEC;EADD;IAEE;IACA;IACA;IACA;IACA;;EACA;IACC;;;AAGF;EAXD;IAYE;;;AAGF;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EATD;IAUE;IACA;;;AAED;EAbD;IAcE;IACA;IACA;IACA;IACA;;EACA;IACC;IACA;IACA;IACA;IACA;;;AAGF;EACC;;AACA;EAFD;IAGE;IACA;IACA;IACA;IACA;;;AAED;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EAZD;IAaE;IACA;IACA;IACA;;;AAED;EACC;;AAGF;EACC;;AAGF;EACC;EACA;;AACA;EAHD;IAIE;IACA;IACA;IACA;IACA;IACA;;;AAED;EAXD;IAYE;;;AAED;EACC;EACA;;AACA;EACC;EACA;EACG;EACH;;AAGF;EACC;;AAGF;EACC;EACA;;AACA;EAHD;IAIE;IACA;IACA;;;AAED;EACC;;AAED;EACC;;AAID;EACC;EACA;EACA;;AACA;EACC;;AAGF;EACC;;AAQF;EACC;;AAED;EACC;;AACA;EAFD;IAGE;;;AAIH;EACC;;AACA;EAFD;IAGE;;;AAED;EACC;EACA;;AACA;EAHD;IAIE;;;AAIF;EACC;EACA;EACA;;AACA;EAJD;IAKE;;;AAED;EAPD;IAQE;;;AAID;EADD;IAEE;;;AAED;EAJD;IAKE;;;;AAQJ;EACC;EACA;EACA;;AACA;EAJD;IAKE;;;AAGF;EACC;;AACA;EACC;EACA;EACA;EACA;;AACA;EALD;IAME;;;AAGF;EACC;;AAED;EACC;EACA;EACA;EACA;;AACA;EACC;;AAIH;EACC;;AACA;EAFD;IAGE;IACA;;;AAED;EACC;EACA;;AACA;EACC;;;AAMJ;EACC;EACA;EACA;;AACA;EAJD;IAKE;;;AAED;EACC;;AACA;EAFD;IAGE;;;AAID;EACC;;AACA;EACC;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EARD;IASE;;;AAMF;EACC;;AACA;EACC;;AAED;EACC;EACA;;AACA;EACC;EACA;;AAED;EACC;EACA;;AAKJ;EACC;EACA;EACA;;AAID;EACC;EACA;EACA;EACA;;AACA;EALD;IAME;IACA;;;AAID;EACC;EACA;EACA;EACA;;AAED;EACC;;AACA;EAFD;IAGE;;;AAGD;EAND;IAOE;;;AAID;EACC;;AAID;EACC;;AAKA;EACC;EACA;;AAKF;EACC;EACA;EACA;EACA;;AACA;EACC;;AAGF;EACC;;AAED;EACC;;AACA;EACC;EACA;;AAKF;EACC;EACA;EACA;;AAKF;EACC;EACA;EACA;;AACA;EACC;;AACA;EACC;EACA;;AACA;EAHD;IAIE;;;AAED;EACC;;AAED;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGF;EACC;;AAGF;EACC;;AAKJ;EACC;EACA;;AAEC;EACC;;;AAOH;EACC;;AAIC;EACC;;AAGF;EACC;EACA;EACA;;AAED;EACC;EACA;EACA;;AAED;EACC;;AAED;EACC;;AACA;EACC;EACA;EACA;EACA;;AAEA;EACC;EACA;EACA;EACA;;AAIH;EACC;;AAEC;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EAPA;IAQC;;;AAED;EAVA;IAWC;;;AAED;EACC;EACA;EACA;;AAED;EACC;EACA;EACA;;AAGF;EACC;;AAID;EACC;EACA;EACA;;;AAQJ;EACC;;AACA;EACC;;AAED;EACC;EACA;EACA;;AACA;EACC;;AAED;EACC;EACA;;AACA;EACC;EACA;EACA;;AAED;EACC;;AAIH;EACC;EACA;;AAED;EACC;EACA;EACA;;AACA;EACC;EACA;;AAED;EACC;EACA;EACA;EACA;EACA;EACA;;AAGD;EACC;EACA;EACA;;AACA;EACC;;AAIH;EACC;EACA;EACA;;AACA;EACC;;AAED;EACC;;AAED;EACC;EACA;;;AChnBH;AAAA;AAAA;AAGA;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGD;EACC;EACA;EACA;EACA;EACA;EACA;;AACA;EAPD;IAQE;;;AAED;EAVD;IAWE;IACA;;;AAED;EACC;;AACA;EAFD;IAGE;;;AAGF;EACC;EACA;EACA;;AACA;EAJD;IAKE;IACA;;;AAED;EACC;EACA;EACA;EACA;;AAED;EACC;;AACG;EAFJ;IAGK;;;AAED;EALJ;IAMK;IACA;;;AAIN;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;EACC;EACA;EACA;;AACA;EACC;;;AAMJ;EACC;EACA;EACA;;AACA;EAJD;IAKE;;;AAED;EAPD;IAQE;;;;AAIF;EACC;EACA;EACA;EACA;EACA;;AACA;EAND;IAOE;IACA;IACA;;;AAED;EAXD;IAYE;IACA;;;;AAIF;EACC;EACA;EACA;;AACA;EACC;;AAED;EACC;;AAED;EACC;;AAED;EACC;;AACA;EACC;;AAGF;EACC;EACA;EACA;EACA;;AAED;EACC;;AAED;EACC;EACA;EACA;;AACA;EACC;;;AAMF;EACC;;AACA;EACC;;AAED;EACC;;AACA;EACC;;AAGF;EACC;EACA;EACA;;AAIF;EACC;;AACA;EAFD;IAGE;;;;AAKH;EACC;EACA;EACA;;AACA;EAJD;IAKE;;;AAED;EAPD;IAQE;;;AAED;EACC;;AAED;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EAXD;IAYE;;;AAED;EACC;EACA;EACA;EACA;;AACA;EALD;IAME;;;AAGF;EAEC;EACA;;;AAMF;EACC;EACA;EACA;;AACA;EACC;;AAED;EACC;EACA;;AACA;EACC;;AAIH;EACC;;;AAKD;EACC;;AACA;EACC;;AACA;EACC;EACA;EACA;EACA;EACA;EACA;;AACA;EACC;;AAED;EACC;EACA;EACA;;AAED;EAfD;IAgBE;;;AAIH;EACC;;AAEC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACC;;AAGF;EACC;;AAED;EACC;;AAGA;EACC;;AAIH;EACC;EACA;EACA;EACA;;AACA;EACC;;AAED;EACC;;AAED;EACC;EACA;EACA;;;AAQJ;EACC;EACA;;AAED;EACC;EACA;EACA;;AAED;EACC;EACA;;AACA;EAHD;IAIE;;;AAGF;EACC;EACA;EACA;;AACA;EACC;;AAKD;EACC;EACA;EACA;EACA;;;AAOD;EACC;EACA;EACA;EACA;;AAGA;EACC;EACA;;;AvChPJ;AAAA;AAAA;AwCxHA;AAAA;AAAA;AAII;EACI;EACA;;AACA;EACI;EACA;EACA;;;AAMZ;AAGI;EACI;;AACA;EACI;EACA;;AACA;EACI;EACA;;AAKR;EACI;EACA;;AAKJ;EACI;;AAGA;EACI;;AAKR;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;;;AAKR;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;;;AAII;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;;;AAEJ;EARJ;IASQ;;;AAWpB;EACI;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;;AAEJ;EAfJ;IAgBQ;IACA;IACA;;;AAOpB;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;;;AAEJ;EACI;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;;AACA;EANJ;IAOQ;IACA;IACA;;;AAEJ;EAXJ;IAYQ;IACA;;;AAEJ;EACI;EACA;;AACA;EACI;EACA;EACA;EACA;;AAIZ;EACI;;AACA;EACI;EACA;;AACA;EAHJ;IAIQ;;;AAEJ;EACI;EACA;EACA;EACA;EACA;;AAGR;EACI;;AAKA;EACI;;;AAOpB;AAAA;AAAA;AAIA;EACI;EACA;;AACA;EACI;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;;AAIJ;EACI;EACA;;AAQZ;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGA;EACI;;;AAMhB;AAAA;AAAA;AAGA;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;;AACA;EACI;;AAEJ;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAKR;EACI;;AAEJ;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;;AACA;EACI;EACA;;AAEJ;EACI;;AAOR;EACI;;;ACnVhB;AAAA;AAAA;AAII;EACI;;AACA;EAFJ;IAGQ;;;AAKA;EACI;;AAMJ;EACI;EACA;;;AAMhB;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EACI;EACA;;;AAKJ;EACI;;AACA;EACI;;AAGR;EACI;EACA;EACA;;;AAKR;AAAA;EAEI;;;AAIA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAIR;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;;AAIR;EACI;EACA;EACA;EACA;EACA;EACA;;AAEI;EACI;;;AAKZ;AAAA;EAEI;;AACA;EAHJ;AAAA;IAIQ;;;;AAKJ;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;AAGR;EACI;EACA;;AACA;EAHJ;IAIQ;IACA;;;;AAKZ;EACI;EACA;;;AC3HJ;AAAA;AAAA;AAIA;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;;;AAGA;EADJ;IAEQ;;;;AAMZ;AAAA;AAAA;AAIA;EACI;;AACA;EACI;;AAEJ;EACI;EACA;;AAEJ;EACI;;;AAKR;AAAA;AAAA;AAOQ;EAEI;;AACA;EAHJ;IAIQ;;;AAIZ;EACI;EACA;;AACA;EACI;EACA;;AACA;EACI;EACA;EACA;;AACA;EACI;EACA;;AAIZ;EACI;EACA;;AAIY;EACI;;AAEJ;EACI;;AAMpB;EACI;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;;AAII;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAIQ;EACI;;AAEJ;EACI;;AAIZ;EACI;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAQxB;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;;ACnJJ;AAAA;AAAA;AAII;EADJ;IAEQ;;;AAGA;EACI;;;AAKZ;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;;AAEJ;EATJ;IAUQ;;;;AAKR;AAAA;AAAA;AAIA;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;;AAEA;EACI;EACA;EACA;;;AAOZ;AAAA;AAAA;AAGA;AAAA;EAEI;;AACA;AAAA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;AAAA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;AAAA;EACI;EACA;;;AAOZ;AAAA;AAAA;AAOY;EACI;EACA;EAEA;;AACA;EACI;;AAIJ;EACI;;AAGR;EACI;;AAEJ;EACI;EACA;EACA;EACA;;;AAMhB;AAAA;AAAA;AAII;EACI;EACA;;AAEI;EACI;EACA;EACA;;AAEJ;EACI;EACA;;AAGA;EACI;;AAEJ;EACI;;;AAOpB;AAAA;AAAA;AAII;EACI;EACA;EACA;;;A3CpCR;AAAA;AAAA;A4CjIA;AAAA;AAAA;AAIA;EACI;EACA;;AACA;EAHJ;IAIQ;;;AAGA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAMZ;AAAA;AAAA;AAIA;EACI;;AACA;EACI;EACA;EACA;EACA;;AAGA;EACI;;AAEI;EACI;;AAIZ;EACI;;AACA;EACI;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;;AACA;EACI;EACA;EACA;;AASpB;EACG;EACA;;AAGP;EACI;;AACA;EACI;EACA;;AACA;EACI;EACA;;AACA;EACI;;AAGR;EACI;EACA;EACA;EACA;;;AAOhB;AAAA;AAAA;AAIA;EACI;;AACA;EAEI;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EAVJ;IAWQ;;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;;AACA;EACI;EACA;EACA;;AAKhB;EACI;;AACA;EACI;;AACA;EACI;;AAKhB;EACI;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAKA;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EAPJ;IAQQ;;;AAMb;EADH;IAEQ;;;AAEL;EAJH;IAKQ;IACA;;;AAGJ;EACI;;AACA;EAFJ;IAGQ;;;AAGA;EACI;;;AASpB;EACI;;AAEI;EACI;;AAIJ;EACI;;AACA;EACI;;AAKR;EACI;;AAKQ;EACI;;AACA;EACI;;AAShB;EACI;;AAIJ;EACI;;AACA;EACI;;AAEJ;EACI;;AACA;EACI;;AAMZ;EACI;;;AASZ;EACI;;AAEI;EACI;EACA;;AAKR;EACI;;AAGA;EACI;EACA;EACA;;AACA;EACI;;AAEI;EADJ;IAEQ;;;AAIZ;EACI;;AACA;EACI;EACA;;AACA;EACI;;AAWR;EACI;;AAKR;EACI;;AACA;EACG;;AAMX;EADJ;IAEQ;IACA;IACA;;;AAEJ;EACI;EACA;EACA;;AACA;EACI;EACA;;AAEJ;EACI;;;AAShB;EACI;;AAEJ;EACI;EACA;;AACA;EACI;;AAGR;EACI;;AAEJ;EACI;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;;AAMZ;EADJ;IAEQ;;;AAEJ;EACI;EACA;;AACA;EACI;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;;AAMpB;EACI;;AAGQ;EACI;;;AAQhB;EACI;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;;AACA;EACI;;AACA;EACI;;AACA;EACI;;AAEJ;EACI;;AAEJ;EACI;EACA;EACA;;AAMpB;EACI;;AAEI;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;;AAEJ;EACI;AACA;EACA;;AAEJ;AACI;EACA;;AAEJ;AACI;EACA;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;;AAIZ;EACI;EACA;EACA;;AAEJ;EACI;EACA;EACA;;AACA;EACI;;;AAOhB;AAAA;AAAA;AAGA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EAZJ;IAaQ;IACA;IACA;IACA;;;AAEJ;EAlBJ;IAmBQ;IACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;;AAEJ;EAbJ;IAcQ;IACA;IACA;IACA;IACA;;;AAEJ;EApBJ;IAqBQ;IACA;;;AAGR;EACI;EACA;EACA;EACA;;AACA;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EACI;EACA;;AACA;EAHJ;IAIQ;;;AAIZ;EACI;;AACA;EACI;;AAKJ;EACI;EACA;EACA;EACA;;;AAMpB;EACI;EACA;EACA;EACA;;;A5CzgBJ;AAAA;AAAA;A6CvIA;AAAA;AAAA;AAIA;EACI;;;AAGJ;EACI;;AACA;EAFJ;IAGQ;;;;AAIR;EACI;;AACA;EAFJ;IAGQ;;;;AAIR;EACI;;AACA;EAFJ;IAGQ;;;;AAIR;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EALJ;IAMQ;;;;AAMJ;EAFJ;AAAA;IAGQ;;;AAEJ;EALJ;AAAA;IAMQ;;;;AAGR;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EALJ;IAMQ;;;;AAMA;EACI;EACA;;AAEA;EAJJ;IAKQ;IACA;;;AAEJ;EARJ;IASQ;IACA;;;;AAMhB;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAEJ;EACI;;;AAGJ;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EALJ;IAMQ;;;;AAKJ;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAGpB;EACI;IACI;;;AAIR;EAEQ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;;AAMZ;EAEQ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAEJ;IACI;;EA7BJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAEJ;IACI;;EA7BJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAEJ;IACI;;EA7BJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAEJ;IACI;;EA7BJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAEJ;IACI;;EA7BJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAEJ;IACI;;EA7BJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAEJ;IACI;;EA7BJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAEJ;IACI;;EA7BJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAEJ;IACI;;EA7BJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAEJ;IACI;;EA7BJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAEJ;IACI;;EA7BJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAEJ;IACI;;EA7BJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAEJ;IACI;;EA7BJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAEJ;IACI;;EA7BJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAEJ;IACI;;EA7BJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAEJ;IACI;;EA7BJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAEJ;IACI;;EA7BJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAEJ;IACI;;EA7BJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAEJ;IACI;;EA7BJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAEJ;IACI;;;AAMZ;EAEI;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAIA;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;;AAMZ;EACI;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGA;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAnCJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAnCJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAnCJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAnCJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAnCJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAnCJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAnCJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAnCJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAnCJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAnCJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAnCJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAnCJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAnCJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAnCJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAnCJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAnCJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAnCJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAnCJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAnCJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAIR;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;;AAMR;EAEQ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAtBJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAtBJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAtBJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAtBJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAtBJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAtBJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAtBJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAtBJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAtBJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAtBJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAtBJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAtBJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAtBJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAtBJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAtBJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAtBJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAtBJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAtBJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAtBJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;EAEJ;IACI;;;A7CtQZ;AAAA;AAAA;AAIA;AAAA;AAAA", "file": "../scss/style.css", "sourcesContent": ["/*-- ========================================================\r\n\r\nTemplate Name: eTrade || Multipurpose eCommerce HTML5 Template\r\nTemplate URL: http://new.axilthemes.com/demo/template/etrade/\r\nDescription: Modern and Multipurpose eCommerce HTML5 Template\r\nVersion: 1.1\r\nAuthor: Axilthemes\r\nAuthor URL: https://www.axilthemes.com/\r\nDate Created: September 01, 2022\r\n\r\n======================================================== --*/\r\n\r\n/*-- ========================================================\r\n\t\r\nSTYLESHEET INDEXING\r\n|\r\n|___ Default Styles\r\n|\t|\r\n|\t|___ Variables\r\n|\t|___ Reset Styels\r\n|\t|___ Typography\r\n|\t|___ Extend\r\n|\t|___ Animations\r\n|\t|___ Shortcode\r\n|\t|___ Common Style \r\n|\t|___ Forms Styles \r\n|\r\n|___Elements Styles\r\n|\t|___ About Us\r\n|\t|___ Back To Top\r\n|\t|___ Breadcrumb Styles\r\n|\t|___ Button Styles\r\n|\t|___ Categories\r\n|\t|___ Contact\r\n|\t|___ Countdown\r\n|\t|___ Error\r\n|\t|___ Newsletter\r\n|\t|___ Pagination\r\n|\t|___ Poster\r\n|\t|___ Price Slider\r\n|\t|___ Privacy Policy\r\n|\t|___ Section\r\n|\t|___ Service\r\n|\t|___ Slick Style\r\n|\t|___ Slider Style\r\n|\t|___ Social Share\r\n|\t|___ Team Style\r\n|\t|___ Testimonial Style\r\n|\t|___ Video Style\r\n|\r\n|___Blocks Styles\r\n|\t|___ Header Styles\r\n|\t|___ Shop Styles\r\n|\t|___ Blog Styles\r\n|\t|___ Footer Styles\r\n|\r\n|\r\n|___ END STYLESHEET INDEXING\r\n\r\n======================================================== --*/\r\n\r\n\r\n/* ===============================\r\nDefault Styles\r\n================================== */\r\n\r\n@import 'default/variables';\r\n@import 'default/mixins';\r\n@import 'default/reset';\r\n@import 'default/typography';\r\n@import 'default/extend';\r\n@import 'default/animations';\r\n@import 'default/shortcode';\r\n@import 'default/common';\r\n@import 'default/forms';\r\n\r\n/* ===============================\r\nElements Styles\r\n================================== */\r\n\r\n@import 'elements/about';\r\n@import 'elements/back-to-top';\r\n@import 'elements/breadcrumb';\r\n@import 'elements/button';\r\n@import 'elements/categories';\r\n@import 'elements/contact';\r\n@import 'elements/countdown';\r\n@import 'elements/error';\r\n@import 'elements/newsletter';\r\n@import 'elements/pagination';\r\n@import 'elements/poster';\r\n@import 'elements/price-slider';\r\n@import 'elements/privacy-policy';\r\n@import 'elements/section';\r\n@import 'elements/service';\r\n@import 'elements/slick';\r\n@import 'elements/slider';\r\n@import 'elements/social';\r\n@import 'elements/team';\r\n@import 'elements/testimonial';\r\n@import 'elements/video';\r\n@import 'elements/splash';\r\n\r\n/* ===============================\r\nHeader Styles\r\n================================== */\r\n@import 'header/header-top';\r\n@import 'header/header';\r\n@import 'header/nav';\r\n\r\n/* ===============================\r\nShop Styles\r\n================================== */\r\n\r\n@import 'shop/header-cart';\r\n@import 'shop/shop';\r\n@import 'shop/product-details';\r\n@import 'shop/checkout';\r\n@import 'shop/my-account';\r\n\r\n/* ===============================\r\nBlog Styles\r\n================================== */\r\n\r\n@import 'blog/blog-list';\r\n@import 'blog/blog-single';\r\n@import 'blog/comment';\r\n@import 'blog/sidebar';\r\n\r\n/* ===============================\r\nFooter Styles\r\n================================== */\r\n\r\n@import 'footer/footer';\r\n\r\n/* ===============================\r\nSpacing\r\n================================== */\r\n@import 'default/spacing';\r\n\r\n/* ===============================\r\nDark Style\r\n================================== */\r\n\r\n/* ===============================\r\nRTL Style\r\n================================== */\r\n", "/*----------------------\r\nVariables \r\n-----------------------*/\r\n:root {\r\n    //Themes colors\r\n    --color-primary: #3577f0;\r\n    --light-primary: #8c71db;\r\n    --color-secondary: #ff497c;\r\n    --color-tertiary: #FAB8C4;\r\n    --color-white: #ffffff;\r\n    --color-dark: #27272E;\r\n    --color-black: #000000;\r\n    --color-light: #CBD3D9;\r\n    --color-lighter: #F6F7FB;\r\n    --color-lightest: #C4C4C4;\r\n\r\n    // Chart Color \r\n    --color-chart01: #896BA7;\r\n    --color-chart02: #BADEFF;\r\n    --color-chart03: #E76458;\r\n    // Typo Color \r\n    --color-heading: #292930; \r\n    --color-body: #777777;\r\n    // Border Color \r\n    --color-border-light: #E3E6E9;\r\n    --color-border-dark: #42424A;\r\n    // Gery Colors \r\n    --color-gray: #999FAE;\r\n    --color-midgray: #878787;\r\n    // Extra Color \r\n    --color-extra01: #999FAE;\r\n    // Notify Colors \r\n    --color-success: #3EB75E;\r\n    --color-danger: #FF0003;\r\n    --color-warning: #FF8F3C;\r\n    --color-info: #1BA2DB;\r\n    //Social icon colors\r\n    --color-facebook: #3B5997;\r\n    --color-twitter: #1BA1F2;\r\n    --color-youtube: #ED4141;\r\n    --color-linkedin: #0077B5;\r\n    --color-pinterest: #E60022;\r\n    --color-instagram: #C231A1;\r\n    --color-vimeo: #00ADEF;\r\n    --color-twitch: #6441A3;\r\n    --color-discord: #7289da;\r\n    //Border-radius\r\n    --radius: 6px;\r\n    --radius-big: 16px;\r\n    --radius-small: 6px;\r\n    --border-width: 2px;\r\n    --border-thin: 1px;\r\n    //Font weight\r\n    //primary font\r\n    --p-light: 300;\r\n    --p-regular: 400;\r\n    --p-medium: 500;\r\n    --p-semi-bold: 600;\r\n    --p-bold: 700;\r\n    --p-extra-bold: 800;\r\n    --p-black: 900;\r\n    //secondary font\r\n    --s-light: 300;\r\n    --s-regular: 400;\r\n    --s-medium: 500;\r\n    --s-bold: 700;\r\n    --s-black: 900;\r\n    //Shadows\r\n    --shadow-primary: 0px 4px 10px rgba(37, 47, 63, 0.1);\r\n    --shadow-light: 0 2px 6px 0 rgba(0, 0, 0, 0.05);\r\n    --shadow-dark: 0 16px 32px 0 rgba(0, 0, 0, 0.04);\r\n    //transition easing\r\n    --transition: 0.3s;\r\n    //Font Family\r\n    --font-primary: 'DM Sans', sans-serif;\r\n    --font-secondary: 'DM Sans', sans-serif;\r\n    --font-awesome: 'Font Awesome 6 Pro';\r\n    //Fonts Size\r\n    --font-size-b1: 16px;\r\n    --font-size-b2: 14px;\r\n    --font-size-b3: 12px;\r\n    //Line Height\r\n    --line-height-b1: 1.7;\r\n    --line-height-b2: 1.65;\r\n    --line-height-b3: 1.6;\r\n    // Heading Font \r\n    --h1: 46px;\r\n    --h2: 36px;\r\n    --h3: 28px;\r\n    --h4: 24px;\r\n    --h5: 20px; \r\n    --h6: 18px; \r\n}\r\n\r\n// Layouts Variation\r\n$smlg-device: 'only screen and (max-width: 1199px)';\r\n$extra-device: 'only screen and (min-width: 1600px) and (max-width: 1919px)';\r\n$laptop-device: 'only screen and (min-width: 1200px) and (max-width: 1599px)';\r\n$lg-layout: 'only screen and (min-width: 992px) and (max-width: 1199px)';\r\n$md-layout:'only screen and (min-width: 768px) and (max-width: 991px)';\r\n$sm-layout:'only screen and (max-width: 767px)';\r\n$large-mobile: 'only screen and (max-width: 575px)';\r\n$small-mobile: 'only screen and (max-width: 479px)';", "/*----------------------\r\nShortcode\r\n-----------------------*/\r\n/*==============================\r\n *  Utilities\r\n=================================*/\r\n\r\n@import url('https://fonts.googleapis.com/css2?family=DM+Sans:ital,wght@0,400;0,500;0,700;1,400&family=Poppins:wght@400;500;600;700&display=swap');\r\n\r\n.clearfix:before,\r\n.clearfix:after {\r\n    content: \" \";\r\n    display: table;\r\n}\r\n\r\n.clearfix:after {\r\n    clear: both;\r\n}\r\n\r\n.fix {\r\n    overflow: hidden;\r\n}\r\n\r\n.slick-initialized .slick-slide {\r\n    margin-bottom: -10px;\r\n}\r\n\r\n\r\n/*===============================\r\n    Background Color \r\n=================================*/\r\n\r\n@include config-bg-colors('bg-color-',\r\n'primary'var(--color-primary),\r\n'secondary'var(--color-secondary),\r\n'tertiary'var(--color-tertiary),\r\n'grey'#F0F2F5,\r\n'white'#FFFFFF,\r\n'dark'var(--color-dark),\r\n'lighter'var(--color-lighter));\r\n\r\n/*===========================\r\n    Background Image \r\n=============================*/\r\n\r\n.bg_image {\r\n    @extend %bgImagePosition;\r\n}\r\n\r\n@for $i from 1 through 20 {\r\n    .bg_image--#{$i} {\r\n        background-image: url(../images/bg/bg-image-#{$i}.jpg);\r\n    }\r\n}\r\n\r\n\r\n/* Height and width */\r\n\r\n.fullscreen {\r\n    min-height: 100vh;\r\n    width: 100%;\r\n}\r\n\r\n.flex-center {\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n.require {\r\n    color: var(--color-chart03);\r\n}\r\n\r\n\r\n/*===================\r\nCustom Row\r\n======================*/\r\n\r\n.container, \r\n.container-fluid, \r\n.container-lg, \r\n.container-md, \r\n.container-sm, \r\n.container-xl, \r\n.container-xxl {\r\n    padding-left: 15px;\r\n    padding-right: 15px;\r\n}\r\n\r\n.row {\r\n    margin-right: -15px;\r\n    margin-left: -15px;\r\n    &>[class*=\"col\"] {\r\n        padding-left: 15px;\r\n        padding-right: 15px;\r\n    }\r\n}\r\n\r\n\r\n.row--0 {\r\n    margin-left: -0px;\r\n    margin-right: -0px;\r\n    &>[class*=\"col\"] {\r\n        padding-left: 0px;\r\n        padding-right: 0px;\r\n    }\r\n}\r\n\r\n.row--5 {\r\n    margin-left: -5px;\r\n    margin-right: -5px;\r\n    &>[class*=\"col\"] {\r\n        padding-left: 5px;\r\n        padding-right: 5px;\r\n    }\r\n}\r\n\r\n.row--10 {\r\n    margin-left: -10px;\r\n    margin-right: -10px;\r\n    &>[class*=\"col\"] {\r\n        padding-left: 10px;\r\n        padding-right: 10px;\r\n    }\r\n}\r\n\r\n.row--20 {\r\n    margin-left: -20px;\r\n    margin-right: -20px;\r\n    // Responsive\r\n    @media #{$laptop-device} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$lg-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$md-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$sm-layout} {\r\n        margin-left: -15px !important;\r\n        margin-right: -15px !important;\r\n    }\r\n    &>[class*=\"col\"],\r\n    &>[class*=\"col-\"] {\r\n        padding-left: 20px;\r\n        padding-right: 20px;\r\n        // Responsive\r\n        @media #{$laptop-device} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n        @media #{$lg-layout} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n        @media #{$md-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n        @media #{$sm-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n    }\r\n}\r\n\r\n.row--25 {\r\n    margin-left: -25px;\r\n    margin-right: -25px;\r\n    // Responsive\r\n    @media #{$laptop-device} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$lg-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$md-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$sm-layout} {\r\n        margin-left: -15px !important;\r\n        margin-right: -15px !important;\r\n    }\r\n    &>[class*=\"col\"],\r\n    &>[class*=\"col-\"] {\r\n        padding-left: 25px;\r\n        padding-right: 25px;\r\n        // Responsive\r\n        @media #{$laptop-device} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n        @media #{$lg-layout} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n        @media #{$md-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n        @media #{$sm-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n    }\r\n}\r\n\r\n.row--30 {\r\n    margin-left: -30px;\r\n    margin-right: -30px;\r\n    // Responsive\r\n    @media #{$laptop-device} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$lg-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$md-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$sm-layout} {\r\n        margin-left: -15px !important;\r\n        margin-right: -15px !important;\r\n    }\r\n    &>[class*=\"col\"],\r\n    &>[class*=\"col-\"] {\r\n        padding-left: 30px;\r\n        padding-right: 30px;\r\n        // Responsive\r\n        @media #{$laptop-device} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n        @media #{$lg-layout} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n        @media #{$md-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n        @media #{$sm-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n    }\r\n}\r\n\r\n.row--45 {\r\n    margin-left: -45px;\r\n    margin-right: -45px;\r\n    // Responsive\r\n    @media #{$laptop-device} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$lg-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$md-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$sm-layout} {\r\n        margin-left: -15px !important;\r\n        margin-right: -15px !important;\r\n    }\r\n    &>[class*=\"col\"],\r\n    &>[class*=\"col-\"] {\r\n        padding-left: 45px;\r\n        padding-right: 45px;\r\n        // Responsive\r\n        @media #{$laptop-device} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n        @media #{$lg-layout} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n        @media #{$md-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n        @media #{$sm-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n    }\r\n}\r\n\r\n.row--40 {\r\n    margin-left: -40px;\r\n    margin-right: -40px;\r\n    // Responsive\r\n    @media #{$laptop-device} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$lg-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$md-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$sm-layout} {\r\n        margin-left: -15px !important;\r\n        margin-right: -15px !important;\r\n    }\r\n    &>[class*=\"col\"],\r\n    &>[class*=\"col-\"] {\r\n        padding-left: 40px;\r\n        padding-right: 40px;\r\n        // Responsive\r\n        @media #{$laptop-device} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n        @media #{$lg-layout} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n        @media #{$md-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n        @media #{$sm-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n    }\r\n}\r\n\r\n.row--50 {\r\n    margin-left: -50px;\r\n    margin-right: -50px;\r\n    // Responsive\r\n    @media only screen and (min-width: 1400px) and (max-width: 1599px) {\r\n        margin-left: -30px;\r\n        margin-right: -30px;\r\n    }\r\n    @media only screen and (min-width: 1200px) and (max-width: 1399px) {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$lg-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$md-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$sm-layout} {\r\n        margin-left: -15px !important;\r\n        margin-right: -15px !important;\r\n    }\r\n    &>[class*=\"col\"],\r\n    &>[class*=\"col-\"] {\r\n        padding-left: 50px;\r\n        padding-right: 50px;\r\n        // Responsive\r\n        @media only screen and (min-width: 1400px) and (max-width: 1599px) {\r\n            padding-left: 30px;\r\n            padding-right: 30px;\r\n        }\r\n        @media only screen and (min-width: 1200px) and (max-width: 1399px) {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n        @media #{$lg-layout} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n        @media #{$md-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n        @media #{$sm-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n    }\r\n}\r\n\r\n.row--60 {\r\n    margin-left: -60px;\r\n    margin-right: -60px;\r\n    // Responsive\r\n    @media #{$laptop-device} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$lg-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$md-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$sm-layout} {\r\n        margin-left: -15px !important;\r\n        margin-right: -15px !important;\r\n    }\r\n    &>[class*=\"col\"],\r\n    &>[class*=\"col-\"] {\r\n        padding-left: 60px;\r\n        padding-right: 60px;\r\n        // Responsive\r\n        @media #{$laptop-device} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n        @media #{$lg-layout} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n        @media #{$md-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n        @media #{$sm-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n/*===========================\r\n    Input Placeholder\r\n=============================*/\r\n\r\ninput:-moz-placeholder,\r\ntextarea:-moz-placeholder {\r\n    opacity: 1;\r\n    -ms-filter: \"progid:DXImageTransform.Microsoft.Alpha(Opacity=100)\";\r\n}\r\n\r\ninput::-webkit-input-placeholder,\r\ntextarea::-webkit-input-placeholder {\r\n    opacity: 1;\r\n    -ms-filter: \"progid:DXImageTransform.Microsoft.Alpha(Opacity=100)\";\r\n}\r\n\r\ninput::-moz-placeholder,\r\ntextarea::-moz-placeholder {\r\n    opacity: 1;\r\n    -ms-filter: \"progid:DXImageTransform.Microsoft.Alpha(Opacity=100)\";\r\n}\r\n\r\ninput:-ms-input-placeholder,\r\ntextarea:-ms-input-placeholder {\r\n    opacity: 1;\r\n    -ms-filter: \"progid:DXImageTransform.Microsoft.Alpha(Opacity=100)\";\r\n}\r\n\r\n\r\n/*=============================\r\n\tOverlay styles \r\n==============================*/\r\n\r\n[data-overlay],\r\n[data-black-overlay],\r\n[data-white-overlay] {\r\n    position: relative;\r\n}\r\n\r\n[data-overlay]>div,\r\n[data-overlay]>*,\r\n[data-black-overlay]>div,\r\n[data-black-overlay]>*,\r\n[data-white-overlay]>div,\r\n[data-white-overlay]>* {\r\n    position: relative;\r\n    z-index: 2;\r\n}\r\n\r\n[data-overlay]:before,\r\n[data-black-overlay]:before,\r\n[data-white-overlay]:before {\r\n    content: \"\";\r\n    position: absolute;\r\n    left: 0;\r\n    top: 0;\r\n    height: 100%;\r\n    width: 100%;\r\n    z-index: 2;\r\n}\r\n\r\n[data-overlay]:before {\r\n    background-color: var(--color-primary);\r\n}\r\n\r\n[data-black-overlay]:before {\r\n    background-color: #000000;\r\n}\r\n\r\n[data-white-overlay]:before {\r\n    background-color: #ffffff;\r\n}\r\n\r\n@for $i from 1 through 10 {\r\n    [data-overlay=\"#{$i}\"]:before,\r\n    [data-black-overlay=\"#{$i}\"]:before,\r\n    [data-white-overlay=\"#{$i}\"]:before {\r\n        opacity: #{$i * 0.10};\r\n    }\r\n}\r\n\r\n\r\n/*------------------------------\r\n    Scroll Up \r\n--------------------------------*/\r\n\r\n#scrollUp {\r\n    width: 70px;\r\n    height: 80px;\r\n    right: 100px;\r\n    bottom: 60px;\r\n    text-align: center;\r\n    z-index: 9811 !important;\r\n    text-decoration: none;\r\n    background: #fff;\r\n    line-height: 80px;\r\n    color: #757589;\r\n    font-size: 15px;\r\n    font-weight: 400;\r\n    @extend %transition;\r\n    display: inline-block;\r\n    background: #ffffff;\r\n    @media #{$md-layout} {\r\n        right: 20px;\r\n        bottom: 40px;\r\n    }\r\n    @media #{$sm-layout} {\r\n        right: 20px;\r\n        bottom: 40px;\r\n    }\r\n    &::before {\r\n        width: 100%;\r\n        height: 100%;\r\n        left: 0;\r\n        bottom: 0;\r\n        background: rgba(0, 2, 72, 0.1);\r\n        content: \"\";\r\n        position: absolute;\r\n        z-index: -1;\r\n        transform-style: preserve-3d;\r\n        transform: rotateY(-10deg);\r\n        filter: blur(50px);\r\n    }\r\n    &::after {\r\n        background: #ffffff;\r\n        position: absolute;\r\n        content: \"\";\r\n        top: 0;\r\n        bottom: 0;\r\n        left: 0;\r\n        right: 0;\r\n        width: 100%;\r\n        height: 100%;\r\n        z-index: -1;\r\n        transform-style: preserve-3d;\r\n        transform: rotateY(-10deg);\r\n    }\r\n    @media #{$sm-layout} {\r\n        right: 20px;\r\n        bottom: 30px;\r\n        width: 50px;\r\n        height: 60px;\r\n        line-height: 60px;\r\n    }\r\n    span {\r\n        &.text {\r\n            position: relative;\r\n            display: inline-block;\r\n            margin-top: 7px;\r\n            @media #{$sm-layout} {\r\n                margin-top: 3px;\r\n            }\r\n            &::after {\r\n                width: 0;\r\n                height: 0;\r\n                border-style: solid;\r\n                border-width: 0 5px 7px 5px;\r\n                border-color: transparent transparent var(--color-primary) transparent;\r\n                position: absolute;\r\n                content: \"\";\r\n                left: 50%;\r\n                top: 21%;\r\n                transform: translateX(-50%);\r\n            }\r\n        }\r\n    }\r\n    &:hover {\r\n        span {\r\n            &.text {\r\n                color: var(--color-primary);\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n/*--------------------\r\n    Contact Form \r\n----------------------*/\r\n\r\n.form-message {\r\n    margin-bottom: 0;\r\n    text-align: center;\r\n    &.error {\r\n        margin-top: 20px;\r\n        color: #f80707;\r\n    }\r\n    &.success {\r\n        margin-top: 20px;\r\n        color: #0d8d2d;\r\n    }\r\n}", "\r\n/*----------------------\r\nReset Styels\r\n-----------------------*/\r\n\r\n * {\r\n    -webkit-box-sizing: border-box;\r\n    -moz-box-sizing: border-box;\r\n    box-sizing: border-box;\r\n}\r\n\r\n\r\narticle,\r\naside,\r\ndetails,\r\nfigcaption,\r\nfigure,\r\nfooter,\r\nheader,\r\nnav,\r\nsection,\r\nsummary {\r\n    display: block;\r\n}\r\n\r\naudio,\r\ncanvas,\r\nvideo {\r\n    display: inline-block;\r\n}\r\n\r\naudio:not([controls]) {\r\n    display: none;\r\n    height: 0;\r\n}\r\n\r\n[hidden] {\r\n    display: none;\r\n}\r\n\r\n\r\na {\r\n    color: var(--color-heading);\r\n    text-decoration: none;\r\n    outline: none;\r\n}\r\n\r\n\r\na:hover,\r\na:focus,\r\na:active {\r\n\ttext-decoration: none;\r\n\toutline: none;\r\n\tcolor: var(--color-primary);\r\n}\r\n\r\na:focus {\r\n    outline: none;\r\n}\r\naddress {\r\n    margin: 0 0 24px;\r\n}\r\n\r\nabbr[title] {\r\n    border-bottom: 1px dotted;\r\n}\r\n\r\nb,\r\nstrong {\r\n    font-weight: bold;\r\n}\r\nmark {\r\n    background: var(--color-primary);\r\n    color: #ffffff;\r\n}\r\ncode,\r\nkbd,\r\npre,\r\nsamp {\r\n    font-size: var(--font-size-b3);\r\n    -webkit-hyphens: none;\r\n    -moz-hyphens: none;\r\n    -ms-hyphens: none;\r\n    hyphens: none;\r\n    color: var(--color-primary);\r\n}\r\nkbd ,\r\nins{\r\n    color: #ffffff;\r\n}\r\n\r\npre  {\r\n    font-family: \"Courier 10 Pitch\", Courier, monospace;\r\n    font-size: var(--font-size-b3);\r\n    margin: 10px 0;\r\n    overflow: auto;\r\n    padding: 20px;\r\n    white-space: pre;\r\n    white-space: pre-wrap;\r\n    word-wrap: break-word;\r\n    color: var(--color-body);\r\n    background: var(--color-lighter);\r\n}\r\n\r\n\r\nsmall {\r\n    font-size: smaller;\r\n}\r\n\r\nsub,\r\nsup {\r\n    font-size: 75%;\r\n    line-height: 0;\r\n    position: relative;\r\n    vertical-align: baseline;\r\n}\r\nsup {\r\n    top: -0.5em;\r\n}\r\nsub {\r\n    bottom: -0.25em;\r\n}\r\n\r\ndl {\r\n    margin-top: 0;\r\n    margin-bottom: 10px;\r\n}\r\n\r\ndd {\r\n    margin: 0 15px 15px;\r\n}\r\ndt {\r\n    font-weight: bold;\r\n    color: var(--color-heading);\r\n}\r\n\r\nmenu,\r\nol,\r\nul {\r\n    margin: 16px 0;\r\n    padding: 0 0 0 40px;\r\n}\r\n\r\nnav ul,\r\nnav ol {\r\n    list-style: none;\r\n    list-style-image: none;\r\n}\r\nli>ul,\r\nli>ol {\r\n    margin: 0;\r\n}\r\n\r\nol {\r\n    ul {\r\n        margin-bottom: 0;\r\n    }\r\n}\r\n\r\nimg {\r\n    -ms-interpolation-mode: bicubic;\r\n    border: 0;\r\n    vertical-align: middle;\r\n    max-width: 100%;\r\n    height: auto;\r\n}\r\n\r\nsvg:not(:root) {\r\n    overflow: hidden;\r\n}\r\nfigure {\r\n    margin: 0;\r\n}\r\nform {\r\n    margin: 0;\r\n}\r\nfieldset {\r\n    border: 1px solid var(--color-border);\r\n    margin: 0 2px;\r\n    min-width: inherit;\r\n    padding: 0.35em 0.625em 0.75em;\r\n}\r\nlegend {\r\n    border: 0;\r\n    padding: 0;\r\n    white-space: normal;\r\n}\r\n\r\nbutton,\r\ninput,\r\nselect,\r\ntextarea {\r\n    font-size: 100%;\r\n    margin: 0;\r\n    max-width: 100%;\r\n    vertical-align: baseline;\r\n}\r\n\r\nbutton,\r\ninput {\r\n    line-height: normal;\r\n}\r\n\r\nbutton,\r\nhtml input[type=\"button\"],\r\ninput[type=\"reset\"],\r\ninput[type=\"submit\"] {\r\n    -webkit-appearance: button;\r\n    -moz-appearance: button;\r\n    appearance: button;\r\n    cursor: pointer;\r\n}\r\n\r\nbutton[disabled],\r\ninput[disabled] {\r\n    cursor: default;\r\n}\r\n\r\ninput[type=\"checkbox\"],\r\ninput[type=\"radio\"] {\r\n    padding: 0;\r\n}\r\n\r\ninput[type=\"search\"] {\r\n    -webkit-appearance: textfield;\r\n    -moz-appearance: textfield;\r\n    appearance: textfield;\r\n    appearance: textfield;\r\n    padding-right: 2px;\r\n    width: 270px;\r\n    cursor: text;\r\n}\r\n\r\ninput[type=\"search\"]::-webkit-search-decoration {\r\n    -webkit-appearance: none;\r\n    appearance: none;\r\n}\r\n\r\nbutton::-moz-focus-inner,\r\ninput::-moz-focus-inner {\r\n    border: 0;\r\n    padding: 0;\r\n}\r\ntextarea {\r\n    overflow: auto;\r\n    vertical-align: top;\r\n}\r\ncaption,\r\nth,\r\ntd {\r\n    font-weight: normal;\r\n}\r\n\r\nth {\r\n    font-weight: 500;\r\n    text-transform: uppercase;\r\n}\r\ntd,\r\n.wp-block-calendar tfoot td {\r\n    border: 1px solid var(--color-border);\r\n    padding: 7px 10px;\r\n}\r\ndel {\r\n    color: #656973;\r\n}\r\nins {\r\n    background: rgba(255, 47, 47, 0.4);\r\n    text-decoration: none;\r\n}\r\nhr {\r\n    background-size: 4px 4px;\r\n    border: 0;\r\n    height: 1px;\r\n    margin: 0 0 24px;\r\n}\r\n\r\ntable a,\r\ntable a:link, \r\ntable a:visited {\r\n    text-decoration: underline;\r\n}\r\n\r\ndt {\r\n    font-weight: bold;\r\n    margin-bottom: 10px;\r\n}\r\n\r\ndd {\r\n    margin: 0 15px 15px;\r\n}\r\n\r\ncaption {\r\n    caption-side: top;\r\n}\r\n\r\nkbd {\r\n    background: var(--heading-color);\r\n}\r\n\r\ndfn,\r\ncite,\r\nem {\r\n    font-style: italic;\r\n}\r\n\r\n\r\n/* BlockQuote  */\r\nblockquote,\r\nq {\r\n    -webkit-hyphens: none;\r\n    -moz-hyphens: none;\r\n    -ms-hyphens: none;\r\n    hyphens: none;\r\n    quotes: none;\r\n}\r\n\r\nblockquote:before,\r\nblockquote:after,\r\nq:before,\r\nq:after {\r\n    content: \"\";\r\n    content: none;\r\n}\r\n\r\nblockquote {\r\n    font-size: var(--font-size-b1);\r\n    font-style: italic;\r\n    font-weight: var(--p-light);\r\n    margin: 24px 40px;\r\n}\r\n\r\nblockquote blockquote {\r\n    margin-right: 0;\r\n}\r\n\r\nblockquote cite,\r\nblockquote small {\r\n    font-size: var(--font-size-b3);\r\n    font-weight: normal;\r\n}\r\n\r\nblockquote strong,\r\nblockquote b {\r\n    font-weight: 700;\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n", "/*----------------------\r\n Typography\r\n-----------------------*/\r\n\r\n * {\r\n\tbox-sizing: border-box;\r\n}\r\n\r\nhtml {\r\n\toverflow: hidden;\r\n\toverflow-y: auto;\r\n\tmargin: 0;\r\n\tpadding: 0;\r\n\tfont-size: 10px;\r\n}\r\n@media only screen and (min-width: 992px) {\r\n\tbody.overflow-visible {\r\n\t\toverflow: visible;\r\n\t\toverflow-y: visible;\r\n\t\t\r\n\t}\r\n}\r\n\r\nbody {\r\n\tfont-size: var(--font-size-b1);;\r\n\tline-height: var(--line-height-b1);;\r\n\t-webkit-font-smoothing: antialiased;\r\n\t-moz-osx-font-smoothing: grayscale;\r\n\tfont-family: var(--font-primary);\r\n\tcolor: var(--color-body);\r\n\tfont-weight: var(--p-regular);\r\n\toverflow: hidden;\r\n\toverflow-y: auto;\r\n}\r\n\r\nh1,\r\nh2,\r\nh3,\r\nh4,\r\nh5,\r\nh6,\r\n.h1,\r\n.h2,\r\n.h3,\r\n.h4,\r\n.h5,\r\n.h6,\r\naddress,\r\np,\r\npre,\r\nblockquote,\r\nmenu,\r\nol,\r\nul,\r\ntable,\r\nhr {\r\n\tmargin: 0;\r\n\tmargin-bottom: 30px;\r\n}\r\n\r\nh1,\r\nh2,\r\nh3,\r\nh4,\r\nh5,\r\nh6,\r\n.h1,\r\n.h2,\r\n.h3,\r\n.h4,\r\n.h5,\r\n.h6 {\r\n\tword-break: break-word;\r\n\tfont-family: var(--font-secondary);\r\n\tline-height: 1.3;\r\n\tcolor: var(--color-heading);\r\n}\r\n\r\nh1,\r\n.h1 {\r\n\tfont-size: var(--h1);\r\n}\r\n\r\nh2,\r\n.h2 {\r\n\tfont-size: var(--h2);\r\n}\r\n\r\nh3,\r\n.h3 {\r\n\tfont-size: var(--h3);\r\n}\r\n\r\nh4,\r\n.h4 {\r\n\tfont-size: var(--h4);\r\n}\r\n\r\nh5,\r\n.h5 {\r\n\tfont-size: var(--h5);\r\n}\r\n\r\nh6,\r\n.h6 {\r\n\tfont-size: var(--h6);\r\n}\r\n\r\nh1,\r\nh2,\r\nh3,\r\nh4,\r\nh5,\r\nh6,\r\n.h1,\r\n.h2,\r\n.h3,\r\n.h4,\r\n.h5,\r\n.h6 {\r\n\ta {\r\n\t\tcolor: inherit;\r\n\t}\r\n}\r\n\r\n@media #{$md-layout} {\r\n\th1,\r\n\t.h1 {\r\n\t\tfont-size: 40px;\r\n\t}\r\n\th2,\r\n\t.h2 {\r\n\t\tfont-size: 30px;\r\n\t}\r\n\r\n\th3,\r\n\t.h3 {\r\n\t\tfont-size: 26px;\r\n\t}\r\n\r\n\th4,\r\n\t.h4 {\r\n\t\tfont-size: 22px;\r\n\t}\r\n}\r\n\r\n\r\n@media #{$sm-layout} {\r\n\th1,\r\n\t.h1 {\r\n\t\tfont-size: 34px;\r\n\t}\r\n\th2,\r\n\t.h2 {\r\n\t\tfont-size: 26px;\r\n\t}\r\n\r\n\th3,\r\n\t.h3 {\r\n\t\tfont-size: 24px;\r\n\t}\r\n\r\n\th4,\r\n\t.h4 {\r\n\t\tfont-size: 20px;\r\n\t}\r\n}\r\n\r\n\r\nh1,\r\n.h1,\r\nh2,\r\n.h2,\r\nh3,\r\n.h3 {\r\n\tfont-weight: var(--s-bold);\r\n}\r\n\r\nh4,\r\n.h4,\r\nh5,\r\n.h5 {\r\n\tfont-weight: var(--s-bold);\r\n}\r\n\r\nh6,\r\n.h6 {\r\n\tfont-weight: var(--s-medium);\r\n}\r\n\r\n\r\nh1,\r\nh2,\r\nh3,\r\nh4,\r\nh5,\r\nh6 {\r\n\t&.b1 {\r\n\t\tfont-size: var(--font-size-b1);\r\n\t\tline-height: var(--line-height-b1);\r\n\t}\r\n\t\r\n\t&.b2 {\r\n\t\tfont-size: var(--font-size-b2);\r\n\t\tline-height: var(--line-height-b2);\r\n\t}\r\n\r\n\t&.b3 {\r\n\t\tfont-size: var(--font-size-b3);\r\n\t\tline-height: var(--line-height-b3);\r\n\t}\r\n}\r\n\r\np {\r\n\tfont-size: var(--font-size-b1);\r\n\tline-height: var(--line-height-b1);\r\n\tfont-weight: var(--p-regular);\r\n\tcolor: var(--color-body);\r\n\tmargin: 0 0 30px;\r\n\t&.has-large-font-size {\r\n\t\tline-height: 1.5;\r\n\t\tfont-size: 36px;\r\n\t}\r\n\t&.has-medium-font-size {\r\n\t\tfont-size: 24px;\r\n\t\tline-height: 36px;\r\n\t}\r\n\r\n\t&.has-small-font-size {\r\n\t\tfont-size: 13px;\r\n\t}\r\n\r\n\t&.has-very-light-gray-color {\r\n\t\tcolor: var(--color-white);\r\n\t}\r\n\r\n\t&.has-background {\r\n\t\tpadding: 20px 30px;\r\n\t}\r\n\r\n\t&.b1 {\r\n\t\tfont-size: var(--font-size-b1);\r\n\t\tline-height: var(--line-height-b1);\r\n\t}\r\n\t\r\n\t&.b2 {\r\n\t\tfont-size: var(--font-size-b2);\r\n\t\tline-height: var(--line-height-b2);\r\n\t}\r\n\r\n\t&.b3 {\r\n\t\tfont-size: var(--font-size-b3);\r\n\t\tline-height: var(--line-height-b3);\r\n\t}\r\n\r\n\t&:last-child {\r\n\t\tmargin-bottom: 0;\r\n\t}\r\n}\r\n\r\n.b1 {\r\n\tfont-size: var(--font-size-b1);\r\n\tline-height: var(--line-height-b1);\r\n}\r\n\r\n.b2 {\r\n\tfont-size: var(--font-size-b2);\r\n\tline-height: var(--line-height-b2);\r\n}\r\n\r\n.b3 {\r\n\tfont-size: var(--font-size-b3);\r\n\tline-height: var(--line-height-b3);\r\n}\r\n\r\n\r\ntable {\r\n\tborder-collapse: collapse;\r\n\tborder-spacing: 0;\r\n\tmargin: 0 0 20px;\r\n\twidth: 100%;\r\n}\r\n\r\ntable a,\r\ntable a:link,\r\ntable a:visited {\r\n\ttext-decoration: none;\r\n}\r\n\r\ncite,\r\n.wp-block-pullquote cite,\r\n.wp-block-pullquote.is-style-solid-color blockquote cite,\r\n.wp-block-quote cite {\r\n\tcolor: var(--heading-color);\r\n}\r\n\r\nvar {\r\n\tfont-family: \"Courier 10 Pitch\", Courier, monospace;\r\n}\r\n\r\n/*---------------------------\r\n\tList Style \r\n---------------------------*/\r\nul,\r\nol {\r\n\tpadding-left: 18px;\r\n}\r\n\r\nul {\r\n    list-style: square;\r\n\tmargin-bottom: 30px;\r\n\tpadding-left: 20px;\r\n\t&.liststyle {\r\n\t\t&.bullet {\r\n\t\t\tli {\r\n\t\t\t\tfont-size: 18px;\r\n\t\t\t\tline-height: 30px;\r\n\t\t\t\tcolor: var(--color-body);\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tpadding-left: 30px;\r\n\r\n\t\t\t\t@media #{$sm-layout} {\r\n\t\t\t\t\tpadding-left: 19px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&::before {\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tcontent: \"\";\r\n\t\t\t\t\twidth: 6px;\r\n\t\t\t\t\theight: 6px;\r\n\t\t\t\t\tborder-radius: 100%;\r\n\t\t\t\t\tbackground: var(--color-body);\r\n\t\t\t\t\tleft: 0;\r\n\t\t\t\t\ttop: 10px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&+li {\r\n\t\t\t\t\tmargin-top: 8px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\tli {\r\n\t\tfont-size: var(--font-size-b1);\r\n\t\tline-height: var(--line-height-b1);\r\n\t\tmargin-top: 8px;\r\n\t\tmargin-bottom: 8px;\r\n\t\tcolor: var(--color-body);\r\n\t\tfont-family: var(--font-secondary);\r\n\t\ta {\r\n\t\t\ttext-decoration: none;\r\n\t\t\tcolor: var(--color-heading);\r\n\t\t\t@extend %transition;\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\tcolor: var(--color-primary);\r\n\t\t\t}\r\n\t\t}\r\n\t\t&::marker {\r\n\t\t\tcolor: var(--color-body);\r\n\t\t}\r\n\t}\r\n\tul {\r\n\t\tmargin-bottom: 0;\r\n\t}\r\n}\r\n\r\nol {\r\n\tmargin-bottom: 30px;\r\n\tli {\r\n\t\tfont-size: var(--font-size-b1);\r\n\t\tline-height: var(--line-height-b1);\r\n\t\tcolor: var(--color-body);\r\n\t\tmargin-top: 8px;\r\n\t\tmargin-bottom: 8px;\r\n\r\n\t\ta {\r\n\t\t\tcolor: var(--heading-color);\r\n\t\t\t@extend %transition;\r\n\t\t\ttext-decoration: none;\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\tcolor: var(--color-primary);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\tul {\r\n\t\tpadding-left: 30px;\r\n\t}\r\n}", "/*----------------------\r\nExtend \r\n-----------------------*/\r\n// Others \r\n\r\n%box-shadow {\r\n    box-shadow: var(--shadow-primary);\r\n}\r\n\r\n%radius {\r\n    border-radius: var(--radius);\r\n}\r\n\r\n.radius {\r\n    @extend %radius;\r\n}\r\n\r\n\r\n/*=============== Style Css =============*/\r\n\r\n%liststyle {\r\n    padding: 0;\r\n    margin: 0;\r\n    list-style: none;\r\n}\r\n.liststyle {\r\n    padding: 0;\r\n    margin: 0;\r\n    list-style: none;\r\n}\r\n\r\n%transition {\r\n    transition: var(--transition);\r\n}\r\n\r\n%bgImagePosition {\r\n    background-repeat: no-repeat;\r\n    background-size: cover;\r\n    background-position: center center;\r\n}\r\n\r\n.bgImagePosition {\r\n    @extend %bgImagePosition;\r\n}", "/*----------------------\r\nAnimation  \r\n-----------------------*/\r\n.post-scale {\r\n    overflow: hidden;\r\n    @extend %radius;\r\n    img {\r\n        transition: 0.5s;\r\n    }\r\n    &:hover {\r\n        img {\r\n            transform: scale(1.1);\r\n        }\r\n    }\r\n}\r\n\r\n@keyframes signalanimation {\r\n    0% { \r\n        opacity: 1;\r\n    }\r\n    100% { \r\n        opacity: 0; \r\n    }\r\n}\r\n\r\n@keyframes customOne {\r\n    from {\r\n        transform: scale(1);\r\n    }\r\n\r\n    50% {\r\n        transform: scale(0.90);\r\n    }\r\n\r\n    to {\r\n        transform: scale(1);\r\n    }\r\n}\r\n\r\n@keyframes customTwo {\r\n    0% {\r\n        transform: (translate(0.0px, 0.0px));\r\n    }\r\n\r\n    50% {\r\n        transform: (translate(100.0px, 0.0px));\r\n    }\r\n\r\n    100% {\r\n        transform: (translate(50.0px, 50.0px));\r\n    }\r\n}\r\n\r\n.customOne {\r\n    animation: customOne 2s infinite;\r\n}\r\n\r\n@keyframes pulse {\r\n    0% {\r\n        box-shadow: 0 0 0 0 var(--color-primary);\r\n    }\r\n\r\n    70% {\r\n        box-shadow: 0 0 0 20px rgba(255, 255, 255, 0);\r\n    }\r\n\r\n    100% {\r\n        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);\r\n    }\r\n}\r\n\r\n\r\n\r\n/* ------------------------\r\n    Custom Animation 01 \r\n----------------------------*/\r\n\r\n@-webkit-keyframes headerSlideDown {\r\n    0% {\r\n        transform: translateY(-100px);\r\n    }\r\n\r\n    to {\r\n       transform: translateY(0);\r\n    }\r\n}\r\n\r\n@keyframes headerSlideDown {\r\n    0% {\r\n        transform: translateY(-100px);\r\n    }\r\n\r\n    to {\r\n       transform: translateY(0);\r\n    }\r\n}\r\n\r\n\r\n/*------------------------\r\n\tslidefadeinup\r\n--------------------------*/\r\n\r\n@-webkit-keyframes slideFadeInUp {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(0, 20%, 0);\r\n        transform: translate3d(0, 20%, 0);\r\n    }\r\n\r\n    to {\r\n        opacity: 1;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n}\r\n\r\n@keyframes slideFadeInUp {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(0, 20%, 0);\r\n        transform: translate3d(0, 20%, 0);\r\n    }\r\n\r\n    to {\r\n        opacity: 1;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n}\r\n\r\n.slideFadeInUp {\r\n    -webkit-animation-name: slideFadeInUp;\r\n    animation-name: slideFadeInUp;\r\n}\r\n\r\n/* -----------------------------------\r\n    Custom Animation For All Page\r\n---------------------------------------*/\r\n\r\n@-webkit-keyframes moveVertical {\r\n    to {\r\n        opacity: 1;\r\n        -webkit-transform: translateY(0)\r\n    }\r\n}\r\n\r\n@keyframes moveVertical {\r\n    to {\r\n        opacity: 1;\r\n        -webkit-transform: translateY(0);\r\n        transform: translateY(0)\r\n    }\r\n}\r\n\r\n\r\n/*--------------------------------\r\nScroll Down Button Animation  \r\n----------------------------------*/\r\n@keyframes scrollDown {\r\n    0% {\r\n        opacity: 0;\r\n    }\r\n\r\n    10% {\r\n        transform: translateY(0);\r\n        opacity: 1;\r\n    }\r\n\r\n    100% {\r\n        transform: translateY(10px);\r\n        opacity: 0;\r\n    }\r\n}\r\n\r\n@keyframes btnIconSlide {\r\n    0% {\r\n        transform: translateY(0);\r\n    }\r\n\r\n    40% {\r\n        transform: translateY(-5px);\r\n        opacity: 0;\r\n    }\r\n    80% {\r\n        transform: translateY(5px);\r\n        opacity: 0;\r\n    }\r\n\r\n    100% {\r\n        transform: translateY(0);\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n@keyframes prevNavSlide {\r\n    0% {\r\n        transform: translateX(0);\r\n    }\r\n\r\n    40% {\r\n        transform: translateX(-5px);\r\n        opacity: 0;\r\n    }\r\n    80% {\r\n        transform: translateX(5px);\r\n        opacity: 0;\r\n    }\r\n\r\n    100% {\r\n        transform: translateX(0);\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n@keyframes nextNavSlide {\r\n    0% {\r\n        transform: translateX(0);\r\n    }\r\n\r\n    40% {\r\n        transform: translateX(5px);\r\n        opacity: 0;\r\n    }\r\n    80% {\r\n        transform: translateX(-5px);\r\n        opacity: 0;\r\n    }\r\n\r\n    100% {\r\n        transform: translateX(0);\r\n        opacity: 1;\r\n    }\r\n}\r\n", "@mixin clearfix() {\r\n\t&::after {\r\n\t\tcontent: \"\";\r\n\t\tclear: both;\r\n\t\tdisplay: table;\r\n\t}\r\n}\r\n\r\n@mixin config-bg-colors($prefix, $bg-color-...) {\r\n\t@each $i in $bg-color- {\r\n\t\t.#{$prefix}#{nth($i, 1)} {\r\n\t\t\tbackground: nth($i, 2);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n@mixin placeholder {\r\n    &::-webkit-input-placeholder {\r\n        @content;\r\n    }\r\n    &:-moz-placeholder {\r\n        @content;\r\n    }\r\n    &::-moz-placeholder { \r\n        @content;\r\n    }\r\n    &:-ms-input-placeholder {\r\n        @content;\r\n    }\r\n}\r\n", "/*----------------------\r\nCommon Style \r\n-----------------------*/\r\n\r\n/* Theme Gradient */\r\n\r\n%axil-gradient {\r\n    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #000000 100%);\r\n}\r\n\r\n\r\n.bg-gradient-1 {\r\n    background-image: radial-gradient(134.22% 147.34% at -14.53% -24.7%, #FFFFFF 0%, #FEEBED 100%);\r\n}\r\n\r\n.bg-gradient-2 {\r\n    background: radial-gradient(77.67% 226.43% at 30.03% 4.61%, #FFFFFF 0%, #F1E6FF 100%);\r\n\r\n}\r\n\r\n.bg-gradient-3 {\r\n    background: radial-gradient(119.73% 312.23% at 62.29% -39.18%, #FFFFFF 0%, #F0E6FF 100%);\r\n}\r\n\r\n.bg-gradient-4 {\r\n   \tbackground-image: radial-gradient(115.16% 203.59% at 65.89% 10.39%, #FFFFFF 0%, #FEEBED 100%);\r\n}\r\n\r\n.bg-gradient-5 {\r\n    background-image: radial-gradient(106.12% 118.09% at 67.29% -3.46%, #FFFFFF 0%, #FEEBED 100%);\r\n}\r\n\r\n.bg-gradient-6 {\r\n    background-image: radial-gradient(53.86% 87.31% at 67.29% -3.46%, #FFFFFF 0%, #FEEBED 100%);\r\n}\r\n\r\n.bg-gradient-7 {\r\n    background-image: radial-gradient(95.25% 95.25% at 50% 4.75%, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%);\r\n}\r\n\r\n.bg-gradient-8 {\r\n    background: radial-gradient(171.05% 478.76% at 62.29% -39.18%, #FFFFFF 0%, #F0E6FF 100%);\r\n}\r\n\r\n.bg-primary {\r\n    background-color: var(--color-primary);\r\n}\r\n.bg-tertiary {\r\n    background-color: var(--color-tertiary);\r\n}\r\n\r\n.bg-tertiary-2 {\r\n    background-color: #FFECEE;\r\n}\r\n\r\n.bg-lightest {\r\n    background-color: var(--color-lightest);\r\n}\r\n.bg-lighter {\r\n    background-color: var(--color-lighter);\r\n}\r\n\r\n.bg-vista-white {\r\n    background-color: #f9f3f0;\r\n}\r\n.bg-wild-sand {\r\n    background-color: #f6f6f6;\r\n}\r\n\r\n.primary-color {\r\n    color: var(--color-primary);\r\n}\r\n\r\n.secondary-color {\r\n    color: var(--color-secondary);\r\n}\r\n\r\n.tertiary-color {\r\n    color: var(--color-tertiary);\r\n}\r\n\r\n.black-color {\r\n    color: var(--color-black);\r\n}\r\n\r\n.white-color {\r\n    color: var(--color-white);\r\n}\r\n\r\n.heading-color {\r\n    color: var(--color-heading);\r\n}\r\n\r\n.d-flex-center {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    align-items: center;\r\n}\r\n\r\n.overflow-md-visible {\r\n    @media only screen and (min-width: 992px) {\r\n        overflow: visible !important;\r\n    }\r\n}", "/*----------------------\r\nForms Styles\r\n-----------------------*/\r\n\r\ninput,\r\nbutton,\r\nselect,\r\ntextarea {\r\n    background: var(--color-lighter);\r\n    border: 1px solid var(--color-border);\r\n    transition: all 0.4s ease-out 0s;\r\n    color: var(--color-heading);\r\n    width: 100%;\r\n    &:focus,\r\n    &:active {\r\n        outline: none;\r\n        border-color: var(--color-primary);\r\n    }\r\n}\r\n\r\nbutton,\r\n[type=\"button\"],\r\n[type=\"reset\"],\r\n[type=\"submit\"] {\r\n    -webkit-appearance: button;\r\n}\r\n\r\ninput {\r\n    height: 40px;\r\n    padding: 0 15px;\r\n}\r\n\r\nselect,\r\n.select2 {\r\n    cursor: pointer;\r\n    transition: 0.3s;\r\n    height: 55px;\r\n    padding: 0 30px;\r\n    outline: none;\r\n    color: var(--color-body);\r\n    -moz-appearance: none;\r\n    -webkit-appearance: none;\r\n    appearance: none;\r\n    border: 1px solid var(--color-border-light);\r\n    border-radius: 6px;\r\n    background: url(../images/icons/arrow-icon.png) 95% center no-repeat transparent;\r\n    padding-right: 32px;\r\n    font-size: var(--font-size-b1);\r\n    line-height: var(--line-height-b1);\r\n    font-family: var(--font-secondary);\r\n}\r\n\r\ninput[type=\"text\"],\r\ninput[type=\"password\"],\r\ninput[type=\"email\"],\r\ninput[type=\"number\"],\r\ninput[type=\"tel\"],\r\ntextarea {\r\n    font-size: var(--font-size-b2);\r\n    font-weight: 400;\r\n    height: auto;\r\n    line-height: 60px;\r\n    background: #fff;\r\n    -webkit-box-shadow: none;\r\n    box-shadow: none;\r\n    padding: 0 30px;\r\n    outline: none;\r\n    border: var(--border-width) solid var(--color-border);\r\n    border-radius: var(--radius);\r\n    /* -- Placeholder -- */\r\n    &::placeholder {\r\n        color: var(--color-body);\r\n        /* Firefox */\r\n        opacity: 1;\r\n    }\r\n    &:-ms-input-placeholder {\r\n        /* Internet Explorer 10-11 */\r\n        color: var(--color-body);\r\n    }\r\n    &::-ms-input-placeholder {\r\n        /* Microsoft Edge */\r\n        color: var(--color-body);\r\n    }\r\n    &.p-holder__active {\r\n        border-color: var(--color-primary);\r\n        /* -- Placeholder -- */\r\n        &::placeholder {\r\n            color: var(--color-primary);\r\n            /* Firefox */\r\n            opacity: 1;\r\n        }\r\n        &:-ms-input-placeholder {\r\n            /* Internet Explorer 10-11 */\r\n            color: var(--color-primary);\r\n        }\r\n        &::-ms-input-placeholder {\r\n            /* Microsoft Edge */\r\n            color: var(--color-primary);\r\n        }\r\n    }\r\n    &.p-holder__error {\r\n        border-color: #f4282d;\r\n        /* -- Placeholder -- */\r\n        &::placeholder {\r\n            color: #f4282d;\r\n            /* Firefox */\r\n            opacity: 1;\r\n        }\r\n        &:-ms-input-placeholder {\r\n            /* Internet Explorer 10-11 */\r\n            color: #f4282d;\r\n        }\r\n        &::-ms-input-placeholder {\r\n            /* Microsoft Edge */\r\n            color: #f4282d;\r\n        }\r\n        &:focus {\r\n            border-color: #f4282d;\r\n        }\r\n    }\r\n    &:focus {\r\n        border-color: var(--color-primary);\r\n    }\r\n}\r\n\r\n.input-active {\r\n    @extend .p-holder__active;\r\n    input {\r\n        @extend .p-holder__active;\r\n    }\r\n}\r\n\r\n.input-error {\r\n    @extend .p-holder__error;\r\n    input {\r\n        @extend .p-holder__error;\r\n    }\r\n}\r\n\r\n// Custom Checkbox and radio button \r\ninput[type=\"checkbox\"],\r\ninput[type=\"radio\"] {\r\n    opacity: 0;\r\n    position: absolute;\r\n    ~label {\r\n        position: relative;\r\n        font-size: 16px;\r\n        line-height: 20px;\r\n        color: var(--color-body);\r\n        font-weight: 500;\r\n        padding-left: 28px;\r\n        cursor: pointer;\r\n        &::before {\r\n            content: \" \";\r\n            position: absolute;\r\n            top: 2px;\r\n            left: 0;\r\n            width: 16px;\r\n            height: 16px;\r\n            background-color: #fff;\r\n            border: var(--border-thin) solid var(--color-body);\r\n            border-radius: 2px;\r\n            transition: all .3s;\r\n        }\r\n        &::after {\r\n            content: \" \";\r\n            position: absolute;\r\n            top: 5px;\r\n            left: 2px;\r\n            width: 10px;\r\n            height: 5px;\r\n            background-color: transparent;\r\n            border-bottom: var(--border-thin) solid #fff;\r\n            border-left: var(--border-thin) solid #fff;\r\n            border-radius: 2px;\r\n            transform: rotate(-45deg);\r\n            opacity: 0;\r\n            transition: all .3s;\r\n        }\r\n    }\r\n    &:checked {\r\n        ~label {\r\n            &::before {\r\n                background-color: var(--color-primary);\r\n                border: var(--border-width) solid var(--color-primary);\r\n            }\r\n            &::after {\r\n                opacity: 1;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\ninput[type=\"radio\"] {\r\n    ~label {\r\n        &::before {\r\n            border-radius: 50%;\r\n        }\r\n        &::after {\r\n            width: 8px;\r\n            height: 8px;\r\n            left: 4px;\r\n            top: 6px;\r\n            background: #fff;\r\n            border-radius: 50%;\r\n        }\r\n    }\r\n}\r\n\r\n.form-group {\r\n    margin-bottom: 30px;\r\n    position: relative;\r\n    label {\r\n        margin-bottom: 6px;\r\n        font-size: 14px;\r\n        line-height: 22px;\r\n        font-weight: 500;\r\n        color: var(--color-body);\r\n    }\r\n    input {\r\n        border: 0 none;\r\n        border-radius: 6px;\r\n        height: 50px;\r\n        font-size: var(--font-size-b2);\r\n        @extend %transition;\r\n        padding: 0 20px;\r\n        background-color: #fff;\r\n        border: 1px solid var(--color-light);\r\n        @extend %transition;\r\n        &:focus {\r\n            border-color: var(--color-primary);\r\n            box-shadow: none;\r\n        }\r\n    }\r\n    textarea {\r\n        min-height: 160px;\r\n        border: 0 none;\r\n        border-radius: 6px;\r\n        resize: none;\r\n        padding: 15px;\r\n        font-size: var(--font-size-b2);\r\n        @extend %transition;\r\n        background-color: #fff;\r\n        border: 1px solid var(--color-light);\r\n        line-height: 1.5;\r\n        padding-left: 30px;\r\n        padding-top: 20px;\r\n        &:focus {\r\n            border-color: var(--color-primary);\r\n        }\r\n    }\r\n}\r\n\r\ninput[type=\"submit\"] {\r\n    width: auto;\r\n    padding: 0 30px;\r\n    border-radius: 6px;\r\n    display: inline-block;\r\n    font-weight: 500;\r\n    transition: 0.3s;\r\n    height: 60px;\r\n    background: var(--color-primary);\r\n    color: var(--color-white);\r\n    font-weight: var(--p-medium);\r\n    font-size: var(--font-size-b2);\r\n    line-height: var(--line-height-b3);\r\n    border: 2px solid var(--color-primary);\r\n    @extend %transition;\r\n    &:hover {\r\n        background: transparent;\r\n        color: var(--color-primary);\r\n    }\r\n}\r\n\r\n.error-msg,\r\n.success-msg {\r\n    p {\r\n        width: 100%;\r\n        margin: 20px 0 0 !important;\r\n    }\r\n}\r\n.error-msg {\r\n    p {\r\n        color: #ff0000;\r\n    }\r\n}\r\n\r\n.success-msg {\r\n    p {\r\n        color: #5956e9;\r\n    }\r\n}", "/*-------------------------\r\nAbout Us  \r\n--------------------------*/\r\n.axil-about-area {\r\n\t.about-thumbnail {\r\n\t\t@media only screen and (max-width: 991px) {\r\n\t\t\tmargin-bottom: 50px;\r\n\t\t\ttext-align: center;\r\n\t\t}\r\n\t\t@media only screen and (max-width: 767px) {\r\n\t\t\tmargin-bottom: 40px;\r\n\t\t}\r\n\t\timg {\r\n\t\t\tborder-radius: 10px;\r\n\t\t\twidth: 100%;\r\n\t\t}\r\n\t}\r\n\t.about-content {\r\n\t\t.title {\r\n\t\t\tmargin-bottom: 24px;\r\n\t\t\tline-height: 1.2;\r\n\t\t\t@media #{$smlg-device} {\r\n\t\t\t\tfont-size: 36px;\r\n\t\t\t}\r\n\t\t\t@media #{$sm-layout} {\r\n\t\t\t\tfont-size: 30px;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.text-heading {\r\n\t\t\tfont-size: 20px;\r\n\t\t\tmargin-bottom: 22px;\r\n\t\t\tdisplay: block;\r\n\t\t}\r\n\t\tp {\r\n\t\t\tfont-size: var(--font-size-b2);\r\n\t\t\tmargin-bottom: 34px;\r\n\t\t}\r\n\t\t&.content-right {\r\n\t\t\tpadding-left: 50px;\r\n\t\t\t@media #{$smlg-device} {\r\n\t\t\t\tpadding-left: 0;\r\n\t\t\t}\r\n\t\t}\r\n\t\t&.content-left {\r\n\t\t\tpadding-right: 60px;\r\n\t\t\t@media #{$smlg-device} {\r\n\t\t\t\tpadding-right: 0;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t&.about-style-2 {\r\n\t\tpadding-top: 80px;\r\n\t\t@media only screen and (max-width: 767px) {\r\n\t\t\tpadding-top: 60px;\r\n\t\t}\r\n\t\t.about-content {\r\n\t\t\t.subtitle {\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\tmargin-bottom: 5px;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t}\r\n\t\t\t.title {\r\n\t\t\t\tfont-size: 40px;\r\n\t\t\t\t@media #{$smlg-device} {\r\n\t\t\t\t\tfont-size: 34px;\r\n\t\t\t\t}\r\n\t\t\t\t@media #{$sm-layout} {\r\n\t\t\t\t\tfont-size: 30px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.axil-btn {\r\n\t\t\t\tborder-color: #efefef;\r\n\t\t\t\t&:hover {\r\n\t\t\t\t\tborder-color: var(--color-primary);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.about-info-area {\r\n\tposition: relative;\r\n\tz-index: 1;\r\n\t&:after {\r\n\t\tcontent: \"\";\r\n\t\theight: 50%;\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #f6f6f6;\r\n\t\tposition: absolute;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tz-index: -1;\r\n\t}\r\n}\r\n\r\n.about-info-box {\r\n\tbox-shadow: 0 16px 32px 0 rgba(0, 0, 0, .04);\r\n\tpadding: 40px 50px;\r\n\tborder: 1px solid var(--color-white);\r\n\tborder-radius: 5px;\r\n\tbackground-color: var(--color-white);\r\n\ttransition: var(--transition);\r\n\tmargin-bottom: 30px;\r\n\t@media #{$sm-layout} {\r\n\t\tpadding: 30px;\r\n\t}\r\n\t.thumb {\r\n\t\tmargin-bottom: 26px;\r\n\t}\r\n\t.content {\r\n\t\t.title {\r\n\t\t\tmargin-bottom: 12px;\r\n\t\t\tfont-weight: 700;\r\n\t\t}\r\n\t\tp {\r\n\t\t\tfont-size: var(--font-size-b2);\r\n\t\t}\r\n\t}\r\n\t&:hover {\r\n\t\tborder-color: var(--color-primary);\r\n\t}\r\n}\r\n\r\n.about-style-3 {\r\n\tpadding: 80px 0 0;\r\n\tmargin-bottom: -20px;\r\n\t@media #{$sm-layout} {\r\n\t\tpadding: 60px 0 0;\r\n\t}\r\n\t.section-title-wrapper {\r\n\t\tpadding-right: 0;\r\n\t\t.title {\r\n\t\t\tmargin-bottom: 10px;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.about-features {\r\n\tmargin-bottom: 50px;\r\n\t@media #{$sm-layout} {\r\n\t\tmargin-bottom: 30px;\r\n\t}\r\n\t.sl-number {\r\n\t\tfont-size: 40px;\r\n\t\tfont-weight: 700;\r\n\t\tcolor: var(--color-lightest);\r\n\t\tmargin-bottom: 10px;\r\n\t}\r\n\t.title {\r\n\t\tmargin-bottom: 10px;\r\n\t}\r\n}\r\n\r\n.about-gallery {\r\n\t.thumbnail {\r\n\t\tmargin-bottom: 20px;\r\n\t\timg {\r\n\t\t\tborder-radius: 6px;\r\n\t\t\twidth: 100%;\r\n\t\t}\r\n\t\t&.thumbnail-1 {\r\n\t\t\tmargin-top: 30px;\r\n\t\t\t@media only screen and (max-width: 991px) {\r\n\t\t\t\tmargin-top: 0;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t}\r\n\t}\r\n}", "/*-------------------------\r\n    Back To Top  \r\n--------------------------*/\r\n.back-to-top {\r\n    position: fixed;\r\n    bottom: -40px;\r\n    right: 40px;\r\n    display: block;\r\n    width: 45px;\r\n    height: 45px;\r\n    line-height: 46px;\r\n    background: var(--color-primary);\r\n    color: #fff;\r\n    text-align: center;\r\n    text-decoration: none;\r\n    border-radius: var(--radius);\r\n    opacity: 0;\r\n    transform: scale(0.3);\r\n    box-shadow: 4px 4px 10px rgba(0, 0, 0, 0.2);\r\n    z-index: 9;\r\n    transition: all .3s;\r\n    @media #{$large-mobile} {\r\n        width: 40px;\r\n        height: 40px;\r\n        line-height: 40px;\r\n    }\r\n    &:focus {\r\n    \tcolor: var(--color-white);\r\n    }\r\n    &.show {\r\n\t \tbottom: 40px;\r\n\t    right: 40px;\r\n\t    opacity: 1;\r\n\t    transform: scale(1);\r\n        @media #{$large-mobile} {\r\n            bottom: 10px;\r\n            right: 10px;\r\n        }\r\n        &:hover {\r\n        color: var(--color-white);\r\n           bottom: 45px;\r\n           opacity: 1;\r\n           @media #{$large-mobile} {\r\n               bottom: 10px;\r\n           }\r\n       }\r\n    }\r\n}\r\n\r\n", "/*-------------------------\r\n    Breadcrumb Styles  \r\n--------------------------*/\r\n.axil-breadcrumb-area {\r\n    position: relative;\r\n    background-color: #f8f8f8;\r\n    padding: 40px 0 45px;\r\n    .inner {\r\n        .title {\r\n            font-size: 40px;\r\n            margin-bottom: 0;\r\n            @media #{$md-layout} {\r\n               font-size: 34px;\r\n            }\r\n            @media #{$sm-layout} {\r\n               font-size: 28px;\r\n            }\r\n            @media #{$large-mobile} {\r\n               font-size: 24px;\r\n            }\r\n        }\r\n        .bradcrumb-thumb {\r\n            text-align: right;\r\n            position: relative;\r\n            z-index: 1;\r\n            @media #{$sm-layout} {\r\n                display: none;\r\n            }\r\n            &::after {\r\n                content: \"\";\r\n                height: 110px;\r\n                width: 110px;\r\n                background-color: var(--color-white);\r\n                border-radius: 50%;\r\n                position: absolute;\r\n                top: -10px;\r\n                right: 60px;\r\n                z-index: -1;\r\n            }\r\n        }\r\n    }\r\n}\r\n.axil-breadcrumb {\r\n    display: flex;\r\n    padding: 0;\r\n    margin: 0 0 15px;\r\n    list-style: none;\r\n    align-items: center;\r\n    li {\r\n        margin-top: 0;\r\n        margin-bottom: 0;\r\n        font-size: var(--font-size-b1);\r\n        line-height: var(--line-height-b1);\r\n        font-weight: 500;\r\n        a {\r\n            color: #999999;\r\n            display: block;\r\n        }\r\n        &.axil-breadcrumb-item {\r\n            &.active {\r\n                color: var(--color-primary);\r\n            }\r\n        }\r\n        &.separator {\r\n            height: 11px;\r\n            width: 2px;\r\n            background-color: #e5e5e5;\r\n            margin: 0 8px;\r\n        }\r\n    }\r\n}\r\n\r\n    \r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n", "/*----------------------\r\n    <PERSON><PERSON> Styles  \r\n-----------------------*/\r\n\r\na,\r\nbutton {\r\n    &.axil-btn {\r\n        border-radius: 6px;\r\n        font-size: var(--font-size-b1);\r\n        line-height: var(--line-height-b1);\r\n        font-weight: 700;\r\n        display: inline-block;\r\n        padding: 16px 38px;\r\n        position: relative;\r\n        transition: all 0.3s ease-in-out;\r\n        z-index: 1;\r\n        &:before {\r\n            content: \"\";\r\n            height: 100%;\r\n            width: 100%;\r\n            border-radius: 6px;\r\n            position: absolute;\r\n            top: 0;\r\n            bottom: 0;\r\n            left: 0;\r\n            right: 0;\r\n            z-index: -1;\r\n            transition: transform .5s cubic-bezier(.165,.84,.44,1);\r\n        }\r\n        @media #{$sm-layout} {\r\n            padding: 12px 25px;\r\n            font-size: 14px;\r\n        }\r\n        i {\r\n            font-weight: 400;\r\n            margin-right: 10px;\r\n            color: var(--color-heading);\r\n            transition: 0.3s;\r\n        }\r\n        &:hover {\r\n            &:before {\r\n                transform: scale(1.1);\r\n            }\r\n        }\r\n        &.right-icon {\r\n            i {\r\n                margin-left: 5px;\r\n                margin-right: 0;\r\n                color: var(--color-heading);\r\n                position: relative;\r\n                top: 2px;\r\n            }\r\n        }\r\n        &.btn-bg-white {\r\n            background-color: var(--color-white);\r\n            color: var(--color-heading);\r\n            box-shadow: 0 16px 32px 0 rgba(103, 103, 103, .06);\r\n            &:before {\r\n                background-color: var(--color-white);\r\n            }\r\n        }\r\n        &.btn-bg-lighter {\r\n            background-color: var(--color-lighter);\r\n            color: var(--color-body);\r\n            &:before {\r\n                background-color: var(--color-lighter);\r\n            }\r\n        }\r\n        &.btn-bg-primary {\r\n            background-color: var(--color-primary);\r\n            color: var(--color-white);\r\n            &:before {\r\n                background-color: var(--color-primary);\r\n            }\r\n            i {\r\n                color: var(--color-white);\r\n            }\r\n            &:hover {\r\n                // background-color: var(--color-secondary);\r\n                &:after {\r\n                    transform: scale(1.1);\r\n                }\r\n            }\r\n        }\r\n        &.btn-bg-secondary {\r\n            background-color: var(--color-secondary);\r\n            color: var(--color-white);\r\n            i {\r\n                color: var(--color-white);\r\n            }\r\n            &:before {\r\n                background-color: var(--color-secondary);\r\n            }\r\n        }\r\n        &.btn-outline {\r\n            border: 2px solid var(--color-heading);\r\n            &:hover {\r\n                background-color: var(--color-primary);\r\n                border-color: var(--color-primary);\r\n                color: var(--color-white);\r\n            }\r\n        }\r\n        &.wishlist-btn {\r\n            border: 2px solid var(--color-light);\r\n            padding: 15px 16px 10px;\r\n            @media #{$sm-layout} {\r\n                padding: 11px 18px 9px;\r\n            }\r\n            i {\r\n                font-size: 20px;\r\n                color: var(--color-body);\r\n                margin: 0;\r\n                @media #{$sm-layout} {\r\n                    font-size: 16px;\r\n                }\r\n            }\r\n            &:before {\r\n                background-color: var(--color-primary);\r\n                opacity: 0;\r\n                visibility: hidden;\r\n                transform: scale(.8);\r\n                transition: .3s;\r\n            }\r\n            &:hover {\r\n                border-color: var(--color-primary);\r\n                i {\r\n                    color: var(--color-white);\r\n\r\n                }\r\n                &:before {\r\n                    visibility: visible;\r\n                    opacity: 1;\r\n                    transform: scale(1.2);\r\n                }\r\n            }\r\n        }\r\n        &.btn-size-md {\r\n            font-size: 14px;\r\n            font-weight: 500;\r\n            display: inline-flex;\r\n            align-items: center;\r\n            padding: 10px 30px 9px;\r\n            border-radius: 8px;\r\n            i {\r\n                font-size: 21px;\r\n            }\r\n        }\r\n    }\r\n}", "/*-------------------------\r\nCategories \r\n--------------------------*/\r\n.categrie-product {\r\n    min-width: 120px;\r\n    position: relative;\r\n    text-align: center;\r\n    border-radius: 6px;\r\n    margin-bottom: 30px;\r\n    box-shadow: 0 15px 20px -10px rgba(0, 0, 0, 0.04);\r\n    transition: .5s ease-in-out;\r\n    z-index: 1;\r\n    margin-top: 30px;\r\n    &:before {\r\n        content: \"\";\r\n        height: 100%;\r\n        width: 100%;\r\n        background-color: var(--color-white);\r\n        border: 1px solid #f0f0f0;\r\n        border-radius: 4px;\r\n        position: absolute;\r\n        top: 0;\r\n        bottom: 0;\r\n        left: 0;\r\n        right: 0;\r\n        z-index: -1;\r\n        transition: transform .5s cubic-bezier(.165,.84,.44,1);\r\n    }\r\n    a {\r\n        padding: 28px 12px;\r\n        display: block;\r\n    }\r\n    img {\r\n        margin: 0 auto 8px;\r\n        min-height: 41px;\r\n    }\r\n    .cat-title {\r\n        margin-bottom: 0;\r\n        font-size: 16px;\r\n        line-height: 24px;\r\n    }\r\n    &:hover {\r\n        border-color: var(--color-white);\r\n        box-shadow: 0 20px 20px -10px rgba(0, 0, 0, .1);\r\n        \r\n        &:before {\r\n            transform: scale(1.1);\r\n        }\r\n    }\r\n}\r\n\r\n.categrie-product-2 {\r\n    border: 1px solid #f0f0f0;\r\n    position: relative;\r\n    text-align: center;\r\n    border-radius: 6px;\r\n    transition: 0.3s;\r\n    box-shadow: var(--shadow-dark);\r\n    margin-bottom: 30px;\r\n    a {\r\n        padding: 17px 10px;\r\n        display: flex;\r\n        justify-content: center;\r\n    }\r\n  \r\n    img {\r\n        margin-right: 10px;\r\n        max-height: 24px;\r\n    }\r\n    .cat-title {\r\n        margin-bottom: 0;\r\n        font-size: 16px;\r\n        line-height: 24px;\r\n    }\r\n    &:hover {\r\n        background: #fff;\r\n        box-shadow: none;\r\n    }\r\n}\r\n\r\n.categrie-product-3 {\r\n    border: 1px solid #f0f0f0;\r\n    &:before {\r\n        display: none;\r\n    }\r\n    a {\r\n        padding: 12px 12px;\r\n        img {\r\n            border-radius: 4px;\r\n            width: 100%;\r\n        }\r\n    }\r\n}\r\n\r\n.categrie-product-4 {\r\n    box-shadow: none;\r\n    margin-top: 0;\r\n    &:before {\r\n        display: none;\r\n    }\r\n    .cate-thumb {\r\n        padding: 0;\r\n        img {\r\n            min-height: auto;\r\n            margin: 0 auto;\r\n        }\r\n    }\r\n    .cat-title {\r\n        margin-top: 16px;\r\n        font-size: 20px;\r\n        font-weight: 500;\r\n    }\r\n    &:hover {\r\n        box-shadow: none;\r\n    }\r\n}\r\n\r\n.categorie-product-two {\r\n    .slick-arrow {\r\n        display: none !important;\r\n    }\r\n}\r\n\r\n.axil-categorie-area {\r\n    .section-title-wrapper {\r\n        margin-bottom: 10px;\r\n        .title {\r\n            margin-bottom: 0;\r\n        }\r\n        @media #{$sm-layout} {\r\n            margin-bottom: 0;\r\n        }\r\n    }\r\n    .arrow-top-slide .slide-arrow {\r\n        top: -60px;\r\n    }\r\n}\r\n", "/*-------------------------\r\n Contact \r\n--------------------------*/\r\n#gmap_canvas {\r\n\twidth: 100%;\r\n\tborder-radius: 6px;\r\n\tborder: none;\r\n}\r\n.axil-contact-page {\r\n\t.title {\r\n\t\tfont-weight: var(--s-medium);\r\n\t\tcolor: var(--color-black);\r\n\t}\r\n\t.contact-form {\r\n\t\t@media only screen and (max-width: 991px) {\r\n\t\t\tmargin-bottom: 50px;\t\r\n\t\t}\r\n\t\tp {\r\n\t\t\twidth: 80%;\r\n\t\t\tmargin-bottom: 45px;\r\n\t\t}\r\n\t}\r\n\t.form-group {\r\n\t\tlabel {\r\n\t\t\tspan {\r\n\t\t\t\tcolor: \tvar(--color-chart03);\r\n\t\t\t}\r\n\t\t}\r\n\t\tinput {\r\n\t\t\theight: 60px;\r\n\t\t}\r\n\t\t.axil-btn {\r\n\t\t\twidth: auto;\r\n\t\t}\r\n\t}\r\n\t.contact-location {\r\n\t\tspan {\r\n\t\t\tdisplay: block;\r\n\t\t\t@media #{$sm-layout} {\r\n\t\t\t\tfont-size: var(--font-size-b2);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}", "/*-------------------------\r\nCountdown \r\n--------------------------*/\r\n.countdown {\r\n    display: flex;\r\n    .countdown-section {\r\n        height: 80px;\r\n        width: 80px;\r\n        background-color: var(--color-white);\r\n        border-radius: 50%;\r\n        margin-right: 15px;\r\n        display: flex;  \r\n        align-items: center;\r\n        justify-content: center;\r\n        text-align: center; \r\n        @media #{$md-layout} {\r\n            height: 70px;\r\n            width: 70px;\r\n        } \r\n        @media #{$sm-layout} {\r\n            height: 60px;\r\n            width: 60px;\r\n            margin-right: 10px;\r\n        }\r\n        @media #{$small-mobile} {\r\n            margin-right: 6px;\r\n        }\r\n        &:last-child {\r\n            margin-right: 0;\r\n            &:after {\r\n                display: none;\r\n            }\r\n        }\r\n    }\r\n    .countdown-number {\r\n        font-size: 24px;\r\n        font-weight: var(--s-medium);\r\n        color: var(--color-black);\r\n        line-height: 1;\r\n        margin-bottom: 5px;\r\n        @media #{$md-layout} {\r\n            font-size: 20px;\r\n        }\r\n        @media #{$sm-layout} {\r\n            font-size: 18px;\r\n        }\r\n    }\r\n    .countdown-unit {\r\n        line-height: 1;\r\n        font-size: 14px;\r\n        font-weight: var(--s-medium);\r\n    }\r\n}\r\n\r\n.sale-countdown {\r\n    .countdown-section {\r\n        background-color: var(--color-lighter);\r\n        height: 50px;\r\n        width: 50px;\r\n        margin-right: 25px;\r\n        position: relative;\r\n        &:after {\r\n            content:\":\";\r\n            font-size: var(--font-size-b2);\r\n            color: var(--color-heading);\r\n            font-weight: var(--s-medium);\r\n            position: absolute;\r\n            right: -14px;\r\n        }\r\n    }\r\n    .countdown-number {\r\n        margin-bottom: 0;\r\n        font-size: var(--font-size-b2);\r\n        color: var(--color-heading);\r\n    }\r\n    .countdown-unit {   \r\n        display: none;\r\n    }\r\n}\r\n", "/*-------------------------\r\n    404 <USER>\r\n<GROUP>*/\r\n.onepage-screen-area {\r\n    position: relative;\r\n    z-index: 1;\r\n    background: var(--gradient-primary);\r\n    min-height: 500px;\r\n    padding: 100px 0;\r\n    @media #{$md-layout} {\r\n        text-align: center;\r\n        padding: 80px 0;\r\n    }\r\n    @media #{$sm-layout} {\r\n        text-align: center;\r\n        padding: 60px 0;\r\n    }\r\n    .content {\r\n        padding-right: 100px;\r\n        @media only screen and (max-width: 991px) {\r\n            padding-right: 0;\r\n            margin-bottom: 50px;\r\n        }\r\n        .title {\r\n            margin-bottom: 30px;\r\n        }\r\n        @media only screen and (max-width: 991px) {\r\n            .title-highlighter {\r\n                justify-content: center;\r\n            }\r\n        }\r\n        p {\r\n            margin-bottom: 45px;\r\n        }\r\n     \r\n      \r\n    }\r\n}\r\n\r\n\r\n.comming-soon-area {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tbottom: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\theight: 100%;\r\n\twidth: 100%;\r\n\toverflow: hidden;\r\n    @media only screen and (max-width: 991px) {\r\n        padding: 60px 15px;\r\n    }\r\n}\r\n\r\n.comming-soon-banner {\r\n\tmin-height: 100vh;\r\n\theight: 100vh;\r\n\tpadding: 100px 50px;\r\n\t@media #{$lg-layout} {\r\n\t\tmargin-right: 50px;\r\n\t}\r\n\t@media only screen and (max-width: 991px) {\r\n\t\tdisplay: none;\t\r\n\t}\r\n}\r\n\r\n.comming-soon-content {\r\n    text-align: center;\r\n    .brand-logo {\r\n        margin-bottom: 30px;\r\n    }\r\n    .title {\r\n        margin-bottom: 15px;\r\n    }\r\n    .countdown {\r\n        margin-bottom: 50px;\r\n        justify-content: center;\r\n        @media #{$smlg-device} {\r\n        }\r\n        .countdown-section {\r\n            box-shadow: 0 8px 16px 0 rgba(53, 119, 240, .3);\r\n            height: 100px;\r\n            width: 100px;\r\n            background-color: var(--color-primary);\r\n            color: var(--color-white);\r\n            @media #{$large-mobile} {\r\n                height: 60px;\r\n                width: 60px;\r\n            }\r\n        }\r\n        .countdown-number {\r\n            font-size: 30px;\r\n            color: var(--color-white);\r\n            @media #{$large-mobile} {\r\n                font-size: 20px;\r\n            }\r\n        }\r\n    }\r\n    .newsletter-form {\r\n        justify-content: center;\r\n        input {\r\n            background-color: var(--color-lighter);\r\n            \r\n        }\r\n    }\r\n}\r\n\r\n", "/*-------------------------\r\nNewsletter  \r\n--------------------------*/\r\n.etrade-newsletter-wrapper{\r\n  padding: 100px 107px 85px;\r\n  border-radius: 6px;\r\n  @media #{$smlg-device} {\r\n    padding: 80px 30px 65px;\r\n  }\r\n\r\n  @media #{$sm-layout} {\r\n    padding: 60px 20px 45px;\r\n  }\r\n  \r\n}\r\n.newsletter-content {\r\n  .title {\r\n    @media #{$large-mobile} {\r\n      letter-spacing: -0.045em;\r\n      font-size: 30px;\r\n\r\n    }\r\n  }\r\n}\r\n.newsletter-form{\r\n  display: flex;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  @media #{$sm-layout} {\r\n    display: block;\r\n  }\r\n  button{\r\n    width: auto;\r\n    border-radius: 6px !important;\r\n    background-color: var(--color-heading);\r\n    color: var(--color-white);\r\n    padding: 16px 38px 17px;\r\n    &:before {\r\n      background-color: var(--color-heading);\r\n    }\r\n    &:hover {\r\n      background-color: var(--color-secondary);\r\n    }\r\n    @media #{$sm-layout} {\r\n      padding: 16px 35px;\r\n    }\r\n  }\r\n}\r\n.newsletter-inner{\r\n  margin-right: 20px;\r\n  @media #{$sm-layout} {\r\n    margin-right: 0;\r\n  }\r\n  input{\r\n    padding-left: 66px;\r\n    width: 390px;\r\n  }\r\n}\r\n.send-mail-icon {\r\n  position: absolute;\r\n  max-width: 100%;\r\n  top: 17px;\r\n  left: 30px;\r\n}\r\n\r\n", "/* -----------------------\r\nPagination \r\n--------------------------*/\r\n.post-pagination {\r\n  margin-top: 80px;\r\n  width: 100%;\r\n  @media #{$lg-layout} {\r\n      margin-top: 40px;\r\n  }\r\n  @media #{$md-layout} {\r\n      margin-top: 30px;\r\n  }\r\n  @media #{$sm-layout} {\r\n      margin-top: 30px;\r\n  }\r\n  nav {\r\n      &.pagination {\r\n          display: block;\r\n          .screen-reader-text {\r\n              display: none;\r\n          }\r\n          ul {\r\n              position: relative;\r\n              display: flex;\r\n              list-style: none;\r\n              flex-wrap: wrap;\r\n              align-items: center;\r\n              margin: -3px;\r\n              padding: 0;\r\n              li {\r\n                  margin: 3px;\r\n                  span {\r\n                      line-height: 42px;\r\n                      min-width: 42px;\r\n                      text-align: center;\r\n                      color: var(--color-heading);\r\n                      transition: all 0.5s;\r\n                      display: block;\r\n                      padding: 0 15px;\r\n                      transition: all 0.5s;\r\n                      border: 1px solid var(--color-border-light);\r\n                      border-radius: var( --radius-small);\r\n                      &.current {\r\n                          background: var(--color-primary);\r\n                          color: #ffffff;\r\n                          border-color: var(--color-primary);\r\n                      }\r\n                  }\r\n                  a {\r\n                      line-height: 42px;\r\n                      min-width: 42px;\r\n                      text-align: center;\r\n                      color: var(--color-heading);\r\n                      transition: all 0.5s;\r\n                      display: block;\r\n                      padding: 0 15px;\r\n                      transition: all 0.5s;\r\n                      border: 1px solid var(--color-border-light);\r\n                      border-radius: var( --radius-small);\r\n                      &:hover {\r\n                          background: var(--color-primary);\r\n                          color: #ffffff;\r\n                          border-color: var(--color-primary);\r\n                      }\r\n                  }\r\n              }\r\n          }\r\n      }\r\n  }\r\n}", "/*-------------------------\r\nPoster\r\n--------------------------*/\r\n.single-poster {\r\n    position: relative;\r\n    overflow: hidden;\r\n    border-radius: 6px;\r\n    a {\r\n        display: block;\r\n        img {\r\n            transition: 0.5s;\r\n            width: 100%;\r\n        }\r\n    }\r\n    .poster-content {\r\n        position: absolute;\r\n        top: 50%;\r\n        right: 0;\r\n        transform: translateY(-50%);\r\n        z-index: 2;\r\n        padding-right: 100px;\r\n        pointer-events: none;\r\n        @media #{$small-mobile} {\r\n            padding: 20px;\r\n        }\r\n        &.content-left {\r\n            right: auto;\r\n            left: 0;\r\n            padding-right: 0;\r\n            padding-left: 60px;\r\n            @media only screen and (max-width: 991px) {\r\n                padding-left: 30px;\r\n            }\r\n            .title {\r\n                margin-bottom: 0;\r\n                margin-top: 8px;\r\n            }\r\n            .sub-title {\r\n                color: rgba(255, 255, 255, .6);\r\n                transition: all 0.3s ease-in-out;\r\n            }\r\n        }\r\n    }\r\n    .inner {\r\n        .title {\r\n            line-height: 1;\r\n            margin-bottom: 20px;\r\n            font-size: 40px;\r\n            color: var(--color-white);\r\n            @media #{$sm-layout} {\r\n               font-size: 30px;\r\n            }\r\n            @media #{$large-mobile} {\r\n               font-size: 22px;\r\n            }\r\n            @media #{$small-mobile} {\r\n                margin-bottom: 10px;\r\n            }\r\n        }\r\n        .sub-title {    \r\n            font-size: 16px;\r\n            line-height: 24px;\r\n            position: relative;\r\n            color: rgba(255, 255, 255, .4);\r\n            transition: all 0.3s ease-in-out;\r\n            i {\r\n                vertical-align: middle;\r\n                margin-left: 10px;\r\n            }\r\n        }\r\n    }\r\n    &:hover {\r\n        img {\r\n            transform: scale(1.1);\r\n            @media #{$large-mobile} {\r\n                transform: scale(1.25);\r\n            }\r\n        }\r\n        .poster-content {\r\n            .sub-title {\r\n                color: var(--color-white);\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.poster-countdown-wrap {\r\n    padding: 65px 80px;\r\n    border-radius: 6px;\r\n    margin-top: 95px;\r\n    @media #{$smlg-device} {\r\n        margin-top: 0;\r\n    }\r\n    @media only screen and (max-width: 991px) {\r\n        text-align: center;\r\n        .poster-countdown {\r\n            justify-content: center;\r\n        }  \r\n    }\r\n    @media #{$sm-layout} {\r\n        padding: 50px 30px;\r\n    }\r\n    @media #{$small-mobile} {\r\n        padding: 40px 15px;\r\n    }\r\n\r\n    .section-title-wrapper {\r\n        margin-bottom: 0;\r\n        padding-right: 0;\r\n        .title {\r\n            font-size: 48px;\r\n            @media #{$smlg-device} {\r\n                font-size: 40px;\r\n            }\r\n            @media #{$sm-layout} {\r\n                font-size: 28px;\r\n            }\r\n\r\n        }\r\n        .title-highlighter {\r\n            @media only screen and (max-width: 991px) {\r\n                justify-content: center;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.poster-countdown-thumbnail {\r\n    text-align: center;\r\n    margin-top: -160px;\r\n    position: relative;\r\n    @media #{$smlg-device} {\r\n        margin-top: 0;\r\n    }\r\n    @media only screen and (max-width: 991px) {\r\n        margin-top: 50px;  \r\n    }\r\n}\r\n\r\n.music-singnal {\r\n    display: block;\r\n    position: absolute;\r\n    top: 48%;\r\n    left: 45%;\r\n    width: 100px;\r\n    height: 100px;\r\n    transform: rotate(-95deg);\r\n    @media #{$lg-layout} {\r\n        top: 44%;\r\n        left: 40%;\r\n    } \r\n    @media #{$large-mobile} {\r\n        display: none;\r\n    }\r\n\r\n    .item-circle {\r\n        display: block;\r\n        width: 100%;\r\n        height: 100%;\r\n        position: absolute;\r\n        bottom: 0;\r\n        left: 0;\r\n        border-color: var(--color-chart03);\r\n        border-style: solid;\r\n        border-width: 1px 4px 0 0;\r\n        border-radius: 0 100% 0 0;\r\n        opacity: 0;\r\n        animation: signalanimation 2s infinite;\r\n\r\n        &.circle-1 {\r\n            animation-delay: 800ms;\r\n        }\r\n        &.circle-2 {\r\n            width: 80px;\r\n            height: 80px;\r\n            animation-delay: 600ms;\r\n        }\r\n        &.circle-3 {\r\n            width: 60px;\r\n            height: 60px;\r\n            animation-delay: 400ms;\r\n        }\r\n        &.circle-4 {\r\n            width: 40px;\r\n            height: 40px;\r\n            animation-delay: 200ms;\r\n        }\r\n        &.circle-5 {\r\n            width: 20px;\r\n            height: 20px;\r\n            animation-delay: 0ms;\r\n        }\r\n    }\r\n}\r\n\r\n// Sale Banner \r\n.sale-banner-thumb {\r\n    img {\r\n        border-radius: 8px;\r\n    }\r\n}\r\n\r\n\r\n// Delivery Poster\r\n.delivery-poster-area {\r\n    padding-bottom: 50px;\r\n}\r\n.delivery-poster {\r\n    display: flex;\r\n    background: linear-gradient(97.29deg, #FFFFFF -1.43%, rgba(230, 222, 255, 0.634676) 43.99%, rgba(242, 238, 255, 0) 111.12%);\r\n    border: 3px solid #EAE3FF;\r\n    border-radius: 8px;\r\n    padding: 30px 70px 12px 30px;\r\n    margin-bottom: 30px;\r\n    @media (max-width: 575px) {\r\n        padding: 30px;\r\n    }\r\n    .content {\r\n        flex: 1;\r\n        padding-right: 50px;\r\n        @media (max-width: 575px) {\r\n            padding-right: 0;\r\n        }\r\n        .badge {\r\n            font-size: 12px;\r\n            font-weight: 500;\r\n            color: var(--color-white);\r\n            background-color: var(--color-secondary);\r\n            padding: 8px 10px;\r\n            border-radius: 4px;\r\n            display: inline-block;\r\n            text-transform: uppercase;\r\n            margin-bottom: 34px;\r\n        }\r\n        .title {\r\n            font-weight: 700;\r\n            color: var(--color-primary);\r\n            margin-bottom: 8px;\r\n        }\r\n        p {\r\n            margin-bottom: 0;\r\n            color: var(--color-heading);\r\n        }\r\n    }\r\n    .thumbnail {\r\n        @media (max-width: 575px) {\r\n            display: none;\r\n        }\r\n    }\r\n    &.delivery {\r\n        background: linear-gradient(97.29deg, #FFFFFF -1.43%, rgba(255, 224, 222, 0.64) 43.99%, rgba(242, 238, 255, 0) 111.12%);\r\n        \r\n    }\r\n}", "/*-------------------------\r\nPrice Slider\r\n--------------------------*/\r\n\r\n.ui-slider-handle.ui-state-default.ui-corner-all {\r\n    background: #5956E9 none repeat scroll 0 0;\r\n    border: medium none;\r\n    border-radius: 50%;\r\n    height: 13px;\r\n    margin-left: -1px;\r\n    top: 50%;\r\n    -webkit-transform: translateY(-50%);\r\n    transform: translateY(-50%);\r\n    width: 13px;\r\n}\r\n\r\n.ui-slider-range.ui-widget-header.ui-corner-all {\r\n    background: #DBDEFF none repeat scroll 0 0;\r\n    border-radius: 0;\r\n    height: 4px;\r\n}\r\n\r\n.ui-widget.ui-widget-content {\r\n    background: #CBD3D9 none repeat scroll 0 0;\r\n    border: medium none;\r\n    height: 4px;\r\n}\r\n\r\n.ui-slider-horizontal .ui-slider-handle {\r\n    margin-left: 0;\r\n}\r\n\r\n.ui-slider-handle.ui-corner-all.ui-state-default.ui-state-focus {\r\n    outline: medium none;\r\n}\r\n\r\n.amount-range {\r\n    background: rgba(0, 0, 0, 0) none repeat scroll 0 0;\r\n    border: medium none;\r\n    color: #666;\r\n    font-size: 15px;\r\n    margin-top: 10px;\r\n    padding: 5px 0;\r\n}\r\n\r\n.amount-range,\r\n.price-button {\r\n    width: 100%;\r\n    word-spacing: 10px;\r\n    padding: 0!important;\r\n    font-weight: 500!important;\r\n    font-size: 16px!important;\r\n    line-height: 1!important;\r\n    margin-top: 0!important;\r\n}\r\n\r\n.input-range {\r\n    font-weight: 500;\r\n    padding-right: 3px;\r\n}\r\n\r\n.flter-option.mb-80 {\r\n    padding-right: 15px;\r\n}", "/*-------------------------\r\nPrivacy Policy\r\n--------------------------*/\r\n.axil-privacy-policy {\r\n\t.policy-published {\r\n\t\tfont-size: 22px;\r\n\t\tfont-weight: var(--s-medium);\r\n\t\tcolor: var(--color-dark);\r\n\t\tposition: relative;\r\n\t\tpadding-bottom: 20px;\r\n\t\tmargin-bottom: 70px;\r\n\t\tdisplay: inline-block;\r\n\t\t&:after {\r\n\t\t\tcontent: \"\";\r\n\t\t\theight: 3px;\r\n\t\t\twidth: 100%;\r\n\t\t\tbackground-color: var(--color-primary);\r\n\t\t\tposition: absolute;\r\n\t\t\tbottom: 0;\r\n\t\t\tleft: 0;\r\n\t\t\tright: 0;\r\n\t\t}\r\n\t}\r\n\t.title {\r\n\t\tmargin-bottom: 20px;\r\n\t\tcolor: var(--color-dark);\r\n\t\tfont-weight: var(--s-medium);\r\n\t}\r\n\r\n\ta {\r\n\t\tcolor: var(--color-primary);\r\n\t}\r\n\tul {\r\n\t\tlist-style: disc;\r\n\t\tmargin-bottom: 45px;\r\n\t\tli {\r\n\t\t\tmargin: 0 0 12px 0;\r\n\t\t\t&::marker {\r\n\t\t\t\tcolor: var(--color-light);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n", "\r\n/*-------------------------\r\nSection Heading  \r\n--------------------------*/\r\n.title-highlighter {\r\n    font-size: 14px;\r\n    font-weight: var(--s-bold);\r\n    line-height: 1;\r\n    display: flex;\r\n\talign-items: center;\r\n\tmargin-bottom: 10px;\r\n    i {\r\n      height: 24px;\r\n      width: 24px;\r\n      line-height: 24px;\r\n      border-radius: 50%;\r\n      font-size: 12px;\r\n      text-align: center;\r\n      margin-right: 10px;\r\n      \r\n    }\r\n\t&.highlighter-primary {\r\n\t\tcolor: var(--light-primary);\r\n\t\ti {\r\n\t\tbackground-color: var(--light-primary);\r\n\t\tcolor: var(--color-white);\r\n\t\t}\r\n\t}\r\n\t&.highlighter-primary2 {\r\n\t\tcolor: var(--color-primary);\r\n\t\ti {\r\n\t\tbackground-color: var(--color-primary);\r\n\t\tcolor: var(--color-white);\r\n\t\t}\r\n\t}\r\n\t&.highlighter-secondary {\r\n\t\tcolor: var(--color-secondary);\r\n\t\ti {\r\n\t\t\tbackground-color: var(--color-secondary);\r\n\t\t\tcolor: var(--color-white);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.section-title-wrapper {\r\n   margin-bottom: 40px;\r\n   padding-right: 100px;\r\n   @media only screen and (max-width: 767px) {\r\n\t   margin-bottom: 30px;\r\n\t   \r\n   }\r\n   &.section-title-center {\r\n\t   text-align: center;\r\n\t   padding-right: 0;\r\n\t   .title-highlighter {\r\n\t\t   justify-content: center;\r\n\t   }\r\n   }\r\n}\r\n\r\n.section-title-border {\r\n\tborder-bottom: 1px solid #EBEBEB;\r\n\tmargin-bottom: 30px;\r\n\tpadding-bottom: 25px;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\tpadding-right: 0;\r\n\t.title {\r\n\t\tmargin-bottom: 0;\r\n\t\tpadding-right: 20px;\r\n\t\tflex: 1;\r\n\t}\r\n\t.view-btn {\r\n\t\ta {\r\n\t\t\tcolor: var(--color-primary);\r\n\t\t\ttext-decoration: underline;\r\n\t\t\tfont-weight: 700;\r\n\t\t\ttransition: 0.3s;\r\n\t\t\t&:hover {\r\n\t\t\t\tcolor: var(--color-secondary);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t&.slider-section-title {\r\n\t\t.title {\r\n\t\t\tpadding-right: 100px;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.flash-sale-section {\r\n\tmargin-bottom: 60px;\r\n\t@media #{$sm-layout} {\r\n\t\tmargin-bottom: 50px;\r\n\t}\r\n\t.section-title-wrapper {\r\n\t\tmargin-bottom: 0;\r\n\t\t@media only screen and (max-width: 991px) {\r\n\t\t\tpadding-right: 80px;\r\n\t\t}\r\n\t\t@media #{$sm-layout} {\r\n\t\t\tpadding-right: 100px;\r\n\t\t\tmargin-bottom: 30px;\r\n\t\t}\r\n\t\t.title {\r\n\t\t\tmargin-bottom: 0;\r\n\t\t}\r\n\t}\r\n}", "/*-------------------------\r\nService\r\n--------------------------*/\r\n.service-box {\r\n    background-color: var(--color-white);\r\n    border: 1px solid #f1f1f1;\r\n    padding: 50px 30px;\r\n    text-align: center;\r\n    margin-bottom: 30px;\r\n    border-radius: 6px;\r\n    position: relative;\r\n    transition: all .3s ease-in-out;\r\n    @media only screen and (min-width: 1400px) {\r\n        padding: 50px 40px;\r\n    }\r\n    @media #{$smlg-device} {\r\n        padding: 40px 30px;\r\n    }\r\n    @media #{$sm-layout} {\r\n        padding: 40px;\r\n    }\r\n    &:before {\r\n        content: \"\";\r\n        height: 100%;\r\n        width: 90%;\r\n        border: 1px solid #f1f1f1;\r\n        border-radius: 6px;\r\n        position: absolute;\r\n        bottom: 0;\r\n        left: 0;\r\n        right: 0;\r\n        margin: 0 auto;\r\n        z-index: -1;\r\n        visibility: hidden;\r\n        opacity: 0;\r\n        transition: all .3s ease-in-out;\r\n    }\r\n    .icon {\r\n        margin-bottom: 20px;\r\n        img {\r\n            max-height: 60px;\r\n        }\r\n    }\r\n    .title {\r\n        font-size: 16px;\r\n        line-height: 26px;\r\n        font-weight: 700;\r\n        margin-bottom: 0;\r\n    }\r\n    &:hover {\r\n        box-shadow: var(--shadow-dark);\r\n        &:before {\r\n            visibility: visible;\r\n            opacity: 1;\r\n            bottom: -12px;\r\n        }\r\n    }\r\n    &.service-style-2 {\r\n        display: flex;\r\n        border: none;\r\n        padding: 0;\r\n        text-align: left;\r\n        background-color: transparent;\r\n        &:before {\r\n            display: none;\r\n        }\r\n        .icon {\r\n            margin-right: 20px;\r\n            margin-bottom: 0;\r\n            max-width: 45px;\r\n            margin-top: 6px;\r\n        }\r\n        .content {\r\n            flex: 1;\r\n            .title {\r\n                line-height: var(--line-height-b1);\r\n            }\r\n        }\r\n        &:hover {\r\n            box-shadow: none;\r\n        }\r\n    }\r\n    &.service-style-3 {\r\n        background-color: #F6F7FB;\r\n        border: none;\r\n        border-radius: 8px;\r\n        padding: 60px 30px 40px;\r\n        &:before {\r\n            display: none;\r\n        }\r\n        .icon {\r\n            position: relative;\r\n            z-index: 1;\r\n            margin-bottom: 40px;\r\n            &:before {\r\n                content: \"\";\r\n                width: 99px;\r\n                height: 99px;\r\n                background-color: var(--color-white);\r\n                border-radius: 50%;\r\n                position: absolute;\r\n                top: 50%;\r\n                transform: translateY(-50%);\r\n                left: 0;\r\n                right: 0;\r\n                margin: 0 auto;\r\n                z-index: -1;\r\n            }\r\n            i {\r\n                font-size: 50px;\r\n                color: var(--color-primary);\r\n            }\r\n        }\r\n        .content {\r\n            .title {\r\n                margin-bottom: 4px;\r\n                font-weight: 700;\r\n            }\r\n            p {\r\n                margin-bottom: 0;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// How To Sell\r\n.how-to-sell {\r\n    padding: 30px 20px;\r\n    .title {\r\n        font-size: 18px;\r\n        margin-bottom: 10px;\r\n    }\r\n    p {\r\n        font-size: 16px;\r\n    }\r\n}", "/*----------------------------\r\nSlick Style  \r\n------------------------------*/\r\n\r\n.slick-layout-wrapper--20 {\r\n    .slick-list {\r\n        margin: -20px;\r\n        @media #{$large-mobile} {\r\n            margin: -20px -10px;\r\n        }\r\n    }\r\n    .slick-single-layout {\r\n        padding: 20px;\r\n        @media #{$large-mobile} {\r\n            padding: 20px 10px;\r\n        }\r\n    }\r\n}\r\n\r\n.slick-layout-wrapper--10 {\r\n    .slick-list {\r\n        margin: 0 -10px;\r\n    }\r\n    .slick-single-layout,\r\n    .slick-slide {\r\n        padding: 0 10px;\r\n    }\r\n}\r\n\r\n.slick-layout-wrapper--15 {\r\n    .slick-list {\r\n        margin: 0 -15px;\r\n    }\r\n    .slick-single-layout,\r\n    .slick-slide {\r\n        padding: 0 15px;\r\n    }\r\n}\r\n\r\n.slick-layout-wrapper--30 {\r\n    .slick-list {\r\n        margin: 0 -30px;\r\n        @media #{$sm-layout} {\r\n            margin: 0 -15px;\r\n        }\r\n    }\r\n    .slick-single-layout {\r\n        padding: 0 30px;\r\n        @media #{$sm-layout} {\r\n            padding: 0 15px;\r\n        }\r\n    }\r\n}\r\n\r\n.axil-gallery-activation {\r\n    position: relative;\r\n    &.axil-slick-arrow {\r\n        .slide-arrow {\r\n            background: #fff;\r\n            border: 1px solid #fff;\r\n        }\r\n    }\r\n    &.arrow-between-side {\r\n        .slide-arrow {\r\n            left: 10px;\r\n            &.next-arrow {\r\n                left: auto;\r\n                right: 10px;\r\n            }\r\n        }\r\n        &:hover {\r\n            .slide-arrow {\r\n                left: 30px;\r\n                &.next-arrow {\r\n                    left: auto;\r\n                    right: 30px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n/* Axil Slick Arrow  */\r\n\r\n.axil-slick-arrow {\r\n    .slide-arrow {\r\n        outline: none;\r\n        width: 50px;\r\n        height: 50px;\r\n        background: var(--color-lighter);\r\n        color: var(--color-body);\r\n        border: none;\r\n        border-radius: 6px;\r\n        z-index: 2;\r\n        &:before {\r\n            content: \"\";\r\n            height: 100%;\r\n            width: 100%;\r\n            background-color: var(--color-lighter);\r\n            border-radius: 6px;\r\n            position: absolute;\r\n            top: 0;\r\n            bottom: 0;\r\n            left: 0;\r\n            right: 0;\r\n            z-index: -1;\r\n            transition: transform .5s cubic-bezier(.165,.84,.44,1);\r\n        }\r\n        &:hover {\r\n            &:before {\r\n                transform: scale(1.1);\r\n            }\r\n        }\r\n        @media #{$sm-layout} {\r\n            width: 40px;\r\n            height: 40px;\r\n            font-size: var(--font-size-b2);\r\n        }\r\n        &.prev-arrow {\r\n            &:hover {\r\n                i {\r\n                    animation: prevNavSlide 400ms;\r\n                }\r\n            }\r\n        }\r\n        &.next-arrow {\r\n            &:hover {\r\n                i {\r\n                    animation: nextNavSlide 400ms;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &.testimonial-style-two-wrapper {\r\n        .slide-arrow {\r\n            background-color: var(--color-white);\r\n           \r\n            &:hover {\r\n                background-color: var(--color-primary);\r\n            }\r\n        }\r\n    }\r\n    &.header-campaign-arrow {\r\n        .slide-arrow {\r\n            background: transparent;\r\n            color: rgba($color: #ffffff, $alpha: .8);\r\n            &:before {\r\n                display: none;\r\n            }\r\n            &:hover {\r\n                color: var(--color-white);\r\n            }\r\n        }\r\n        .campaign-content {\r\n            margin: 0 50px;\r\n            @media #{$small-mobile} {\r\n                margin: 0 30px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.axil-slick-angle {\r\n    .slide-arrow {\r\n        outline: none;\r\n        width: 40px;\r\n        height: 40px;\r\n        background: var(--color-lighter);\r\n        color: #D6D6D6;\r\n        border: none;\r\n        border-radius: 50%;\r\n        z-index: 2;\r\n        &:before {\r\n            content: \"\";\r\n            height: 100%;\r\n            width: 100%;\r\n            background-color: var(--color-lighter);\r\n            border-radius: 50%;\r\n            position: absolute;\r\n            top: 0;\r\n            bottom: 0;\r\n            left: 0;\r\n            right: 0;\r\n            z-index: -1;\r\n            transition: transform .5s cubic-bezier(.165,.84,.44,1);\r\n        }\r\n        &:hover {\r\n            color: var(--color-body);\r\n            &:before {\r\n                transform: scale(1.1);\r\n            }\r\n        }\r\n        @media #{$sm-layout} {\r\n            width: 40px;\r\n            height: 40px;\r\n            font-size: var(--font-size-b2);\r\n        }\r\n        &.prev-arrow {\r\n            &:hover {\r\n                i {\r\n                    animation: prevNavSlide 400ms;\r\n                }\r\n            }\r\n        }\r\n        &.next-arrow {\r\n            &:hover {\r\n                i {\r\n                    animation: nextNavSlide 400ms;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.angle-top-slide {\r\n    .slide-arrow {\r\n        position: absolute;\r\n        left: 19px;\r\n        top: -100px;\r\n        @media #{$sm-layout} {\r\n            top: -90px;\r\n        }\r\n        &.next-arrow {\r\n            left: auto;\r\n            right: 0;\r\n        }\r\n        &.prev-arrow {\r\n            left: auto;\r\n            right: 55px;\r\n            @media #{$sm-layout} {\r\n                right: 50px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n/* Arrow Between Slide  */\r\n\r\n.arrow-between-side {\r\n    .slide-arrow {\r\n        position: absolute;\r\n        left: 40px;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n        @extend %transition;\r\n        &.next-arrow {\r\n            left: auto;\r\n            right: 40px;\r\n        }\r\n    }\r\n    &:hover {\r\n        .slide-arrow {\r\n            left: 10px;\r\n            @media #{$sm-layout} {\r\n                left: 14px;\r\n            }\r\n            &.next-arrow {\r\n                left: auto;\r\n                right: 10px;\r\n                @media #{$sm-layout} {\r\n                    right: 14px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.arrow-both-side {\r\n    .slide-arrow {\r\n        position: absolute;\r\n        left: -45px;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n        @extend %transition;\r\n        @media #{$laptop-device} {\r\n            left: 0;\r\n        }\r\n        @media #{$smlg-device} {\r\n            left: 0;\r\n        }\r\n\r\n        &.next-arrow {\r\n            left: auto;\r\n            right: -45px;\r\n            @media #{$laptop-device} {\r\n                right: 0;\r\n            }\r\n            @media #{$smlg-device} {\r\n                right: 0;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.arrow-both-side-2 {\r\n    .slide-arrow {\r\n        position: absolute;\r\n        left: -68px;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n        @extend %transition;\r\n        @media #{$laptop-device} {\r\n            left: 0;\r\n        }\r\n        @media #{$smlg-device} {\r\n            left: 0;\r\n        }\r\n\r\n        &.next-arrow {\r\n            left: auto;\r\n            right: -75px;\r\n            @media #{$laptop-device} {\r\n                right: 0;\r\n            }\r\n            @media #{$smlg-device} {\r\n                right: 0;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.arrow-both-side-3 {\r\n    .slide-arrow {\r\n        position: absolute;\r\n        left: -60px;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n        @extend %transition;\r\n        @media #{$laptop-device} {\r\n            left: 0;\r\n        }\r\n        @media #{$smlg-device} {\r\n            left: 0;\r\n        }\r\n\r\n        &.next-arrow {\r\n            left: auto;\r\n            right: -60px;\r\n            @media #{$laptop-device} {\r\n                right: 0;\r\n            }\r\n            @media #{$smlg-device} {\r\n                right: 0;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.arrow-both-side-4 {\r\n    .slide-arrow {\r\n        position: absolute;\r\n        left: 0;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n        background-color: transparent;\r\n        height: auto;\r\n        width: auto;\r\n        padding: 0;\r\n        @extend %transition;\r\n        &:hover {\r\n            background-color: transparent;\r\n            box-shadow: none;\r\n        }\r\n        &.next-arrow {\r\n            left: auto;\r\n            right: 0;\r\n        }\r\n    }\r\n}\r\n\r\n/* Arrow Top Slide  */\r\n\r\n.arrow-top-slide {\r\n    .slide-arrow {\r\n        position: absolute;\r\n        left: 19px;\r\n        top: -100px;\r\n        @media #{$sm-layout} {\r\n            top: -77px;\r\n        }\r\n        &.next-arrow {\r\n            left: auto;\r\n            right: 0;\r\n        }\r\n        &.prev-arrow {\r\n            left: auto;\r\n            right: 58px;\r\n            @media #{$sm-layout} {\r\n                right: 50px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.post-list-view {\r\n    .post-gallery-activation {\r\n        &.axil-slick-arrow {\r\n            .slide-arrow {\r\n                background: #fff;\r\n                border: 1px solid #fff;\r\n                width: 30px;\r\n                height: 30px;\r\n                font-size: 14px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.slick-initialized .slick-slide {\r\n    outline: none;\r\n    margin-bottom: 0;\r\n}\r\n\r\n.slider-activation .slick-dots {\r\n    bottom: 50px;\r\n}\r\n\r\n.arrow-bottom-slide {\r\n    .slide-arrow {\r\n        position: absolute;\r\n        bottom: -130px;\r\n        &.next-arrow {\r\n            left: 65px;\r\n        }\r\n        &.prev-arrow {\r\n            left: 0;\r\n        }\r\n    }\r\n}\r\n\r\n// Pagination\r\n.axil-slick-dots {\r\n    .slick-dots {\r\n        bottom: -50px;\r\n        li {\r\n            margin: 0 5px;\r\n            height: 4px;\r\n            width: 24px;\r\n            button {\r\n                height: 4px;\r\n                width: 24px;\r\n                border-radius: 6px;\r\n                background-color: #e6e6e6;\r\n                padding: 0;\r\n                &:before {\r\n                    display: none;\r\n                }\r\n            }\r\n            &.slick-active {\r\n                width: 34px;\r\n                button {\r\n                    background-color: var(--color-heading);\r\n                    width: 34px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &.testimonial-style-two-wrapper {\r\n        .slick-dots {\r\n            text-align: left;\r\n            bottom: -50px;\r\n           li {\r\n               &.slick-active {\r\n                   button {\r\n                       background-color: var(--color-primary);\r\n                   }\r\n               }\r\n           }\r\n        }\r\n    }\r\n    &.slick-dots-bottom {\r\n        .slick-dots {\r\n            bottom: 20px;\r\n            li {\r\n                button {\r\n                    border-color: var(--color-white);\r\n                    &:before {\r\n                        color: var(--color-white);\r\n                        opacity: 1;\r\n                    }\r\n                }\r\n                &.slick-active {\r\n                    button {\r\n                        border-color: var(--color-primary);\r\n                        &:before {\r\n                            color: var(--color-primary);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}", "/*-------------------------\r\nSlider Style\r\n--------------------------*/\r\n.main-slider-content {\r\n    .subtitle {\r\n        font-size: 14px;\r\n        font-weight: var(--s-bold);\r\n        line-height: 1;\r\n        display: flex;\r\n        align-items: center;\r\n        margin-bottom: 16px;\r\n        color: var(--color-secondary);\r\n        i {\r\n            height: 24px;\r\n            width: 24px;\r\n            line-height: 24px;\r\n            border-radius: 50%;\r\n            font-size: 12px;\r\n            text-align: center;\r\n            margin-right: 10px;\r\n            background-color: var(--color-secondary);\r\n            color: var(--color-white);\r\n        }\r\n    }\r\n    .title {\r\n        letter-spacing: -0.03em;\r\n        margin-bottom: 50px;\r\n        font-size: 60px;\r\n        line-height: 1.2;\r\n        @media #{$smlg-device} {\r\n            font-size: 50px;\r\n        }\r\n        @media #{$md-layout} {\r\n            font-size: 44px;\r\n        }\r\n        @media #{$sm-layout} {\r\n            font-size: 30px;\r\n        }\r\n    }\r\n    .slide-action {\r\n        display: flex;\r\n        align-items: center;\r\n        @media #{$smlg-device} {\r\n            display: block;\r\n        }\r\n    }\r\n    .item-rating {\r\n        display: flex;\r\n        align-items: center;\r\n        flex: 1;\r\n        margin-left: 30px;\r\n        position: relative;\r\n        top: 2px;\r\n        @media #{$smlg-device} {\r\n            margin-left: 0;\r\n            margin-top: 30px;\r\n        }\r\n        .thumb {\r\n            line-height: 1;\r\n            ul {\r\n                padding-left: 26px;\r\n                margin: 0;\r\n                li {\r\n                    @extend %liststyle;\r\n                    display: inline-block;\r\n                    margin-left: -26px;\r\n                    img {\r\n                        border: 2px solid #f9f3f0;\r\n                        border-radius: 50%;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        .content {\r\n            flex: 1;\r\n            line-height: 1;\r\n            margin-left: 15px;\r\n            .rating-icon {\r\n                display: block;\r\n                font-size: 12px;\r\n                color: #ffa800;\r\n                margin-bottom: 5px;\r\n            }\r\n            .review-text {\r\n                font-size: 12px;\r\n                span {\r\n                    color: var(--color-heading);\r\n                    font-weight: 700;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .shop-btn {\r\n        a {\r\n            margin-right: 25px;\r\n            @media #{$sm-layout} {\r\n                margin-right: 5px;\r\n            }\r\n            &:last-child {\r\n                margin-right: 0;\r\n            }\r\n        }\r\n    }\r\n    .slick-slide.slick-current {\r\n        .subtitle {\r\n            animation: 800ms ease-in-out 0s normal none 1 running customOne;\r\n        }\r\n        .title {\r\n            animation: 1000ms ease-in-out 0s normal none 1 running customOne;\r\n        }\r\n        .shop-btn {\r\n            animation: 1200ms ease-in-out 0s normal none 1 running customOne;\r\n        }\r\n        .item-rating {\r\n            animation: 1200ms ease-in-out 0s normal none 1 running customOne;\r\n        }\r\n        img {\r\n            animation: 1200ms ease-in-out 0s normal none 1 running customOne;\r\n        }\r\n    }\r\n}\r\n\r\n.main-slider-large-thumb {\r\n    width: 70vw;\r\n    @media only screen and (max-width: 991px) {\r\n        width: auto;\r\n    }\r\n    .slick-track {\r\n        display: flex;\r\n        align-items: flex-end;\r\n\r\n    }\r\n    .single-slide {\r\n        position: relative;\r\n        img {\r\n            display: inline-block;\r\n        }\r\n        .product-price {\r\n            height: 100px;\r\n            width: 100px;\r\n            background-color: var(--color-white);\r\n            border-radius: 50%;\r\n            display: flex;\r\n            flex-direction: column;\r\n            justify-content: center;\r\n            text-align: center;\r\n            position: absolute;\r\n            top: 50px;\r\n            left: 150px;\r\n            visibility: hidden;\r\n            opacity: 0;\r\n            transform: scale(0);\r\n            transition-delay: 0.5s;\r\n            transition: all .5s ease-in-out;\r\n            @media only screen and (min-width: 1600px) and (max-width: 1920px) {\r\n                top: 100px;\r\n                left: 200px;\r\n            }\r\n            span {\r\n                &.text {\r\n                    color: var(--color-body);\r\n                }\r\n                &.price-amount {\r\n                    font-size: 18px;\r\n                    font-weight: 700;\r\n                    color: var(--color-primary);\r\n                }\r\n            }\r\n\r\n        }\r\n        &.slick-current {\r\n            .product-price {\r\n                visibility: visible;\r\n                opacity: 1;\r\n                transform: scale(1);\r\n            }\r\n        }\r\n    }\r\n    .single-slide.slick-active:not(.slick-current) {\r\n        img {\r\n            width: 160px;\r\n        }\r\n    }\r\n    .axil-slick-dots {\r\n        .slick-dots {\r\n            text-align: left;\r\n            bottom: -70px;\r\n            @media #{$large-mobile} {\r\n                text-align: center;\r\n                bottom: -55px;\r\n            }\r\n            li {\r\n                button {\r\n                    background-color: var(--color-white);\r\n                    opacity: .5;\r\n                }\r\n                &.slick-active {\r\n                    button {\r\n                        background-color: var(--color-secondary);\r\n                        opacity: 1;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.main-slider-style-1 {\r\n    background-color: #f9f3f0;\r\n    padding: 90px 0;\r\n    position: relative;\r\n    z-index: 1;\r\n    overflow: hidden;\r\n    min-height: 550px;\r\n    @media #{$smlg-device} {\r\n        min-height: 500px;\r\n    }\r\n    @media only screen and (max-width: 991px) {\r\n        padding: 40px 0 80px;\r\n        min-height: 450px;\r\n    }\r\n    @media #{$sm-layout} {\r\n        padding: 30px 0 60px;\r\n        min-height: 100%;\r\n    }\r\n    .main-slider-content {\r\n        margin: -30px;\r\n        @media #{$large-mobile} {\r\n            text-align: center;\r\n        }\r\n        .subtitle {\r\n            @media #{$large-mobile} {\r\n               justify-content: center;\r\n            }\r\n        }\r\n        .title {\r\n            @media #{$smlg-device} {\r\n                font-size: 56px;\r\n            }\r\n            @media #{$md-layout} {\r\n                font-size: 44px;\r\n                margin-bottom: 40px;\r\n            }\r\n            @media #{$sm-layout} {\r\n                font-size: 34px;\r\n                margin-bottom: 30px;\r\n            }\r\n\r\n        }\r\n        .item-rating {\r\n            @media #{$smlg-device} {\r\n                justify-content: flex-start;\r\n            }\r\n            @media #{$large-mobile} {\r\n                justify-content: center;\r\n            }\r\n            .content {\r\n                flex: initial;\r\n            }\r\n        }\r\n        .single-slide {\r\n            padding: 30px;\r\n        }\r\n    }\r\n    .main-slider-large-thumb {\r\n        @media #{$large-mobile} {\r\n            text-align: center;\r\n            padding: 40px 40px 0;\r\n        }\r\n        .single-slide {\r\n            .product-price {\r\n                @media #{$sm-layout} {\r\n                    height: 80px;\r\n                    width: 80px;\r\n                    top: 0;\r\n                    left: 0;\r\n                    span {\r\n                        font-size: 15px;\r\n                        &.price-amount {\r\n                            font-size: 15px;\r\n                        }\r\n                    }\r\n                }    \r\n                @media #{$large-mobile} {\r\n                    left: 50px;\r\n                }    \r\n            }\r\n        }\r\n    }\r\n    .shape-group {\r\n        li {\r\n            position: absolute;\r\n            z-index: -1;\r\n            @extend %liststyle;\r\n            &.shape-1 {\r\n                bottom: -100px;\r\n                right: 33%;\r\n                @media #{$laptop-device} {\r\n                    right: 27%;\r\n                }\r\n                @media #{$smlg-device} {\r\n                    right: 20%;\r\n                }\r\n                @media only screen and (max-width: 991px) {\r\n                    display: none;\r\n                }\r\n            }\r\n            &.shape-2 {\r\n                bottom: -65px;\r\n                right: 2%;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.main-slider-style-2 {\r\n    .slider-offset-left {\r\n        margin-left: 290px;\r\n        margin-top: 40px;\r\n        margin-bottom: 40px;\r\n        @media #{$smlg-device} {\r\n            margin-left: 0;\r\n        }\r\n    }\r\n    .slider-box-wrap {\r\n        background-color: #f7f7f7;\r\n        border-radius: 6px;\r\n        padding: 48px 50px;\r\n        @media #{$large-mobile} {\r\n            padding: 40px 30px;\r\n\r\n        }\r\n        .single-slide {\r\n            display: flex;\r\n            align-items: center;\r\n        }\r\n        .axil-slick-dots {\r\n            .slick-dots {\r\n                bottom: -25px;\r\n                @media #{$small-mobile} {\r\n                    bottom: -30px;\r\n                }\r\n                li {\r\n                    button {\r\n                        background-color: var(--color-white);\r\n                        opacity: 1;\r\n                    }\r\n                    &.slick-active {\r\n                        button {\r\n                            background-color: var(--color-secondary);\r\n                            opacity: 1;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n    }\r\n    .main-slider-content {\r\n        flex: 1;\r\n        z-index: 2;\r\n        position: relative;\r\n        @media #{$sm-layout} {\r\n            flex: 2;\r\n        }\r\n        .title {\r\n            font-size: 50px;\r\n            margin-bottom: 45px;\r\n            @media #{$smlg-device} {\r\n                font-size: 40px;\r\n            }\r\n            @media #{$sm-layout} {\r\n                font-size: 38px;\r\n                margin-bottom: 30px;\r\n            }\r\n            @media #{$large-mobile} {\r\n                font-size: 24px;\r\n                margin-bottom: 20px;\r\n            }\r\n        }\r\n        .axil-btn {\r\n            padding: 0;\r\n            position: relative;\r\n            i {\r\n                margin: 0 0 0 16px;\r\n                color: var(--color-heading);\r\n            }\r\n            &:after {\r\n                content: \"\";\r\n                height: 1px;\r\n                width: 100%;\r\n                background-color: var(--color-heading);\r\n                position: absolute;\r\n                bottom: 0;\r\n                left: 0;\r\n            }\r\n            &:hover {\r\n                color: var(--color-primary);\r\n                i {\r\n                    color: var(--color-primary);\r\n                    margin: 0 0 0 10px;\r\n                }\r\n                &:after {\r\n                    background-color: var(--color-primary);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .main-slider-thumb {\r\n        position: relative;\r\n        margin-left: 30px;\r\n        z-index: 1;\r\n        flex: 1;\r\n        text-align: right;\r\n        @media #{$small-mobile} {\r\n            margin-left: 10px;\r\n        }\r\n        &::after {\r\n            content: \"\";\r\n            height: 288px;\r\n            width: 288px;\r\n            background-color: var(--color-white);\r\n            border-radius: 50%;\r\n            position: absolute;\r\n            top: -5px;\r\n            left: -100px;\r\n            z-index: -1;\r\n            @media #{$sm-layout} {\r\n                height: 150px;\r\n                width: 150px;\r\n                left: 0;\r\n                top: 0;\r\n            }\r\n            @media #{$large-mobile} {\r\n                display: none;\r\n            }\r\n        }\r\n        img {\r\n            display: inline-block;\r\n            max-height: 292px;\r\n        }\r\n\r\n    }\r\n    .slider-product-box {\r\n        background-color: #f7f7f7;\r\n        border-radius: 6px;\r\n        text-align: center;\r\n        padding: 45px 20px;\r\n        overflow: hidden;\r\n        @media only screen and (max-width: 991px) {\r\n            margin-top: 30px;\r\n        }\r\n        .product-thumb {\r\n            margin-bottom: 35px;\r\n            position: relative;\r\n            z-index: 1;\r\n            &::after {\r\n                content: \"\";\r\n                height: 160px;\r\n                width: 160px;\r\n                background-color: var(--color-white);\r\n                border-radius: 50%;\r\n                position: absolute;\r\n                top: 20px;\r\n                right: -10px;\r\n                z-index: -1;\r\n                @media only screen and (max-width: 991px) {\r\n                    left: 50%;\r\n                    transform: translateX(-50%);\r\n                }\r\n            }\r\n        }\r\n        .title {\r\n            font-size: 16px;\r\n            color: var(--color-body);\r\n            margin-bottom: 8px;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n            display: -webkit-box;\r\n            -webkit-line-clamp: 1;\r\n            -webkit-box-orient: vertical;\r\n        }\r\n        .price {\r\n            font-size: 20px;\r\n            font-weight: 700;\r\n            color: var(--color-heading);\r\n        }\r\n    }\r\n}\r\n\r\n.main-slider-style-3 {\r\n    background-color: #f3f7f6;\r\n    padding: 110px 0;\r\n    background-image: url('../../assets/images/bg/bg-image-4.jpg');\r\n    background-repeat: no-repeat;\r\n    background-size: cover;\r\n    @media #{$md-layout} {\r\n        padding: 80px 0;\r\n    }\r\n    @media #{$sm-layout} {\r\n        padding: 40px 0 70px;\r\n    }\r\n    .main-slider-content {\r\n        padding-right: 80px;\r\n        @media only screen and (max-width: 991px) {\r\n            padding-right: 0;\r\n            margin-bottom: 50px;\r\n        }\r\n        @media #{$sm-layout} {\r\n            margin-bottom: 25px;\r\n        }\r\n        .title {\r\n            margin-bottom: 40px;\r\n            font-size: 55px;\r\n            line-height: 1.1;\r\n            @media #{$laptop-device} {\r\n                font-size: 60px;\r\n            }\r\n            @media #{$smlg-device} {\r\n                font-size: 50px;\r\n            }\r\n            @media #{$md-layout} {\r\n                font-size: 40px;\r\n            }\r\n            @media #{$sm-layout} {\r\n                font-size: 34px;\r\n            }\r\n\r\n        }\r\n    }\r\n    .main-slider-large-thumb {\r\n        width: 100%;\r\n        position: relative;\r\n        z-index: 1;\r\n        .single-slide {\r\n            text-align: center;\r\n            .axil-product {\r\n                transform: scale(.8);\r\n                transition: .3s;\r\n                margin: 45px -26px;\r\n                position: relative;\r\n                @media #{$smlg-device} {\r\n                    margin: 45px -19px;\r\n                }\r\n                @media #{$large-mobile} {\r\n                    margin: 20px 0;\r\n                }\r\n\r\n            }\r\n            &.slick-active {\r\n                &.slick-center {\r\n                    z-index: 1;\r\n                    .axil-product {\r\n                        transform: scale(1.1);\r\n                        box-shadow: 0px 10px 80px -87px rgba(0, 0, 0, .5);\r\n                        @media #{$large-mobile} {\r\n                            transform: scale(1);\r\n                        }\r\n                        &:before {\r\n                            content: \"\";\r\n                            width: 100%;\r\n                            height: 50px;\r\n                            background: #000000;\r\n                            position: absolute;\r\n                            left: 0;\r\n                            top: 50%;\r\n                            filter: blur(100px);\r\n                            transform: translateY(-50%);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        \r\n        .single-slide.slick-active:not(.slick-current) {\r\n            img {\r\n                width: 100%;\r\n            }\r\n        }\r\n        .axil-slick-dots {\r\n            .slick-dots {\r\n                text-align: center;\r\n                bottom: -30px;\r\n                li {\r\n                    height: 10px;\r\n                    width: 10px;\r\n                    margin: 0 8px;\r\n                    button {\r\n                        height: 6px;\r\n                        width: 6px;\r\n                        border-radius: 50%;\r\n                        background-color: transparent;\r\n                        box-shadow: inset 0 0 0 5px rgba(0, 0, 0, .5);\r\n                    }\r\n                    &.slick-active {\r\n                        button {\r\n                            transform: scale(2);\r\n                            box-shadow: inset 0 0 0 1px var(--color-primary);\r\n                           \r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.main-slider-style-4 {\r\n    background-color: #F9F3EF;\r\n    padding-top: 50px;\r\n    overflow: hidden;\r\n    min-height: 600px;\r\n    display: flex;\r\n    align-items: flex-end;\r\n    @media (max-width: 1399px) {\r\n        min-height: 100%;\r\n    }\r\n    @media (max-width: 767px) {\r\n        text-align: center;\r\n    }\r\n    .main-slider-content {\r\n        z-index: 2;\r\n        position: relative;\r\n        padding: 10px 0;\r\n        .title {\r\n            line-height: 1.1;\r\n            margin-bottom: 40px;\r\n            animation: 600ms ease-in-out 0s normal none 1 running customOne;\r\n        }\r\n        .shop-btn {\r\n            animation: 800ms ease-in-out 0s normal none 1 running customOne;\r\n            a {\r\n                @media (max-width: 991px) {\r\n                    padding: 12px 20px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .slide-thumb-area {\r\n        position: relative;\r\n        margin-right: -100px;\r\n        margin-left: -100px;\r\n        z-index: 1;\r\n        @media (max-width: 991px) {\r\n            margin-right: -30px;\r\n        }\r\n        @media (max-width:767px) {\r\n            margin-right: 0;\r\n            padding: 20px 0 0;\r\n            margin-left: 0;\r\n        }\r\n\r\n        .main-thumb {\r\n            animation: 1200ms ease-in-out 0s normal none 1 running customTwo;\r\n        }\r\n        .shape-group {\r\n            margin: 0;\r\n            list-style: none;\r\n            padding: 0;\r\n            li {\r\n                position: absolute;\r\n                margin: 0;\r\n                z-index: -1;\r\n                transition: all 0.5s ease-in-out;\r\n                visibility: hidden;\r\n                opacity: 0;\r\n                &.shape-1 {\r\n                    bottom: 0;\r\n                    right: -30px;\r\n                    display: none;\r\n                    svg {\r\n                        path {\r\n                            stroke-dasharray: 1190;\r\n                            stroke-dashoffset: 1180;\r\n                            transition: all 3s ease-in-out;\r\n                        }\r\n                    }\r\n                }\r\n                &.shape-2  {\r\n                    top: -50px;\r\n                    right: 50px;\r\n                    @media (max-width: 767px) {\r\n                        top: -250px;\r\n                    }\r\n                    svg {\r\n                        path {\r\n                            stroke-dasharray: 1190;\r\n                            stroke-dashoffset: 1180;\r\n                            transition: all 3s ease-in-out;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        .banner-product {\r\n            position: absolute;\r\n            left: 80px;\r\n            top: 220px;\r\n            // visibility: hidden;\r\n            // opacity: 0;\r\n            transition: 0.3s;\r\n            @media (max-width:767px) {\r\n                display: none;\r\n            }\r\n            &:hover {\r\n                .product-details {\r\n                    visibility: visible;\r\n                    opacity: 1;\r\n                    margin-bottom: 15px;\r\n                }\r\n            }\r\n            .plus-icon {\r\n                border: 1px solid var(--color-dark);\r\n                font-size: 12px;\r\n                color: var(--color-dark);\r\n                height: 29px;\r\n                width: 29px;\r\n                border-radius: 50%;\r\n                line-height: 29px;\r\n                text-align: center;\r\n                cursor: pointer;\r\n                transition: all 0.3s ease-in-out;\r\n                &:hover {\r\n                    background-color: #F4E7DE;\r\n                }\r\n\r\n            }\r\n            .product-details {\r\n                min-width: 250px;\r\n                width: 100%;\r\n                position: absolute;\r\n                background-color: var(--color-white);\r\n                box-shadow: 0px 54px 94px rgba(172, 128, 117, 0.2);\r\n                border-radius: 8px;\r\n                bottom: 100%;\r\n                left: -46px;\r\n                padding: 15px 20px;\r\n                margin-bottom: 30px;\r\n                visibility: hidden;\r\n                opacity: 0;\r\n                transition: 0.3s;\r\n                .title {\r\n                    margin-bottom: 0;\r\n                    font-size: 18px;\r\n                    a {\r\n                        transition: all 0.3s ease-in-out;\r\n                    }\r\n                }\r\n                .price {\r\n                    color: var(--color-secondary);\r\n                    font-size: 22px;\r\n                    font-weight: 700;\r\n                }\r\n                .product-rating {\r\n                    margin-bottom: 5px;\r\n                    .icon {\r\n                        font-size: 16px;\r\n                        color: #FACC15;\r\n                    }\r\n                    .rating-number {\r\n                        font-size: 14px;\r\n                        font-weight: 500;\r\n                        color: var(--color-body);\r\n                        margin-left: 5px;\r\n                    }\r\n                }\r\n                &:after {\r\n                    content: \"\";\r\n                    width: 0; \r\n                    height: 0; \r\n                    border-left: 12px solid transparent;\r\n                    border-right: 12px solid transparent;\r\n                    border-top: 12px solid var(--color-white);\r\n                    position: absolute;\r\n                    bottom: -12px;\r\n                    left: 52px;\r\n                }\r\n            }\r\n        }\r\n        &:hover {\r\n            .banner-product {\r\n                visibility: visible;\r\n                opacity: 1;\r\n            }\r\n        }\r\n    }\r\n    &.animation-init {\r\n        .slide-thumb-area {\r\n            .shape-group {\r\n                li {\r\n                    visibility: visible;\r\n                    opacity: 1;\r\n                    &.shape-1  {\r\n                        svg {\r\n                            path {\r\n                                stroke-dashoffset: 0;\r\n                                stroke-dasharray: 2000;\r\n                            }\r\n                        }\r\n                    }\r\n                    &.shape-2  {\r\n                        svg {\r\n                            path {\r\n                                stroke-dashoffset: 0;\r\n                                stroke-dasharray: 1300;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.main-slider-style-5 {\r\n    \r\n    .slider-box-wrap {\r\n        background-color: #f7f7f7;\r\n        border-radius: 6px;\r\n        padding: 90px 100px;\r\n        position: relative;\r\n        z-index: 1;\r\n        @media #{$smlg-device} {\r\n            padding: 80px;\r\n        }\r\n        @media #{$md-layout} {\r\n            padding: 60px;\r\n        }\r\n        @media #{$sm-layout} {\r\n            padding: 50px 50px 60px;\r\n        }\r\n        @media #{$small-mobile} {\r\n            padding: 40px 40px 60px;\r\n        }\r\n        &::after {\r\n            content: \"\";\r\n            height: 260px;\r\n            width: 260px;\r\n            background-color: var(--color-white);\r\n            border-radius: 50%;\r\n            position: absolute;\r\n            top: 80px;\r\n            left: 215px;\r\n            z-index: -1;\r\n            @media #{$md-layout} {\r\n               left: 0;\r\n               top: 0;\r\n               transform: scale(.6);\r\n            }\r\n            @media #{$sm-layout} {\r\n                display: none;\r\n            }\r\n        }\r\n        .slider-activation-two {\r\n            margin: -30px;\r\n        }\r\n        .single-slide {\r\n            display: flex;\r\n            align-items: center;\r\n            padding: 30px;\r\n            @media #{$large-mobile} {\r\n                display: block;\r\n            }\r\n        }\r\n        .axil-slick-dots {\r\n            .slick-dots {\r\n                bottom: -15px;\r\n            }\r\n        }\r\n    }\r\n    .main-slider-content {\r\n        flex: 1;\r\n        .title {\r\n            width: 70%;\r\n            @media only screen and (max-width: 1399px) {\r\n                width: 90%;\r\n            }\r\n            @media #{$smlg-device} {\r\n                width: 100%;\r\n            }\r\n            @media #{$sm-layout} {\r\n                margin-bottom: 35px;\r\n            }\r\n        }\r\n    }\r\n    .main-slider-thumb {\r\n        margin-left: 30px;\r\n        flex: 1;\r\n        text-align: right;\r\n        img {\r\n            display: inline-block;\r\n            min-height: 460px;\r\n            max-height: 460px;\r\n            object-fit: contain;\r\n            @media #{$smlg-device} {\r\n                min-height: 300px;\r\n                max-height: 300px;\r\n            }\r\n            @media #{$large-mobile} {\r\n                min-height: 165px;\r\n                max-height: 165px;\r\n            }\r\n\r\n        }\r\n        @media #{$large-mobile} {\r\n            text-align: center;\r\n            margin-left: 0;\r\n            margin-top: 45px;\r\n        }\r\n    }\r\n}\r\n\r\n.main-slider-style-7 {\r\n    padding: 200px 0;\r\n    background-size: cover;\r\n    background-position: center;\r\n    background-repeat: no-repeat;\r\n    @media only screen and (max-width: 991px) {\r\n        padding: 80px 0;\r\n    }\r\n    @media #{$large-mobile} {\r\n        padding: 60px 0;\r\n    }\r\n    @media #{$small-mobile} {\r\n        background-position: right;\r\n    }\r\n    .main-slider-content {\r\n        z-index: 2;\r\n        position: relative;\r\n        padding: 10px 0;\r\n        .subtitle {\r\n            animation: 800ms ease-in-out 0s normal none 1 running customOne;\r\n        }\r\n        .title {\r\n            width: 80%;\r\n            margin-bottom: 20px;\r\n            animation: 600ms ease-in-out 0s normal none 1 running customOne;\r\n            @media only screen and (max-width: 991px) {\r\n                width: 90%;\r\n            }\r\n        }\r\n        p {\r\n            font-size: 20px;\r\n            animation: 1200ms ease-in-out 0s normal none 1 running customOne;\r\n            @media #{$sm-layout} {\r\n                font-size: 16px;\r\n            }\r\n        }\r\n        .shop-btn {\r\n            animation: 1400ms ease-in-out 0s normal none 1 running customOne;\r\n        }\r\n    }\r\n}\r\n\r\n.main-slider-style-8 {\r\n    .slider-offset-left {\r\n        margin-bottom: 0;\r\n    }\r\n    .slider-box-wrap {\r\n        // background-color: #ffefe9;\r\n        padding: 48px 70px;\r\n        @media only screen and (max-width: 575px) {\r\n            padding: 45px 30px;\r\n            \r\n        }\r\n    }\r\n    .main-slider-thumb {\r\n        &:after {\r\n            left: -50px;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n.slick-slide.slick-current {\r\n    .main-slider-content {\r\n        .subtitle {\r\n            animation: 800ms ease-in-out 0s normal none 1 running customOne;\r\n        }\r\n        .title {\r\n            animation: 1000ms ease-in-out 0s normal none 1 running customOne;\r\n        }\r\n        .shop-btn {\r\n            animation: 1200ms ease-in-out 0s normal none 1 running customOne;\r\n        }\r\n    }\r\n    .main-slider-thumb {\r\n        img {\r\n            animation: 1200ms ease-in-out 0s normal none 1 running customTwo;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// Animation\r\n@keyframes customOne {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(0, 50%, 0);\r\n        transform: translate3d(0, 50%, 0);\r\n    }\r\n    to {\r\n        opacity: 1;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n}\r\n\r\n@keyframes customTwo {\r\n    from {\r\n        opacity: 0;\r\n        transform: translate3d(20%, 0, 0);\r\n    }\r\n    to {\r\n        opacity: 1;\r\n        transform: none;\r\n    }\r\n}\r\n\r\n.animationOne {\r\n    animation: 1200ms customOne;\r\n}\r\n\r\n.animationTwo {\r\n    animation: 1200ms customTwo;\r\n}\r\n\r\n\r\n\r\n", "/*-------------------------\r\nSocial Share\r\n--------------------------*/\r\n\r\n.social-share {\r\n    @extend %liststyle;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    margin: -10px;\r\n    a {\r\n        margin: 10px;\r\n        color:var(--color-body);\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        font-size: 18px;\r\n        transition: 0.3s;\r\n        position: relative;\r\n        z-index: 1;\r\n        &:after {\r\n            content: \"\";\r\n            height: 40px;\r\n            width: 40px;\r\n            background-color: var(--color-secondary);\r\n            transform: scale(0);\r\n            border-radius: 50%;\r\n            position: absolute;\r\n            z-index: -1;\r\n            transition: var(--transition);\r\n        }\r\n        &:hover {\r\n            color: var(--color-white);\r\n            &:after {\r\n                transform: scale(1);\r\n            }\r\n        }\r\n        \r\n    }\r\n}\r\n\r\n", "/*-------------------------\r\nTeam Style\r\n--------------------------*/\r\n.axil-team-area {\r\n\tpadding: 50px 0 55px;\r\n}\r\n.team-left-fullwidth {\r\n\tmargin-left: calc((100% - 1320px) / 2);\r\n    overflow: hidden;\r\n\t@media only screen and (max-width: 1349px) {\r\n\t\tmargin-left: auto;\r\n\t\t\r\n\t}\r\n\t.team-slide-activation {\r\n\t\t.slick-list {\r\n\t\t\toverflow: visible;\r\n\t\t\t@media only screen and (max-width: 1349px) {\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.axil-team-member {\r\n\t@media #{$large-mobile} {\r\n\t\ttext-align: center;\r\n\t}\r\n\t.thumbnail {\r\n\t\tmargin-bottom: 12px;\r\n\t\toverflow: hidden;\r\n\t\tdisplay: inline-block;\r\n\t\tborder-radius: 6px;\r\n\t\timg {\r\n\t\t\tborder-radius: 6px;\r\n\t\t\ttransition: var(--transition);\r\n\t\t}\r\n\t}\r\n\t.title {\r\n\t\tcolor: #292930;\r\n\t\tmargin-bottom: 0;\r\n\t}\r\n\t.subtitle {\r\n\t\tfont-size: var(--font-size-b2);\r\n\t}\r\n\t&:hover {\r\n\t\t.thumbnail {\r\n\t\t\timg {\r\n\t\t\t\ttransform: scale(1.1);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}", "/*-------------------------\r\nTestimonial Style\r\n--------------------------*/\r\n.testimonial-style-one-wrapper {\r\n    .slide-arrow {\r\n        background-color: var(--color-white);\r\n    }\r\n    .slick-slide {\r\n        // transition: all .3s;\r\n    }\r\n    .slick-current.slick-active + .slick-active {\r\n        margin-top: 30px;\r\n    }\r\n}\r\n.testimonial-style-one {\r\n    .review-speech {\r\n        background-color: var(--color-white);\r\n        border-radius: 10px;\r\n        padding: 40px;\r\n        margin-bottom: 40px;\r\n        position: relative;\r\n        box-shadow: 0 16px 32px 0 rgba(0,0,0,.04);\r\n        &:after {\r\n            content: \"\";\r\n            width: 0;\r\n            height: 0;\r\n            border-top: 25px solid var(--color-white);\r\n            border-right: 50px solid transparent;\r\n            position: absolute;\r\n            bottom: -25px;\r\n            left: 100px;\r\n        }\r\n        p {\r\n            font-size: 16px;\r\n            line-height: 28px;\r\n        }\r\n    }\r\n    .media {\r\n        display: flex;\r\n        align-items: center;\r\n        .thumbnail {\r\n            margin-right: 20px;\r\n            img {\r\n                border-radius: 6px;\r\n            }\r\n        }\r\n        .media-body {\r\n            flex: 1;\r\n            .designation {\r\n                font-size: 14px;\r\n            }\r\n            .title {\r\n                margin-bottom: 0;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.testimonial-style-two-wrapper {\r\n    margin-bottom: 130px !important;\r\n    .thumbnail {\r\n        display: inline-block;\r\n        border-radius: 50%;\r\n        border: 2px solid var(--color-tertiary);\r\n        padding: 8px;\r\n        margin-bottom: 25px;\r\n        transition: var(--transition);\r\n        position: relative;\r\n        z-index: 1;\r\n        margin: 6px;\r\n        &:before {\r\n            content: \"\";\r\n            height: 100%;\r\n            width: 100%;\r\n            background-color: var(--color-white);\r\n            border-radius: 50%;\r\n            position: absolute;\r\n            top: 0;\r\n            left: 0;\r\n            right: 0;\r\n            bottom: 0;\r\n            z-index: -1;\r\n            transform: scale(1);\r\n            transition: .3s;\r\n        }\r\n        img {\r\n            border-radius: 50%;\r\n        }\r\n    }\r\n\r\n    .thumb-content {\r\n        .item-title {\r\n            margin-bottom: 16px;\r\n        }\r\n        p {\r\n            font-size: 24px;\r\n            color: #292930;\r\n            @media #{$sm-layout} {\r\n                font-size: 18px;\r\n            }\r\n        }\r\n    }\r\n\r\n    .slick-single-layout {\r\n        &:hover {\r\n            .thumbnail {\r\n                &:before {\r\n                    transform: scale(1.2);\r\n                }\r\n                background-color: var(--color-tertiary);\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n.testimonial-container-box {\r\n    background-color: var(--color-lighter);\r\n    border-radius: 8px;\r\n}\r\n.testimonial-video-box {\r\n    position: relative;\r\n    margin-right: 22px;\r\n    height: 100%;\r\n    @media (max-width: 991px) {\r\n        margin-right: 0;\r\n    }\r\n    .thumbnail {\r\n        height: 100%;\r\n        img {\r\n            border-radius: 8px 0 0 8px;\r\n            height: 100%;\r\n            object-fit: cover;\r\n            @media (max-width: 991px) {\r\n               border-radius: 0;\r\n            }\r\n        }\r\n    }\r\n    .play-btn {\r\n        position: absolute;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n        left: 0;\r\n        right: 0;\r\n        text-align: center;\r\n        a {\r\n            height: 110px;\r\n            width: 110px;\r\n            line-height: 110px;\r\n            border-radius: 50%;\r\n            background-color: var(--color-secondary);\r\n            display: inline-block;\r\n            font-size: 28px;\r\n            color: var(--color-white);\r\n            transition: 0.3s;\r\n            &:hover {\r\n                background-color: var(--color-primary);\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.testimonial-style-three-wrapper {\r\n    padding-top: 100px;\r\n    padding-left: 50px;\r\n    padding-right: 50px;    \r\n    position: relative;\r\n    height: 100%;\r\n    @media (max-width: 1199px) {\r\n        padding-left: 0;\r\n    }\r\n    @media (max-width: 991px) {\r\n        padding-left: 50px;\r\n        padding-top: 80px;\r\n    }\r\n    @media (max-width: 767px) {\r\n        padding-left: 30px;\r\n        padding-right: 30px;\r\n    }\r\n    .heading-title {\r\n        position: relative;\r\n        margin-bottom: 45px;\r\n        z-index: 1;\r\n        &:before {\r\n            content: url(\"../../assets/images/testimonial/quote-left.png\");\r\n            height: 100%;\r\n            width: 100%;\r\n            position: absolute;\r\n            top: -58px;\r\n            left: -68px;\r\n            z-index: -1;\r\n            @media (max-width: 1199px) {\r\n                left: 0;\r\n            }\r\n        }\r\n        .title {\r\n            font-size: 48px;\r\n            font-weight: 700;\r\n            @media (max-width: 767px) {\r\n                font-size: 34px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.testimonial-style-three {\r\n    p {\r\n        font-size: 18px;\r\n        font-weight: 500;\r\n        margin-bottom: 45px;\r\n    }\r\n    .author-name {\r\n        margin-bottom: 0;\r\n    }\r\n    .author-desg {\r\n        font-size: 14px;\r\n    }\r\n}\r\n\r\n.testimonial-custom-nav {\r\n    padding-top: 94px;\r\n    padding-bottom: 40px;\r\n    @media (max-width: 991px) {\r\n        padding-top: 50px;\r\n    }\r\n    .slide-custom-nav {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: flex-end;\r\n        margin: 0 -12px;\r\n        button {\r\n            width: auto;\r\n            line-height: 1;\r\n            font-size: 16px;\r\n            font-weight: 500;\r\n            display: flex;\r\n            align-items: center;\r\n            color: var(--color-body);\r\n            background-color: transparent;\r\n            padding: 0 12px;\r\n            i {\r\n                margin-left: 8px;\r\n            }\r\n            &.prev-custom-nav {\r\n                border-right: 1px solid #D6D6D6;\r\n                i {\r\n                    margin-left: 0;\r\n                    margin-right: 8px;\r\n                }\r\n            }\r\n            &:hover {\r\n                color: var(--color-heading);\r\n            }\r\n        }\r\n    }\r\n\r\n    .slick-slide-count {\r\n        font-weight: 500;\r\n    }\r\n}", "/*-------------------------\r\nVideo Style\r\n--------------------------*/\r\n\r\n.video-banner {\r\n    position: relative;\r\n    &:after {\r\n        content: \"\";\r\n        height: 100%;\r\n        width: 100%;\r\n        background: radial-gradient(circle, rgba(0,0,0,0.4) 0%, rgba(0,0,0,0) 100%);\r\n        border-radius: 6px;\r\n        position: absolute;\r\n        top: 0;\r\n        bottom: 00;\r\n        left: 0;\r\n        right: 0;\r\n    }\r\n    img {\r\n        width: 100%;\r\n        border-radius: 6px;\r\n    }\r\n    .popup-video-icon {\r\n        text-align: center;\r\n        position: absolute;\r\n        top: 50%;\r\n        left: 0;\r\n        right: 0;\r\n        transform: translateY(-50%);\r\n        z-index: 1;\r\n        .video-icon {\r\n            height: 80px;\r\n            width: 80px;\r\n            line-height: 80px;\r\n            background-color: var(--color-white);\r\n            border-radius: 50%;\r\n            display: inline-block;\r\n            font-size: 24px;\r\n            color: var(--color-primary);\r\n            position: relative;\r\n            z-index: 1;\r\n            i {\r\n                position: relative;\r\n                left: 2px;\r\n                transition: var(--transition);\r\n            }\r\n            &:after {\r\n                content: \"\";\r\n                height: 100%;\r\n                width: 100%;\r\n                background-color: var(--color-white);\r\n                border-radius: 50%;\r\n                position: absolute;\r\n                top: 0;\r\n                left: 0;\r\n                right: 0;\r\n                bottom: 0;\r\n                z-index: -1;\r\n                transition: var(--transition);\r\n            }\r\n            &:hover {\r\n                i {\r\n                    transform: scale(.9);\r\n                }\r\n                &:after {\r\n                    transform: scale(1.1);\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n", "/*-----------------------\r\n    Splash Page Styles  \r\n-------------------------*/\r\n.pv-main-wrapper {\r\n    .section-title-wrapper {\r\n        padding-right: 0;\r\n    }\r\n}\r\n\r\n// Main Banner\r\n\r\n.pv-banner-wrapper {\r\n    margin-top: -120px;\r\n    background-image: url('../images/preview/banner-bg.png');\r\n    background-repeat: no-repeat;\r\n    background-size: cover;\r\n    background-position: center;\r\n    padding-top: 140px;\r\n    position: relative;\r\n    z-index: 1;\r\n    @media #{$md-layout} {\r\n        padding: 160px 0 60px;\r\n    }\r\n    \r\n}\r\n\r\n.pv-banner-area {\r\n    display: flex;\r\n    justify-content: center;\r\n    position: relative;\r\n    z-index: 2;\r\n    padding-left: calc((100% - 1290px) / 2);\r\n    @media only screen and (max-width: 1399px) {\r\n        padding-left: calc((100% - 1110px) / 2);\r\n        \r\n    }\r\n    @media only screen and (max-width: 1199px){\r\n        padding-left: calc((100% - 930px) / 2);\r\n        padding-bottom: 50px;\r\n    }\r\n    @media only screen and (max-width: 991px){\r\n        padding-left: calc((100% - 690px) / 2);\r\n        padding-right: calc((100% - 690px) / 2);\r\n        text-align: center;\r\n    }\r\n    @media only screen and (max-width: 767px){\r\n        padding-left: calc((100% - 510px) / 2);\r\n        padding-right: calc((100% - 510px) / 2);\r\n    }\r\n    .container-fluid {\r\n        padding: 0;\r\n    }\r\n    .inner {\r\n        @media #{$large-mobile} {\r\n            padding-top: 30px !important;\r\n        }\r\n        @media #{$sm-layout} {\r\n            padding-bottom: 26px;\r\n        }\r\n        .section-title-wrapper {\r\n            @media #{$sm-layout}{\r\n                margin-bottom: 0;\r\n            }\r\n        }\r\n        .title-highlighter {\r\n            margin-bottom: 16px;\r\n            @media only screen and (max-width: 991px){\r\n              justify-content: center;\r\n            }\r\n        }\r\n        .title {\r\n            margin-bottom: 50px;\r\n            font-size: 50px;\r\n            line-height: 1.3;\r\n            @media #{$laptop-device} {\r\n                font-size: 44px;\r\n            }\r\n            @media only screen and (max-width: 1399px) {\r\n                font-size: 36px;\r\n            }\r\n            @media #{$smlg-device} {\r\n                margin-bottom: 30px;\r\n            }\r\n            @media only screen and (max-width: 991px){\r\n                br {\r\n                    display: none;\r\n                }\r\n            }\r\n            @media #{$sm-layout}{\r\n                margin-bottom: 0;\r\n            }\r\n        }\r\n    }\r\n\r\n    .theme-brief {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: flex-start;\r\n        margin: 0 -30px;\r\n        @media only screen and (max-width: 991px){\r\n            justify-content: center;\r\n        }\r\n        .single-counter {\r\n            padding: 0 30px;\r\n            @media #{$large-mobile} {\r\n                padding: 0 5px;\r\n                \r\n            }\r\n            span {\r\n                &.subtile {\r\n                    margin-bottom: 10px;\r\n                    color: var(--color-heading);\r\n                    font-size: 16px;\r\n                    line-height: 24px;\r\n                    display: block;\r\n                }\r\n            }\r\n\r\n            h2 {\r\n                &.title {\r\n                    font-size: 48px;\r\n                    line-height: 1;\r\n                    color: var(--color-primary);\r\n                    display: inline-block;\r\n                    position: relative;\r\n                    min-width: 100px;\r\n                    &::after {\r\n                        content: \"+\";\r\n                        right: 0;\r\n                    }\r\n                    @media only screen and (max-width: 1399px) {\r\n                        font-size: 36px;\r\n                    }\r\n\r\n                    @media #{$md-layout} {\r\n                        font-size: 40px;\r\n                        line-height: 58px;\r\n                    }\r\n\r\n                    @media #{$sm-layout} {\r\n                        font-size: 30px;\r\n                        line-height: 40px;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .theme-powerd {\r\n        background-color: var(--color-white);\r\n        padding: 15px 25px;\r\n        border-radius: 10px;\r\n        display: inline-flex;\r\n        align-items: center;\r\n        box-shadow: 0px 4px 10px rgba(37, 47, 63, 0.1);\r\n        position: absolute;\r\n        bottom: -35px;\r\n        @media only screen and (max-width: 991px) {\r\n            position: initial;\r\n        }\r\n        label {\r\n            margin-right: 20px;\r\n            font-weight: 500;\r\n            color: var(--color-heading);\r\n        }\r\n        .icon-list {\r\n            display: inline-block;\r\n            list-style: none;\r\n            padding: 0;\r\n            margin: -10px -5px;\r\n            li {\r\n                text-align: center;\r\n                display: inline-block;\r\n                margin: 10px 5px;\r\n                background-color: var(--color-lighter);\r\n                border-radius: 50%;\r\n                height: 50px;\r\n                width: 50px;\r\n                line-height: 50px;\r\n                img {\r\n                    width: 25px;\r\n                    height: auto;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .banner-thumbnail {\r\n        margin-bottom: -120px;\r\n        @media only screen and (max-width: 991px) {\r\n            margin-bottom: 0;\r\n            padding-top: 50px;\r\n        }\r\n    }\r\n}\r\n\r\n// Demo\r\n.pv-demo-area {\r\n    background-color: #F6F7FB;\r\n    padding: 140px 0 80px;\r\n    .section-title-wrapper {\r\n        margin-bottom: 0;\r\n        .title {\r\n            margin-bottom: 0;\r\n        }\r\n    }\r\n    @media #{$md-layout} {\r\n        padding: 80px 0;\r\n    }\r\n    @media #{$sm-layout} {\r\n        padding: 80px 0;\r\n    }\r\n}\r\n\r\n.pv-inner-demo-area {\r\n    .section-title-wrapper {\r\n        margin-bottom: 0;\r\n        .title {\r\n            margin-bottom: 0;\r\n        }\r\n    }\r\n}\r\n\r\n.pv-single-demo {\r\n    margin-top: 80px;\r\n    text-align: center;\r\n    box-shadow: 0 0 1px transparent;\r\n    position: relative;\r\n    @media #{$lg-layout} {\r\n        margin-top: 40px;\r\n    }\r\n    @media #{$laptop-device} {\r\n        margin-top: 40px;\r\n    }\r\n    @media #{$sm-layout} {\r\n        margin-top: 40px;\r\n    }\r\n    @media #{$md-layout} {\r\n        margin-top: 40px;\r\n    }\r\n    .thumb {\r\n        position: relative;\r\n        overflow: hidden;\r\n        box-shadow:  0px 24px 24px -16px rgba(15, 15, 15, 0.2);\r\n        border-radius: 6px;\r\n        img {\r\n            max-width: 100%;\r\n            width: 100%;\r\n            border-radius: 6px;\r\n            transform: scale(1);\r\n            transition: all 0.5s ease-in-out;\r\n        }\r\n        .view-btn {\r\n            position: absolute;\r\n            top: 55%;\r\n            left: 50%;\r\n            transform: translateY(-50%) translateX(-50%);\r\n            visibility: hidden;\r\n            opacity: 0;\r\n        }\r\n        &:after {\r\n            content: \"\";\r\n            height: 100%;\r\n            width: 100%;\r\n            background-color: rgba(0,0,0,0.5);\r\n            border-radius: 6px;\r\n            position: absolute;\r\n            top: 0;\r\n            left: 0;\r\n            right: 0;\r\n            bottom: 0;\r\n            visibility: hidden;\r\n            opacity: 0;\r\n            transition: all 0.3s ease-in-out;\r\n        }\r\n    }\r\n    \r\n    .title {\r\n        font-weight: 500;\r\n        margin-top: 35px;\r\n        margin-bottom: 0;\r\n        @extend %transition;\r\n        a {\r\n            text-decoration: none;\r\n            span {\r\n                background-color: var(--color-secondary);\r\n                color: var(--color-white);\r\n                padding: 2px 12px 4px;\r\n                margin-left: 5px;\r\n                border-radius: 4px;\r\n                font-size: 16px;\r\n                display: inline-block;\r\n            }\r\n        }\r\n        &:hover {\r\n            color: var(--color-primary);\r\n        }\r\n        @media #{$sm-layout} {\r\n            margin-top: 15px;\r\n            font-size: 18px;\r\n        }\r\n    }\r\n  \r\n    &:hover {\r\n        .thumb {\r\n            &::after {\r\n                visibility: visible;\r\n                opacity: 1;\r\n            }\r\n            .view-btn {\r\n                top: 50%;\r\n                opacity: 1;\r\n                visibility: visible;\r\n            }\r\n        }\r\n    }\r\n    &.commin {\r\n        &:hover {\r\n          .thumb {\r\n              &::after {\r\n                  visibility: hidden;\r\n              }\r\n          }\r\n        }\r\n    }\r\n}\r\n\r\n// Features\r\n\r\n.pv-feature-area {\r\n    padding-bottom: 60px;\r\n}\r\n.pv-feature-box {\r\n    border-top: 1px solid #f1f1f1;\r\n    padding-top: 80px;\r\n}\r\n\r\n.pv-feature {\r\n    text-align: center;\r\n    height: 100%;\r\n    padding-bottom: 30px;\r\n    .service-box {\r\n        height: 100%;\r\n        margin-bottom: 0;\r\n        .title {\r\n            font-size: 24px;\r\n            line-height: 1.2;\r\n            margin-bottom: 15px;\r\n        }\r\n\r\n    }\r\n   \r\n}\r\n\r\n\r\n// Support\r\n.pv-support-area {\r\n    margin-bottom: -134px;\r\n}\r\n.pv-support {\r\n    padding-bottom: 30px;\r\n    height: 100%;\r\n    .inner {\r\n        height: 100%;\r\n        border-radius: 10px;\r\n        padding: 50px 40px;\r\n        transition: all 0.3s ease-in-out;\r\n        display: flex;\r\n        box-shadow: 0px 24px 24px -16px rgba(15, 15, 15, .20);\r\n        @media #{$large-mobile} {\r\n            padding: 30px 20px;\r\n        }\r\n        .icon {\r\n            margin-top: 2px;\r\n            text-align: center;\r\n            position: relative;\r\n            height: 48px;\r\n            width: 48px;\r\n            line-height: 48px;\r\n            border-radius: 50%;\r\n            z-index: 2;\r\n            background-color: var(--color-primary);\r\n            margin-bottom: 30px;\r\n            display: block;\r\n            margin-right: 20px;\r\n            i {\r\n                font-size: 18px;\r\n                color: var(--color-secondary);\r\n            }\r\n            img {\r\n                max-height: 40px;\r\n            }\r\n        }\r\n\r\n        .content {\r\n           flex: 1;\r\n            .title {\r\n                margin-bottom: 10px;\r\n                color: var(--color-white);\r\n                display: block;\r\n            }\r\n            .paragraph {\r\n                color: var(--color-white);\r\n                margin-bottom: 30px;\r\n                display: block;\r\n            }\r\n            .axil-btn {\r\n                font-size: var(--font-size-b1);\r\n                line-height: var(--line-height-b1);\r\n                color: var(--color-white);\r\n                font-weight: 700;\r\n                display: inline-block;\r\n                z-index: 1;\r\n                transition: 0.3s;\r\n                i {\r\n                    margin-left: 10px;\r\n                    position: relative;\r\n                    top: 1px;\r\n                    transition: .3s;\r\n                }\r\n                &:hover {\r\n                    i {\r\n                        margin-left: 15px;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        \r\n    }\r\n    &.online-documentation {\r\n        .inner {\r\n            background: #8E2DE2;  \r\n            background: -webkit-linear-gradient(to right, #4A00E0, #8E2DE2);  \r\n            background: linear-gradient(to right, #4A00E0, #8E2DE2); \r\n            background-position: center center;\r\n            background-size: cover;\r\n            background-repeat: no-repeat;\r\n            .icon {\r\n                background-color: var(--color-white);\r\n\r\n            }\r\n        }\r\n    }\r\n    &.datecated-support {\r\n        .inner {\r\n            background: #FC5C7D;\r\n            background: -webkit-linear-gradient(to right, #6A82FB, #FC5C7D); \r\n            background: linear-gradient(to right, #6A82FB, #FC5C7D);\r\n            .icon {\r\n                background-color: var(--color-white);\r\n\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// Call To Action\r\n\r\n.pv-call-to-action {\r\n    background-color: #F6F7FB;\r\n}\r\n\r\n.pv-action-box {\r\n    padding: 185px 30px 0;\r\n    text-align: center;\r\n\r\n    .section-title-wrapper {\r\n        margin-bottom: 40px;\r\n    }\r\n    .title-highlighter {\r\n        justify-content: center;\r\n    }\r\n    .pv-action-thumbnail {\r\n        transform: translateY(20px);\r\n        transition: .3s;\r\n        &:hover {\r\n            transform: translateY(0);\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// Footer\r\n\r\n.pv-footer-area {\r\n    background-color: var(--color-white);\r\n    padding: 10px 0;\r\n    position: relative;\r\n    z-index: 2;\r\n    .copyright-default .quick-link li::after {\r\n        display: none;\r\n    }\r\n}\r\n", "/*------------------------------\r\n    Header Campaign Styles  \r\n-------------------------------*/\r\n.header-top-campaign {\r\n    background-image: url('../images/others/campaign-bg2.png');\r\n    background-position: center;\r\n    background-size: cover;\r\n    background-repeat: no-repeat;\r\n    padding: 8px 0;\r\n    position: relative;\r\n    .campaign-content {\r\n        text-align: center;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        position: relative;\r\n        margin: 0 30px;\r\n        @media #{$sm-layout} {\r\n            display: block;\r\n        }\r\n        p {\r\n            color: #eeb2ff;\r\n            font-size: 14px;\r\n            a {\r\n                font-weight: 700;\r\n                color: var(--color-white);\r\n                text-decoration: underline;\r\n                transition: all .3s ease-in-out;\r\n                &:hover {\r\n                    color: var(--color-secondary);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .campaign-countdown {\r\n        display: flex;\r\n        align-items: center;\r\n        margin: 0 -5px;\r\n        padding-right: 30px;\r\n        @media #{$sm-layout} {\r\n           justify-content: center;\r\n           padding-right: 0;\r\n        }\r\n        .countdown-section {\r\n            margin: 0 5px;\r\n            position: relative;\r\n            &::after {\r\n                content: \"\";\r\n                height: 14px;\r\n                width: 1px;\r\n                background-color: #c653e2;\r\n                position: absolute;\r\n                top: 50%;\r\n                right: -7px;\r\n                transform: translateY(-50%);\r\n            }\r\n            &:last-child {\r\n                &::after {\r\n                    display: none;\r\n                }\r\n            }\r\n            >div {\r\n                display: flex;\r\n                align-items: center;\r\n            }\r\n            .countdown-number, .countdown-unit {\r\n                font-size: 14px;\r\n                font-weight: 700;\r\n                color: var(--color-white);\r\n            }\r\n        }\r\n    }\r\n    .remove-campaign {\r\n        position: absolute;\r\n        top: 11px;\r\n        right: 30px;\r\n        background-color: transparent;\r\n        color: var(--color-white);\r\n        width: auto;\r\n        font-size: 14px;\r\n        &:hover {\r\n            color: var(--color-primary);\r\n        }\r\n        @media only screen and (max-width: 991px) {\r\n            right: 10px;\r\n        }\r\n    }\r\n}\r\n\r\n/*------------------------------\r\n    Header Top Styles  \r\n-------------------------------*/\r\n.axil-header-top {\r\n    padding: 8px 0;\r\n}\r\n\r\n.header-top-dropdown {\r\n    display: flex;\r\n    align-items: center;\r\n    @media #{$large-mobile} {\r\n        justify-content: center;\r\n    }\r\n    .dropdown {\r\n        max-width: 70px;\r\n        margin-right: 20px;\r\n        @media #{$large-mobile} {\r\n            margin-right: 10px;\r\n        }\r\n        &:last-child {\r\n            margin-right: 0;\r\n        }\r\n        .dropdown-toggle {\r\n            font-size: 14px;\r\n            color: var(--color-body);\r\n            background-color: transparent;\r\n            display: flex;\r\n            align-items: center;\r\n            &:after {\r\n                content: \"\\f107\";\r\n                font-family: var(--font-awesome);\r\n                border: none;\r\n                margin-left: 5px;\r\n                margin-top: 1px;\r\n            }\r\n        }\r\n        .dropdown-menu {\r\n            min-width: 11rem;\r\n            border: none;\r\n            box-shadow: var(--shadow-primary);\r\n            display: block !important;\r\n            top: 35px;\r\n            visibility: hidden;\r\n            opacity: 0;\r\n            transition: var(--transition);\r\n            li {\r\n                margin: 5px 10px;\r\n            }\r\n            .dropdown-item {\r\n                font-size: 14px;\r\n                border-radius: 6px;\r\n                &:hover {\r\n                    background-color: var(--color-secondary);\r\n                    color: var(--color-white);\r\n                }\r\n            }\r\n            &.show {\r\n                visibility: visible;\r\n                opacity: 1;\r\n                top: 30px;\r\n            }\r\n        }\r\n    }\r\n    &.dropdown-box-style {\r\n        .dropdown {\r\n            max-width: 100%;\r\n            .dropdown-toggle {\r\n                color: var(--color-heading);\r\n                font-weight: 700;\r\n                border: 1px solid #f0f0f0;\r\n                border-radius: 6px;\r\n                padding: 10px 20px;\r\n                justify-content: center;\r\n               &:after {\r\n                   font-weight: 400;\r\n               }\r\n               &:hover {\r\n                   box-shadow: 0 16px 32px 0 rgba(0, 0, 0, .06);\r\n               }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.header-top-link {\r\n    .quick-link {\r\n        @extend %liststyle;\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        margin: 0 -18px;\r\n        justify-content: flex-end;\r\n        @media #{$large-mobile} {\r\n            justify-content: center;\r\n            margin: 5px 0;\r\n        }\r\n        li {\r\n            margin: 0;\r\n            padding: 0 18px;\r\n            position: relative;\r\n            a {\r\n                font-size: var(--font-size-b2);\r\n                color: var(--color-body);\r\n                line-height: var(--line-height-b2);\r\n                display: inline-block;\r\n                &:hover {\r\n                    color: var(--color-primary);\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.header-style-1 {\r\n    .header-top-campaign {\r\n        background-image: url('../images/others/campaign-bg.png');\r\n        .campaign-content {\r\n            p {\r\n                color: var(--color-white);\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.header-style-2 {\r\n    .axil-header-top {\r\n        padding: 25px 0;\r\n        .axil-search {\r\n            flex: 1;\r\n            position: relative;\r\n            margin-left: 70px;\r\n            margin-right: 20px;\r\n            @media only screen and (max-width: 991px) {\r\n                margin-left: 0;\r\n            }\r\n            @media #{$large-mobile} {\r\n               display: none;\r\n            }\r\n            .icon {\r\n                width: auto;\r\n                position: absolute;\r\n                top: 10px;\r\n                left: 15px;\r\n                background-color: transparent;\r\n                font-size: 14px;\r\n                color: var(--color-heading);\r\n                z-index: 1;\r\n                &:hover {\r\n                    color: var(--color-primary);\r\n                }\r\n\r\n            }\r\n            input {\r\n                width: 100%;\r\n                background-color: transparent;\r\n                border: 1px solid #d4d4d4;\r\n                border-radius: 6px;\r\n                font-size: 14px;\r\n                padding-left: 50px;\r\n                color: var(--color-heading);\r\n                font-weight: 500;\r\n                &:focus {\r\n                    border-color: var(--color-primary);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .header-top-dropdown {\r\n        @media #{$large-mobile} {\r\n            justify-content: flex-end;\r\n        }\r\n    }\r\n}\r\n\r\n.header-style-4 {\r\n    .axil-header-top {\r\n        border-bottom: 1px solid #f1f1f1;\r\n        padding: 25px 0;\r\n        @media only screen and (max-width: 991px) {\r\n            padding: 15px 0;   \r\n            border-bottom: none;\r\n        }\r\n        .header-brand {\r\n            text-align: center;\r\n            @media #{$sm-layout} {\r\n                text-align: left;\r\n            }\r\n        }\r\n        .header-action {\r\n            >ul {\r\n                justify-content: flex-end;\r\n\r\n            }\r\n        }\r\n        .header-top-dropdown {\r\n            @media #{$sm-layout} {\r\n                justify-content: center;\r\n                border-bottom: 1px solid #f1f1f1;\r\n                padding-bottom: 15px;\r\n                margin-bottom: 15px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.header-style-5 {\r\n    .axil-header-top {\r\n        background-color: var(--color-heading);\r\n        padding: 6px 0;\r\n    }\r\n    .header-top-dropdown {\r\n        .dropdown {\r\n            .dropdown-toggle {\r\n                color: #c2c2cc;\r\n                &:hover {\r\n                    color: var(--color-white);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .header-top-link {\r\n        .quick-link {\r\n            li {\r\n                a {\r\n                    color: #c2c2cc;\r\n                    &:hover {\r\n                        color: var(--color-primary);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .header-top-campaign {\r\n        background-image: url('../images/others/campaign-bg3.png');\r\n        padding: 20px 0 18px;\r\n        .campaign-content {\r\n            p {\r\n                font-size: 16px;\r\n                font-weight: 700;\r\n                color: var(--color-white);\r\n                a {\r\n                    i {\r\n                        margin-left: 15px;\r\n                    }\r\n                }\r\n                @media #{$small-mobile} {\r\n                    font-size: 12px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.header-style-7 {\r\n    .axil-header-top {\r\n        background-color: var(--color-heading);\r\n        padding: 7px 45px;\r\n        @media (max-width: 991px) {\r\n            padding: 7px 0;\r\n        }\r\n        @media (max-width: 767px) {\r\n           text-align: center;\r\n        }\r\n    }\r\n    .header-top-text {\r\n        p {\r\n            font-size: 14px;\r\n            color: var(--color-white);\r\n            i {\r\n                color: #FACC15;\r\n                margin-right: 5px;\r\n            }\r\n        }\r\n    }\r\n    .header-top-link {\r\n        .quick-link {\r\n            @media (max-width: 767px) {\r\n                justify-content: center;\r\n            }\r\n            li {\r\n                a {\r\n                    color: var(--color-white);   \r\n                    &:hover {\r\n                        color: var(--color-secondary);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}", "/*-----------------------\r\n    Header Styles  \r\n-------------------------*/\r\n.header-brand {\r\n    a {\r\n        display: block;\r\n        img {\r\n            @media only screen and (max-width: 991px) {\r\n               max-height: 35px;\r\n            }\r\n            @media only screen and (max-width: 320px) {\r\n               max-height: 30px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.logo-light {\r\n    display: none !important;\r\n}\r\n\r\n.axil-mainmenu {\r\n    .header-navbar {\r\n        display: flex;\r\n        align-items: center;\r\n        width: 100%;\r\n        .header-main-nav {\r\n            flex: 1;\r\n            margin: 0 50px;\r\n            @media only screen and (max-width: 991px) {\r\n                margin: 0;\r\n            }\r\n        }\r\n    }\r\n    &.axil-sticky {\r\n        position: fixed;\r\n        top: 0;\r\n        right: 0;\r\n        left: 0;\r\n        z-index: 5;\r\n        background-color: var(--color-white);\r\n        box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.10);\r\n        transition: var(--transition);\r\n        .mainmenu {\r\n            >li {\r\n                >a {\r\n                    height: 80px;\r\n                    line-height: 80px;\r\n                    &:before {\r\n                        bottom: 20px;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.header-style-1 {\r\n    background-color: #f9f3f0;\r\n    padding-bottom: 30px;\r\n    .header-navbar {\r\n        background-color: var(--color-white);\r\n        padding: 0 40px;\r\n        border-radius: 10px;\r\n        @media #{$smlg-device} {\r\n            padding: 0 25px;\r\n        }\r\n        @media #{$md-layout} {\r\n            padding: 15px 25px;\r\n        }\r\n        @media #{$sm-layout} {\r\n            padding: 15px 15px;\r\n        }\r\n    }\r\n    .mainmenu > li {\r\n        @media #{$smlg-device} {\r\n            margin: 0 13px;\r\n        }\r\n    }\r\n    .axil-mainmenu {\r\n        &.axil-sticky {\r\n            background-color: transparent;\r\n            box-shadow: none;\r\n            padding-top: 10px;\r\n            .header-navbar {\r\n                box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.10);\r\n            }\r\n        }\r\n    }\r\n    .header-brand {\r\n        @media only screen and (max-width: 575px) {\r\n           margin-right: 10px;\r\n        }\r\n    }\r\n}\r\n\r\n.header-style-3 {\r\n    background-color: #f9f3f0;\r\n    .axil-mainmenu {\r\n        padding: 20px 0;\r\n        position: relative;\r\n        z-index: 2;\r\n        &.axil-sticky {\r\n            position: fixed;\r\n            background-color: transparent;\r\n            box-shadow: none;\r\n            z-index: 4;\r\n            .header-navbar {\r\n                box-shadow: 0 2px 10px 0 rgba(0, 0, 0, .10);\r\n            }\r\n        }\r\n    }\r\n    .header-navbar {\r\n        background-color: var(--color-white);\r\n        padding: 0 20px;\r\n        border-radius: 10px;\r\n        @media #{$md-layout} {\r\n            padding: 10px 20px;\r\n        }\r\n        @media #{$sm-layout} {\r\n            padding: 10px 20px;\r\n        }\r\n    }\r\n    .header-action {\r\n        display: flex;\r\n        align-items: center;\r\n        .header-btn {\r\n            .axil-btn {\r\n                padding: 12px 30px;\r\n                font-size: 15px;\r\n                font-weight: 500;\r\n                @media #{$small-mobile} {\r\n                    padding: 12px 20px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.header-style-4 {\r\n    .mainmenu {\r\n        >li {\r\n            >a {\r\n                line-height: 60px;\r\n                height: 60px;\r\n                &:before {\r\n                    bottom: 18px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.header-style-5 {\r\n    .axil-mainmenu {\r\n        @media only screen and (max-width: 991px) {\r\n            padding: 15px 0;\r\n        }\r\n    }\r\n    \r\n    @media only screen and (max-width: 1399px) {\r\n        .header-navbar {\r\n            .header-main-nav {\r\n                margin: 0 20px;\r\n                @media only screen and (max-width: 991px) {\r\n                    margin: 0;\r\n                }\r\n            }\r\n        }\r\n        .mainmenu {\r\n            margin: 0 -18px;\r\n            >li {\r\n                margin: 0 18px;\r\n            }\r\n        }\r\n    }\r\n\r\n    .header-action {\r\n        .axil-search {\r\n            input {\r\n                border: 1px solid #f0f0f0;\r\n                height: 40px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.header-style-7 {\r\n    position: relative;\r\n    z-index: 5;\r\n    .axil-mainmenu {\r\n        padding: 20px 45px;\r\n        box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.03);\r\n        filter: drop-shadow(0px 4px 10px rgba(0, 0, 0, 0.03));\r\n        @media (max-width: 991px) {\r\n            padding: 20px 0;\r\n            filter: none;\r\n        }\r\n        .header-navbar {\r\n            .header-main-nav {\r\n                margin-left: 90px;\r\n                @media (max-width: 1199px) {\r\n                    margin: 0 30px;\r\n                }\r\n                @media (max-width: 991px) {\r\n                    margin: 0;\r\n                }\r\n                \r\n            }\r\n        }\r\n    }\r\n    .header-action {\r\n        >ul {\r\n            margin: 0 -14px;\r\n            @media (max-width: 767px) {\r\n                margin: 0 -10px;\r\n            }\r\n            >li {\r\n                margin: 0 14px;\r\n                @media (max-width: 767px) {\r\n                    margin: 0 10px;\r\n                }\r\n                >a {\r\n                    font-size: 19px;\r\n                }\r\n            }\r\n        }\r\n        .axil-search {\r\n            margin-right: 110px;\r\n            @media (max-width: 1599px) {\r\n                margin-right: 16px;\r\n            }\r\n            @media (max-width: 767px) {\r\n                margin-right: 10px;\r\n            }\r\n            input {\r\n                border: 1px solid #D6D6D6;\r\n                height: 40px;\r\n                border-radius: 8px;\r\n                padding: 0 15px;\r\n                padding-right: 35px;\r\n                width: 448px;\r\n                max-width: 448px;\r\n            }\r\n            .icon {\r\n                left: auto;\r\n                right: 16px;\r\n                font-size: 18px;\r\n                color: var(--color-body);\r\n                top: 50%;\r\n                &:hover {\r\n                    color: var(--color-heading);\r\n                }\r\n            }\r\n        }\r\n        .shopping-cart {\r\n            .cart-dropdown-btn {\r\n                .cart-count {\r\n                    height: 14px;\r\n                    width: 14px;\r\n                    line-height: 14px;\r\n                    font-size: 10px;\r\n                    border: none;\r\n                    display: block;\r\n                    top: -16px;\r\n                    right: -8px;\r\n                }\r\n            }\r\n        }\r\n        .my-account {\r\n            .my-account-dropdown {\r\n                margin-top: 30px;\r\n            }\r\n        }\r\n    }\r\n\r\n    .mainmenu {\r\n        justify-content: flex-start;\r\n        margin: 0 -20px;\r\n        @media (max-width: 1199px) {\r\n            margin: 0 -15px;\r\n        }\r\n        >li {\r\n            margin: 0 20px;\r\n            @media (max-width: 1199px) {\r\n                margin: 0 15px;\r\n            }\r\n            >a {\r\n                font-size: 16px;\r\n                font-weight: 500;\r\n                line-height: 1 !important;\r\n                height: auto !important;\r\n                i {\r\n                    margin-right: 10px;\r\n                    color: var(--color-body);\r\n                    transition: .3s;\r\n                }\r\n                &:before {\r\n                    display: none;\r\n                }\r\n                &:hover {\r\n                    i {\r\n                        color: var(--color-heading);\r\n                    }\r\n                }\r\n            }\r\n            &.dropdown {\r\n                .dropdown-toggle {\r\n                    border: 1px dashed var(--color-primary);\r\n                    display: flex;\r\n                    align-items: center;\r\n                    font-size: 16px;\r\n                    padding: 9px 14px;\r\n                    border-radius: 8px;\r\n                    i {\r\n                        color: var(--color-primary);\r\n\r\n                    }\r\n                    &:after {\r\n                        content: \"\\f063\";\r\n                        font-family: var(--font-awesome);\r\n                        border: none;\r\n                        vertical-align: middle;\r\n                        font-size: 12px;\r\n                        margin-left: 8px;\r\n                        transition: all 0.3s ease-in-out;\r\n                    }\r\n                    &:hover {\r\n                        color: var(--color-primary);\r\n                        i {\r\n                            color: var(--color-primary);\r\n                        }\r\n                        &:after {\r\n                            color: var(--color-primary);\r\n                        }\r\n                    }\r\n                    @media (max-width: 991px) {\r\n                        border: none;\r\n                        padding: 5px 0;\r\n                        i {\r\n                            color: var(--color-body);\r\n                        }\r\n                    }\r\n                }\r\n                .dropdown-menu {\r\n                    list-style: none;\r\n                    @media (min-width: 992px) {\r\n                        transform: translate(0px, 20px) !important;\r\n                        inset: initial !important;\r\n                        background: #ffffff;\r\n                        min-width: 250px;\r\n                        padding: 15px 10px;\r\n                        border-radius: 4px;\r\n                        display: block !important;\r\n                        visibility: hidden;\r\n                        opacity: 0;\r\n                        box-shadow: var(--shadow-primary);\r\n                        transition: all 0.3s ease-in-out;\r\n                    }\r\n                    @media (max-width: 991px) {\r\n                        position: initial !important;\r\n                        border: none;\r\n                        padding: 0 0 0 10px;\r\n                    }\r\n                    li {\r\n                        margin: 0;\r\n                        a {\r\n                            position: relative;\r\n                            font-size: 15px;\r\n                            text-transform: capitalize;\r\n                            color: var(--color-heading);\r\n                            font-weight: 500;\r\n                            padding: 10px 15px;\r\n                            display: block;\r\n                            transition: all 0.3s ease-in-out;\r\n                            z-index: 1;\r\n                            overflow: hidden;\r\n                            border-bottom: 1px solid #f3f3f3;\r\n                            &:hover {\r\n                                color: var(--color-secondary);\r\n                                background: transparent;\r\n                            }\r\n                            &.active {\r\n                                color: var(--color-secondary);\r\n                                &:hover {\r\n                                    color: var(--color-secondary);\r\n                                }\r\n                            }\r\n                            @media (max-width: 991px) {\r\n                                padding: 10px 0;\r\n                            }\r\n                        }\r\n                        &:last-child {\r\n                            a {\r\n                                border-bottom: none;\r\n                            }\r\n                        }\r\n                    }\r\n                    &.show {\r\n                        visibility: visible;\r\n                        opacity: 1;\r\n                        transform: translate(0px, 10px) !important;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.d-none-laptop {\r\n    @media (max-width: 1599px) {\r\n        display: none;\r\n    }\r\n}\r\n\r\n.d-none-desktop {\r\n    @media (min-width: 1600px) {\r\n        display: none;\r\n    }\r\n}\r\n\r\n/*-----------------------\r\n    Header Action  \r\n-------------------------*/\r\n.header-action {\r\n    @media #{$small-mobile} {\r\n        margin-top: 4px;\r\n    }\r\n    >ul {\r\n        display: flex;\r\n        align-items: center;\r\n        margin: 0 -10px;\r\n        padding: 0;\r\n        @media #{$small-mobile} {\r\n            margin: 0 -6px;\r\n        }\r\n        >li {\r\n            @extend %liststyle;\r\n            margin: 0 10px;\r\n            @media #{$small-mobile} {\r\n                margin: 0 5px;\r\n            }\r\n            >a {\r\n                font-size: 24px;\r\n                font-weight: 500;\r\n                color: var(--color-heading);\r\n                position: relative;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n                z-index: 1;\r\n                @media #{$small-mobile} {\r\n                    font-size: 22px;\r\n                }\r\n                >i {\r\n                    display: inline-block;\r\n                    line-height: 0;\r\n                }\r\n                &::after {\r\n                    content: \"\";\r\n                    height: 45px;\r\n                    width: 45px;\r\n                    background-color: var(--color-secondary);\r\n                    transform: scale(0);\r\n                    border-radius: 50%;\r\n                    position: absolute;\r\n                    z-index: -1;\r\n                    transition: var(--transition);\r\n                    @media #{$large-mobile} {\r\n                        height: 35px;\r\n                        width: 35px;\r\n                    }\r\n                }\r\n                &:focus {\r\n                    color: var(--color-heading);\r\n                }\r\n                &:hover {\r\n                    color: var(--color-white);\r\n                    &::after {\r\n                        transform: scale(1);\r\n                    }\r\n                }\r\n                &.open {\r\n                    color: var(--color-white);\r\n                    &::after {\r\n                        transform: scale(1);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .shopping-cart {\r\n        .cart-dropdown-btn {\r\n            .cart-count {\r\n                text-align: center;\r\n                background-color: var(--color-primary);\r\n                border: 2px solid var(--color-white);\r\n                font-size: 12px;\r\n                font-weight: 500;\r\n                color: var(--color-white);\r\n                border-radius: 50%;\r\n                height: 22px;\r\n                width: 22px;\r\n                line-height: 19px;\r\n                position: absolute;\r\n                top: -12px;\r\n                right: -12px;\r\n            }\r\n        }\r\n    }\r\n    .my-account {\r\n        position: relative;\r\n        .my-account-dropdown {\r\n            position: absolute;\r\n            top: 100%;\r\n            right: 0;\r\n            background: #ffffff;\r\n            z-index: -1;\r\n            opacity: 0;\r\n            visibility: hidden;\r\n            min-width: 250px;\r\n            padding: 20px;\r\n            border-radius: 4px;\r\n            box-shadow: var(--shadow-primary);\r\n            @extend %transition;\r\n            list-style: none;\r\n            transform: translateY(30px);\r\n            margin-top: 20px;\r\n            @media #{$small-mobile} {\r\n                right: -30px;\r\n            }\r\n            ul {\r\n                list-style: none;\r\n                padding-left: 0;\r\n            }\r\n            .title {\r\n                font-size: 12px;\r\n                font-weight: 500;\r\n            }\r\n            li {\r\n                margin: 0;\r\n                a {\r\n                    font-size: 16px;\r\n                    border-bottom: 1px solid #eeeeee;\r\n                    padding: 12px 0;\r\n                    display: block;\r\n                }\r\n                &:hover {\r\n                    >a {\r\n                        color: var(--color-primary);\r\n                    }\r\n                }\r\n            }\r\n            .login-btn {\r\n                text-align: center;\r\n                text-align: center;\r\n                margin-top: 30px;\r\n                margin-bottom: 25px;\r\n            }\r\n            .axil-btn {\r\n               padding: 10px 35px;\r\n               width: 100%;\r\n            }\r\n            .reg-footer {\r\n                font-size: 12px;\r\n                .btn-link {\r\n                    margin-left: 7px;\r\n                    font-weight: 700;\r\n                    text-transform: uppercase;\r\n                    color: var(--color-dark);\r\n                    position: relative;\r\n                    line-height: 1;\r\n                    border-bottom: 2px solid #999FAE;\r\n                    text-decoration: none;\r\n                    &:hover {\r\n                        color: var(--color-primary);\r\n                        border-color: var(--color-primary);\r\n                    }\r\n                }\r\n            }\r\n            \r\n          \r\n            &.open {\r\n                opacity: 1;\r\n                visibility: visible;\r\n                z-index: 9;\r\n                transform: translateY(0);\r\n            }\r\n        }\r\n    }\r\n    .axil-search {\r\n        position: relative;\r\n        .icon {\r\n            position: absolute;\r\n            left: 15px;\r\n            width: auto;\r\n            padding: 0;\r\n            top: 52%;\r\n            transform: translateY(-50%);\r\n            line-height: 1;\r\n            background-color: transparent;\r\n            font-size: 22px;\r\n            color: var(--color-heading);\r\n            i {\r\n                display: inline-block;\r\n                line-height: 0;\r\n            }\r\n        }\r\n        input {\r\n            background: var(--color-white);\r\n            color: var(--color-heading);\r\n            border-radius: 6px;\r\n            padding-left: 40px;\r\n            padding-right: 10px;\r\n            max-width: 250px;\r\n            height: 50px;\r\n            opacity: 1;\r\n            font-size: 14px;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n/*-----------------------\r\n    Header Search\r\n-------------------------*/\r\n.header-search-modal {\r\n    position: fixed;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translateY(-50%) translateX(-50%) scale(.8);\r\n    opacity: 0;\r\n    visibility: hidden;\r\n    z-index: 10;\r\n    @media only screen and (max-width: 991px) {\r\n        width: 92%; \r\n        right: -100%;\r\n    }\r\n    .card-close {\r\n        height: 40px;\r\n        width: 40px;\r\n        font-size: 16px;\r\n        color: var(--color_black);\r\n        border-radius: 50%;\r\n        @extend %transition;\r\n        position: absolute;\r\n        right: -60px;\r\n        top: 0;\r\n        z-index: 1;\r\n        &:hover {\r\n            background-color: var(--color-primary);\r\n            color: var(--color-white);\r\n        }\r\n        @media only screen and (max-width: 991px) {\r\n            height: 35px;\r\n            width: 35px;\r\n            font-size: 15px;\r\n            right: 30px;\r\n            top: 12px;\r\n        }\r\n        @media #{$small-mobile} {\r\n            right: 15px;\r\n            top: 14px;\r\n        }\r\n    }\r\n    .header-search-wrap {\r\n        background-color: var(--color-white);\r\n        border-radius: 10px;\r\n        padding: 40px 30px;\r\n        width: 800px;\r\n        height: 575px;\r\n        max-height: 90vh;\r\n        overflow: auto;\r\n        @media only screen and (max-width: 991px) {\r\n            width: 100%;   \r\n            padding: 70px 30px 30px;\r\n        }\r\n        @media only screen and (max-width: 479px) {  \r\n            padding: 70px 15px 30px;\r\n        }\r\n    }\r\n    .card-header {\r\n        background-color: transparent;\r\n        padding: 0;\r\n        border-bottom: none;\r\n        form {\r\n            padding-bottom: 30px;\r\n        }\r\n        .form-control {\r\n            border: 1px solid #f1f1f1;\r\n            border-radius: 6px !important;\r\n            font-size: 15px;\r\n            height: 55px;\r\n            padding: 5px 20px 5px 50px;\r\n            color: var(--color-);\r\n            &:focus {\r\n                box-shadow: 0 16px 32px 0 rgba(0, 0, 0, 0.04);\r\n            }\r\n            &::placeholder {\r\n                color: var(--color-heading);\r\n                opacity: 1;\r\n            }\r\n            &:-ms-input-placeholder {\r\n                color: var(--color-heading);\r\n            }\r\n            &::-ms-input-placeholder {\r\n                color: var(--color-heading);\r\n            }\r\n        }\r\n        .axil-btn {\r\n            width: auto;\r\n            padding: 5px 20px;\r\n            font-size: 15px;\r\n            background-color: transparent;\r\n            margin: 5px;\r\n            border-radius: 6px !important;\r\n            z-index: 1;\r\n            position: absolute;\r\n            top: 0;\r\n            left: 0;\r\n            bottom: 0;\r\n            z-index: 10;\r\n            pointer-events: none;\r\n            \r\n            &:before {\r\n                display: none;\r\n            }\r\n            i {\r\n                margin-right: 0;\r\n                color: var(--color-lightest);\r\n            }\r\n            &:hover {\r\n                i {\r\n                    color: var(--color-heading);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .card-body {\r\n        padding: 0;\r\n    }\r\n    .search-result-header {\r\n        border-bottom: 1px solid #F6F7FB;\r\n        padding-bottom: 15px;\r\n        margin-bottom: 25px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        .title {\r\n            margin-bottom: 0;\r\n            font-size: 14px;\r\n            font-weight: 400;\r\n            color: var(--color-heading);\r\n        }\r\n        .view-all {\r\n            font-size: 14px;\r\n            transition: .3s;\r\n            color: var(--color-heading);\r\n            position: relative;\r\n            &:after {\r\n                content: \"\";\r\n                height: 2px;\r\n                width: 0;\r\n                background-color: var(--color-heading);\r\n                position: absolute;\r\n                bottom: -2px;\r\n                right: 0;\r\n                opacity: 0;\r\n                transition: 0.5s;\r\n            }\r\n            &:hover {\r\n                color: var(--color-heading);\r\n                &:after {\r\n                    width: 100%;\r\n                    opacity: 1;\r\n                    left: 0;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .psearch-results {\r\n        .axil-product-list {\r\n            padding: 20px;\r\n            margin-bottom: 20px;\r\n            @media #{$large-mobile} {\r\n                display: flex;\r\n                text-align: left;\r\n                align-items: flex-start;\r\n                .thumbnail {\r\n                    margin-bottom: 0;\r\n                    margin-right: 15px;\r\n                }\r\n                .product-rating {\r\n                    justify-content: flex-start;\r\n                }\r\n            }\r\n            @media #{$small-mobile} {\r\n                padding: 15px;\r\n                .thumbnail {\r\n                    width: 60px;\r\n                }\r\n                .product-content {\r\n                    .product-title {\r\n                        margin-bottom: 4px;\r\n                    }\r\n                    .product-price-variant {\r\n                        font-size: 16px;\r\n                    }\r\n                    .rating-icon {\r\n                        margin-right: 10px;\r\n                    }\r\n                    .product-rating {\r\n                        display: block;\r\n                        margin-bottom: 5px;\r\n                        .rating-number {\r\n                            margin-left: 0;\r\n                        }\r\n                    }\r\n                    .product-cart {\r\n                        margin-top: 10px;\r\n                    }\r\n                }\r\n            }\r\n            &:last-child {\r\n                margin-bottom: 0;\r\n            }\r\n        }\r\n        &.show {\r\n            visibility: visible;\r\n            opacity: 1;\r\n        }\r\n    }\r\n    &.open {\r\n        visibility: visible;\r\n        opacity: 1;\r\n        transform: translate(-50%, -50%) scale(1);\r\n        transition: all .3s cubic-bezier(0.29, 1.39, 0.86, 1.15);\r\n        \r\n    }\r\n}\r\n\r\n/*-----------------------\r\n    Header Aside Menu\r\n-------------------------*/\r\n.axil-mainmenu.aside-category-menu {\r\n    background-color: #f7f7f7;\r\n    @media only screen and (max-width: 991px) {\r\n        padding: 10px 0;   \r\n    }\r\n    .header-main-nav {\r\n        margin-right: 0;\r\n        margin-left: 40px;\r\n        @media only screen and (max-width: 991px) {\r\n            margin-left: 0;   \r\n        }\r\n    }\r\n    .header-nav-department {\r\n        width: 250px;\r\n        @media only screen and (max-width: 1199px) {\r\n            width: auto;\r\n        }\r\n    }\r\n    .header-department {\r\n        position: relative;\r\n        .header-department-text {\r\n            font-size: 16px;\r\n            background: var(--color-primary);\r\n            margin-bottom: 0;\r\n            display: flex;\r\n            padding: 17px 30px;\r\n            position: relative;\r\n            @media only screen and (max-width: 991px) {\r\n                padding: 10px 20px;  \r\n                border-radius: 6px;\r\n            }\r\n            .icon {\r\n                margin-right: 20px;\r\n                color: var(--color-white);\r\n                font-size: 18px;\r\n                @media only screen and (max-width: 991px) {\r\n                    margin-right: 15px;   \r\n                }\r\n                @media #{$small-mobile} {\r\n                    display: none;\r\n                }\r\n            }\r\n            .text {\r\n                color: var(--color-white);\r\n                margin: 0;\r\n                cursor: pointer;\r\n            }\r\n        }\r\n        .department-nav-menu {\r\n            position: absolute;\r\n            top: 100%;\r\n            left: 0;\r\n            right: 0;\r\n            background-color: var(--color-white);\r\n            border: 1px solid #f3f3f3;\r\n            padding: 6px 0;\r\n            transition: var(--transition);\r\n            z-index: 5;\r\n            @media #{$smlg-device} {\r\n                position: fixed;\r\n                top: 0;\r\n                left: -260px;\r\n                bottom: 0;\r\n                z-index: 50;\r\n                width: 250px;\r\n                padding: 70px 0 20px;\r\n                height: 100%;\r\n                border-radius: 0;\r\n            }\r\n            .sidebar-close {\r\n                font-size: 14px;\r\n                color: var(--color-black);\r\n                position: absolute;\r\n                top: 8px;\r\n                right: 15px;\r\n                height: 30px;\r\n                width: 30px;\r\n                line-height: 30px;\r\n                background-color: var(--color-lighter);\r\n                border-radius: 10px;\r\n                display: none;\r\n                &:hover {\r\n                    background-color: var(--color-primary);\r\n                    color: var(--color-white);\r\n                }\r\n                @media only screen and (max-width: 1199px) {\r\n                    display: block;\r\n                }\r\n            }\r\n            .nav-menu-list {\r\n                @extend %liststyle;\r\n                @media only screen and (max-width: 1199px) {\r\n                    height: 100%;\r\n                    overflow: auto;\r\n                }\r\n            }\r\n            >ul {\r\n                >li {\r\n                    padding: 0 30px;\r\n                    margin: 0;\r\n                    position: relative;\r\n                    &:hover {\r\n                        .department-megamenu {\r\n                            pointer-events: auto;\r\n                            visibility: visible;\r\n                            opacity: 1;\r\n                            transform: translateX(0);\r\n                            .department-submenu {\r\n                                opacity: 1;\r\n                                transform: translateX(0);\r\n                            }\r\n                            .featured-product {\r\n                                opacity: 1;\r\n                                transform: translateY(0);\r\n                            }\r\n                        }\r\n                    }\r\n                    &:last-child {\r\n                        .nav-link {\r\n                            border-bottom: none;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            .nav-link {\r\n                display: flex;\r\n                align-items: center;\r\n                font-size: 14px;\r\n                font-weight: 500;\r\n                color: #999999;\r\n                padding: 13px 0;\r\n                border-bottom: 1px solid #f3f3f3;\r\n                position: relative;\r\n                @media #{$smlg-device} {\r\n                    font-size: 16px;\r\n                    padding: 12px 0;    \r\n                }\r\n                .menu-icon {\r\n                    margin-right: 14px;\r\n                    position: relative;\r\n                    top: -2px;\r\n                    img {\r\n                        width: 25px;\r\n                        height: auto;\r\n                    }\r\n                }\r\n                &.has-megamenu {\r\n                    &:after {\r\n                        content: \"\\f107\";\r\n                        font-family: var(--font-awesome);\r\n                        font-weight: 400;\r\n                        color: #c5c5c5;\r\n                        font-size: 14px;\r\n                        position: absolute;\r\n                        top: 50%;\r\n                        transform: translateY(-50%);\r\n                        right: 0;\r\n                        transition: var(--transition);\r\n                    }\r\n                }\r\n                &:hover {\r\n                    color: var(--color-heading);\r\n                    &:after {\r\n                        color: var(--color-primary);\r\n                        transform: translateY(-50%) rotate(-90deg);\r\n                    }\r\n                }\r\n            }\r\n            &.open {\r\n                left: 0;\r\n            }\r\n        }\r\n        .department-megamenu {\r\n            position: absolute;\r\n            top: 0;\r\n            left: 100%;\r\n            width: 990px;\r\n            z-index: 3;\r\n            transform: translateX(10px);\r\n            visibility: hidden;\r\n            opacity: 0;\r\n            pointer-events: none;\r\n            transition: all 0.3s ease-in-out;\r\n            margin-left: 1px;\r\n            @media only screen and (max-width: 1320px) {\r\n                width: 870px; \r\n            }\r\n            @media only screen and (max-width: 1199px) {\r\n                position: initial;\r\n                visibility: visible;\r\n                opacity: 1;\r\n                transform: translateX(0);\r\n                pointer-events: auto;\r\n                width: auto;\r\n                display: none;\r\n                transition: initial;\r\n            }\r\n            .department-megamenu-wrap {\r\n                background-color: var(--color-white);\r\n                border-radius: 0 0 24px 0;\r\n                box-shadow: 40px 40px 48px 0px rgba(36, 41, 47, 0.1);\r\n                padding: 30px;\r\n                display: flex;\r\n                @media only screen and (max-width: 1320px) {\r\n                    padding: 15px;\r\n                }\r\n                @media only screen and (max-width: 1199px) {\r\n                    display: block;\r\n                    padding: 0;\r\n                    box-shadow: none;\r\n                    padding: 20px 0;\r\n                }\r\n            }\r\n            .department-submenu-wrap {\r\n                flex: auto;\r\n                padding: 30px;\r\n                display: grid;\r\n                grid-template-columns: repeat(3, 1fr);\r\n                column-gap: 20px;\r\n                border-right: 2px solid #F6F7FB;\r\n                @media only screen and (max-width: 1199px) {\r\n                    grid-template-columns: repeat(1, 1fr);\r\n                    padding: 0;\r\n                    border: none;\r\n                }\r\n            }\r\n            .department-submenu {\r\n                opacity: 0;\r\n                transform: translateX(10px);\r\n                transition: all 0.3s ease-in-out;\r\n                @media only screen and (max-width: 1199px) {\r\n                    opacity: 1;\r\n                    transform: translateX(0);\r\n                }\r\n                &:nth-child(1n) {\r\n                    transition-delay: 0.1s;\r\n                }       \r\n                &:nth-child(2n) {\r\n                    transition-delay: 0.2s;\r\n                }       \r\n                &:nth-child(3n) {\r\n                    transition-delay: 0.3s;\r\n                }\r\n                .submenu-heading {\r\n                    font-size: 16px;\r\n                    color: var(--color-black);\r\n                    margin-bottom: 12px;\r\n                    @media #{$smlg-device} {\r\n                        font-size: 16px;\r\n                    }\r\n                }\r\n                ul {\r\n                    margin-bottom: 30px;\r\n                    @extend %liststyle;\r\n                    li {\r\n                        a {\r\n                            font-size: 14px;\r\n                            font-weight: 500;\r\n                            color: var(--color-body);\r\n                            padding: 10px 0;\r\n                            &:hover {\r\n                                color: var(--color-primary);\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            .featured-product {\r\n                padding: 40px 30px 40px 55px;\r\n                max-width: 40%;\r\n                opacity: 0;\r\n                transform: translateY(10px);\r\n                transition: all 0.3s ease-in-out;\r\n                transition-delay: 0.4s;\r\n                @media only screen and (max-width: 1199px) {\r\n                    max-width: 100%;\r\n                    opacity: 1;\r\n                    transform: translateY(0);\r\n                    padding: 0;\r\n\r\n                }\r\n                .featured-heading {\r\n                    font-size: 16px;\r\n                    color: var(--color-black);\r\n                    margin-bottom: 12px;\r\n                }\r\n                .product-list {\r\n                    display: grid;\r\n                    grid-template-columns: repeat(2, 1fr);\r\n                    gap: 15px;\r\n                    .item-product {\r\n                        &:nth-child(-n +2) {\r\n                            grid-column: span 2;\r\n                        }\r\n                        a {\r\n                            overflow: hidden;\r\n                            display: block;\r\n                            border-radius: 8px;\r\n                            img {\r\n                                border-radius: 8px;\r\n                                transition: 0.4s ease-in-out;\r\n                            }\r\n                            &:hover {\r\n                                img {\r\n                                    transform: scale(1.1);\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n                .axil-btn {\r\n                    margin-top: 15px;\r\n                    display: block;\r\n                    text-align: center;\r\n                }\r\n            }\r\n        } \r\n    }\r\n\r\n    .mainmenu {\r\n        justify-content: flex-start;\r\n        >li {\r\n            @media #{$smlg-device} {\r\n                margin: 0 20px;\r\n            }\r\n            &:last-child {\r\n                margin-right: 0;\r\n            }\r\n            >a {\r\n                line-height: 60px;\r\n                height: 60px;\r\n                &:before {\r\n                    bottom: 18px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n/*-----------------------\r\n    Mobile Menu\r\n-------------------------*/\r\n.header-main-nav {\r\n    .mainmenu-nav {  \r\n        @media only screen and (max-width: 991px) {\r\n            display: block;\r\n            position: fixed;\r\n            top: 0;\r\n            bottom: 0;\r\n            right: -250px;\r\n            width: 250px;\r\n            background-color: var(--color-white);\r\n            z-index: 100; \r\n            transition: all 0.3s ease-in-out;\r\n            padding: 20px 30px 10px;\r\n            visibility: hidden;\r\n            opacity: 0;\r\n            .mainmenu {\r\n                display: block;\r\n                height: calc(100vh - 85px);\r\n                overflow-y: auto;\r\n                margin: 0;\r\n                >li {\r\n                    margin: 10px 0 !important;\r\n                    transform: translateY(20px);\r\n                    opacity: 0;\r\n                    transition: all 0.3s ease-in-out;\r\n                    >a {\r\n                        color: var(--color-body);\r\n                        line-height: var(--line-height-b2) !important;\r\n                        height: auto !important;\r\n                        padding: 5px 0;\r\n                        display: inline-block;\r\n                        &::before {\r\n                           display: none;\r\n                        }\r\n                    }\r\n                    &.menu-item-has-children {\r\n                        a {\r\n                            margin: 0;\r\n                            &::after {\r\n                                right: -18px;\r\n                                top: 4px;\r\n                                color: var(--color-body);\r\n                            }\r\n                        }\r\n                        .axil-submenu {\r\n                            display: none;\r\n                            position: static;\r\n                            transform: scaleY(1);\r\n                            visibility: visible;\r\n                            opacity: 1;\r\n                            min-width: auto;\r\n                            box-shadow: none;\r\n                            padding: 0;\r\n                            transition: initial;\r\n                            li {\r\n                                a {\r\n                                    padding: 5px 10px;\r\n                                    &:after {\r\n                                        display: none;\r\n                                    }\r\n                                    &:hover {\r\n                                        color: var(--color-primary);\r\n                                        background-color: transparent;\r\n                                    }\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    &.open {\r\n        .mainmenu-nav {\r\n            right: 0;\r\n            visibility: visible;\r\n            opacity: 1;\r\n            .mainmenu {\r\n                li {\r\n                    transform: translateY(0);\r\n                    opacity: 1;\r\n                    &:nth-child(1n) {\r\n                        transition-delay: 0.3s;\r\n                    }\r\n                    &:nth-child(2n) {\r\n                        transition-delay: 0.4s;\r\n                    }\r\n                    &:nth-child(3n) {\r\n                        transition-delay: 0.5s;\r\n                    }\r\n                    &:nth-child(4n) {\r\n                        transition-delay: 0.6s;\r\n                    }\r\n                    &:nth-child(5n) {\r\n                        transition-delay: 0.7s;\r\n                    }\r\n                    &:nth-child(6n) {\r\n                        transition-delay: 0.8s;\r\n                    }\r\n                    &:nth-child(7n) {\r\n                        transition-delay: 0.9s;\r\n                    }\r\n                    &:nth-child(8n) {\r\n                        transition-delay: 1s;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.mobile-close-btn {\r\n    background-color: transparent;\r\n    position: absolute;\r\n    top: 19px;\r\n    right: 15px;\r\n    height: 35px;\r\n    width: 35px;\r\n    background-color: var(--color-lighter);\r\n    border-radius: 40px;\r\n    color: var(--color-dark);\r\n    font-size: 12px;\r\n    @media only screen and (min-width: 992px) {\r\n        display: none;   \r\n    }\r\n    &:hover {\r\n        background-color: var(--color-primary);\r\n        color: var(--color-white);\r\n    }\r\n}\r\n\r\n.mobile-nav-brand {\r\n    margin-bottom: 30px;\r\n    img {\r\n        max-height: 35px;\r\n    }\r\n    @media only screen and (min-width: 992px) {\r\n        display: none;   \r\n    }\r\n}\r\n\r\n.axil-mobile-toggle {\r\n    margin-left: 30px;\r\n    @media only screen and (min-width: 992px) {\r\n        display: none;\r\n    }\r\n    @media #{$large-mobile} {\r\n        margin-left: 18px;\r\n    }\r\n    .menu-btn {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        padding: 0;\r\n        background-color: transparent;\r\n        position: relative;\r\n        z-index: 1;\r\n        color: var(--color-heading);\r\n        font-size: 23px;\r\n        @media #{$small-mobile} {\r\n            font-size: 22px;\r\n        }\r\n        i {\r\n            display: inline-block;\r\n            line-height: 0;\r\n        }\r\n        &:after {\r\n            content: \"\";\r\n            height: 40px;\r\n            width: 40px;\r\n            background: var(--color-secondary);\r\n            border-radius: 50%;\r\n            position: absolute;\r\n            z-index: -1;\r\n            transform: scale(0);\r\n            transition: var(--transition);\r\n            @media #{$large-mobile} {\r\n                height: 35px;\r\n                width: 35px;\r\n            }\r\n        }\r\n        &:hover {\r\n            color: var(--color-white);\r\n            &:after {\r\n                transform: scaleX(1);\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.header-mobile-brand {\r\n    img {\r\n        width: 150px;\r\n    }\r\n}\r\n\r\n", "/*-------------------------\r\n    Main Menu Nav  \r\n--------------------------*/\r\n.mainmenu-nav {\r\n    @media only screen and (max-width: 991px) {\r\n        display: none;   \r\n    }\r\n}\r\n.mainmenu {\r\n    @extend %liststyle;\r\n    display: flex;\r\n    align-items: center;\r\n    flex-wrap: wrap;\r\n    justify-content: center;\r\n    margin: 0 -24px;\r\n    @media #{$smlg-device} {\r\n        margin: 0 -15px;\r\n    }\r\n    >li {\r\n        margin: 0 24px;\r\n        @media #{$smlg-device} {\r\n            margin: 0 15px;\r\n        }\r\n        >a {\r\n            color: var(--color-heading);\r\n            font-weight: 700;\r\n            font-size: 15px;\r\n            font-family: var(--font-primary);\r\n            line-height: 80px;\r\n            height: 80px;\r\n            display: block;\r\n            position: relative;\r\n            transition: var(--transition);\r\n            &::before {\r\n                content: \"\";\r\n                height: 2px;\r\n                width: 0;\r\n                background-color: var(--color-black);\r\n                position: absolute;\r\n                bottom: 29px;\r\n                left: 0;\r\n                opacity: 0;\r\n                transition: 0.5s;\r\n            }\r\n            &:hover {\r\n                color: var(--color-black);\r\n                &::before {\r\n                    opacity: 1;\r\n                    width: 100%;\r\n                }\r\n            }\r\n            &.active {\r\n                color: var(--color-black);\r\n                &:before {\r\n                    width: 100%;\r\n                    opacity: 1;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    >.menu-item-has-children {\r\n        position: relative;\r\n        >a {\r\n            position: relative;\r\n            margin-right: 15px;\r\n            &::after {\r\n                content: \"\\f107\";\r\n                font-family: var(--font-awesome);\r\n                font-weight: 400;\r\n                color: #c6c6c6;\r\n                font-size: 14px;\r\n                position: absolute;\r\n                top: 1px;\r\n                right: -14px;\r\n            }\r\n        }\r\n        &.menu-item-open {\r\n            >a {\r\n                &:before {\r\n                    width: 100%;\r\n                    opacity: 1;\r\n                }\r\n            }\r\n        }\r\n        .axil-submenu {\r\n            position: absolute;\r\n            top: 100%;\r\n            left: 0;\r\n            background: #ffffff;\r\n            z-index: -1;\r\n            opacity: 0;\r\n            visibility: hidden;\r\n            min-width: 250px;\r\n            padding: 15px 10px;\r\n            border-radius: 4px;\r\n            box-shadow: var(--shadow-primary);\r\n            transition: all 0.3s ease-in-out;\r\n            list-style: none;\r\n            pointer-events: none;\r\n            li {\r\n                margin: 0;\r\n                a {\r\n                    position: relative;\r\n                    font-size: 15px;\r\n                    text-transform: capitalize;\r\n                    color: var(--color-heading);\r\n                    font-weight: 500;\r\n                    padding: 5px 15px;\r\n                    border-radius: 4px;\r\n                    display: block;\r\n                    transition: all 0.3s ease-in-out;\r\n                    z-index: 1;\r\n                    overflow: hidden;\r\n                    &:hover {\r\n                        color: var(--color-secondary);\r\n                        // background: var(--color-secondary);\r\n                    }\r\n                    &.active {\r\n                        color: var(--color-secondary);\r\n                        &:hover {\r\n                            color: var(--color-secondary);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        &:hover {\r\n            .axil-submenu {\r\n                top: 90%;\r\n                opacity: 1;\r\n                visibility: visible;\r\n                z-index: 9;\r\n                pointer-events: all;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n", "/*----------------------\r\nHeader Shopping Cart  \r\n-----------------------*/\r\n.cart-dropdown {\r\n    position: fixed;\r\n    right: -600px;\r\n    top: 0;\r\n    bottom: 0;\r\n    z-index: 101;\r\n    transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);\r\n    @media only screen and (max-width: 767px) {\r\n        width: 100%; \r\n        right: -100%;\r\n    }\r\n    .cart-content-wrap {\r\n        background-color: var(--color-white);\r\n        padding: 60px 50px;\r\n        width: 600px;\r\n        height: 100%;\r\n        display: flex;\r\n        flex-direction: column;\r\n        overflow: auto;\r\n        @media only screen and (max-width: 767px) {\r\n            width: 100%;   \r\n            padding: 30px;\r\n        }\r\n        @media only screen and (max-width: 479px) {  \r\n            padding: 30px 15px;\r\n        }\r\n        /* width */\r\n        &::-webkit-scrollbar {\r\n            width: 8px;\r\n            border-radius: 10px;\r\n        }\r\n        \r\n        /* Track */\r\n        &::-webkit-scrollbar-track {\r\n            background: #f1f1f1; \r\n            border-radius: 10px;\r\n            transition: .5s;\r\n        }\r\n        \r\n        /* Handle */\r\n        &::-webkit-scrollbar-thumb {\r\n            background: var(--color-lightest);\r\n            border-radius: 10px;\r\n            transition: .5s;\r\n        }\r\n        \r\n        /* Handle on hover */\r\n        &::-webkit-scrollbar-thumb:hover {\r\n            background: var(--color-primary); \r\n        }\r\n        \r\n    }\r\n    .cart-header {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        border-bottom: 2px solid #F6F7FB;\r\n        padding-bottom: 18px;\r\n        .header-title {\r\n            font-size: 26px;\r\n            color: #27272E;\r\n            margin-bottom: 0;\r\n            @media #{$sm-layout} {\r\n                font-size: 24px;\r\n            }\r\n        }\r\n        .cart-close {\r\n            height: 40px;\r\n            width: 40px;\r\n            font-size: 16px;\r\n            color: var(--color_black);\r\n            border-radius: 50%;\r\n            @extend %transition;\r\n            &:hover {\r\n                background-color: var(--color-primary);\r\n                color: var(--color-white);\r\n            }\r\n        }\r\n    }\r\n    .cart-body {\r\n        padding: 30px 0;\r\n        flex: auto;\r\n    }\r\n    .cart-item-list {\r\n        @extend %liststyle;\r\n    }\r\n    .cart-item {\r\n        display: flex;\r\n        align-items: center;\r\n        margin-bottom: 30px;\r\n        border-bottom: 1px solid #F6F7FB;\r\n        padding-bottom: 30px;\r\n        @media #{$small-mobile} {\r\n            align-items: flex-start;\r\n        }\r\n        &:last-child {\r\n            margin-bottom: 0;\r\n            border-bottom: none;\r\n        }\r\n        .item-img {\r\n            margin-right: 30px;\r\n            position: relative;\r\n            @media only screen and (max-width: 479px) {\r\n                margin-right: 15px; \r\n            }\r\n            a {\r\n                display: block;\r\n                background-color: #F6F7FB;\r\n                border-radius: 10px;\r\n                @media #{$sm-layout} {\r\n                    width: 70px;\r\n                }\r\n                img {\r\n                    border-radius: 10px;\r\n                    height: 100px;\r\n                    width: 100px;\r\n                    object-fit: cover;\r\n                }\r\n            }\r\n            .close-btn {\r\n                height: 31px;\r\n                width: 31px;\r\n                background-color: #F6F7FB;\r\n                border: 2px solid var(--color-white);\r\n                border-radius: 50%;\r\n                font-size: 12px;\r\n                color: var(--color-black);\r\n                position: absolute;\r\n                top: -15px;\r\n                left: -10px;\r\n                transition: all 0.3s ease-in-out;\r\n                @media #{$sm-layout} {\r\n                    height: 25px;\r\n                    width: 25px;\r\n                    font-size: 10px;\r\n                }\r\n                &:hover {\r\n                    background-color: var(--color-primary);\r\n                    color: var(--color-white);\r\n                }\r\n            }\r\n        }\r\n        .item-content {\r\n            flex: 1;\r\n            position: relative;\r\n            padding-right: 110px;\r\n            @media #{$small-mobile} {\r\n               padding-right: 0;\r\n            }\r\n        }\r\n        .product-rating {\r\n            margin-bottom: 14px;\r\n            font-size: 10px;\r\n            transition: var(--transition);\r\n            i {\r\n                color: #FFDC60;\r\n            }\r\n            .rating-number {\r\n                margin-left: 5px;\r\n                font-weight: 500;\r\n            }\r\n        }\r\n        .item-title {\r\n            font-size: 16px;\r\n            color: var(--color-black);\r\n            margin-bottom: 10px;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n            display: -webkit-box;\r\n            -webkit-line-clamp: 1;\r\n            -webkit-box-orient: vertical;\r\n            @media #{$sm-layout} {\r\n                font-size: 16px;  \r\n                margin-bottom: 15px;  \r\n            }\r\n            @media #{$small-mobile} {\r\n                margin-bottom: 5px;\r\n            }\r\n        }\r\n        .item-price {\r\n            font-size: 18px;\r\n            font-family: var(--font-secondary);\r\n            color: var(--color-black);\r\n            @media #{$sm-layout} {\r\n                font-size: 16px;\r\n                margin-top: 10px;\r\n            }\r\n        }\r\n        .item-quantity {\r\n            display: flex;\r\n            align-items: center;\r\n            position: absolute;\r\n            top: 50%;\r\n            right: 0;\r\n            transform: translateY(-50%);\r\n            justify-content: flex-end;\r\n            @media #{$small-mobile} {\r\n                position: initial;\r\n                transform: translateY(0);\r\n                justify-content: flex-start;\r\n                margin-top: 8px;\r\n            }\r\n            .qtybtn {\r\n                text-align: center;\r\n                height: 26px;\r\n                width: 26px;\r\n                line-height: 20px;\r\n                font-size: 18px;\r\n                color: var(--color-black);\r\n                background-color: #F6F7FB;\r\n                border-radius: 50%;\r\n                transition: all 0.3s ease-in-out;\r\n                &:hover {\r\n                    background-color: var(--color-primary);\r\n                    color: var(--color-white);\r\n                }\r\n            }\r\n            .quantity-input {\r\n                font-size: 16px;\r\n                font-weight: 600;\r\n                color: #27272E;\r\n                height: 26px;\r\n                width: 30px;\r\n                border: none;\r\n                text-align: center;\r\n                padding: 0;\r\n            }\r\n            input::-webkit-outer-spin-button,\r\n            input::-webkit-inner-spin-button {\r\n              -webkit-appearance: none;\r\n              margin: 0;\r\n            }\r\n            input[type=number] {\r\n              -moz-appearance: textfield;\r\n            }\r\n        }\r\n    }\r\n    .cart-footer {\r\n        border-top: 2px solid #F6F7FB;\r\n        .cart-subtotal {\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-between;\r\n            margin: 22px 0 30px;\r\n            color: var(--color-black);\r\n            font-size: 20px;\r\n            .subtotal-amount {\r\n                font-weight: 700;\r\n            }\r\n        }\r\n        .group-btn {\r\n            display: grid;\r\n            grid-template-columns: repeat(2, 1fr);\r\n            column-gap: 20px;\r\n            @media #{$large-mobile} {\r\n                display: block;\r\n            }\r\n            .axil-btn {\r\n                text-align: center;\r\n                &:hover {\r\n                    &:before {\r\n                        transform: scale(1.05);\r\n                    }\r\n                }\r\n                @media #{$large-mobile} {\r\n                    display: block;\r\n                    margin-bottom: 10px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n.cart-dropdown.open {\r\n    right: 0;\r\n}\r\n\r\nbody.open .closeMask {\r\n    height: 100%;\r\n    width: 100%;\r\n    background-color: rgba(0, 0, 0, 0.6);\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    z-index: 6;\r\n    transition: 0.3s;\r\n\r\n}", "/*----------------------\r\n    Shop Styles  \r\n----------------------*/\r\n.category-select {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    margin: -10px;\r\n    .single-select {\r\n        color: var(--color-dark);\r\n        width: auto;\r\n        margin: 10px;\r\n        padding-right: 43px;\r\n        background: url(../images/icons/arrow-icon2.png) 85% center no-repeat transparent;\r\n        font-family: var(--font-primary);\r\n        font-weight: 500;\r\n        font-size: var(--font-size-b1);\r\n        border: 2px solid var(--color-light);\r\n        @media #{$sm-layout} {\r\n           width: 100%;\r\n           background-position-x: 95%; \r\n        }\r\n    }\r\n}\r\n\r\n\r\n/* Start Axil Single Product  */\r\n\r\n.axil-product {\r\n    position: relative;\r\n    @media #{$large-mobile} {\r\n        text-align: center;\r\n    }\r\n    >.thumbnail {\r\n        position: relative;\r\n        display: block;\r\n        >a {\r\n            display: block;\r\n            background-color: #f7f7f7;\r\n            border-radius: 6px;\r\n            overflow: hidden;\r\n            position: relative;\r\n            img {\r\n                border-radius: 6px;\r\n                width: 100%;\r\n                transition: 0.3s;\r\n            }\r\n            .hover-img {\r\n                position: absolute;\r\n                top: 0;\r\n                left: 0;\r\n                right: 0;\r\n                bottom: 0;\r\n                visibility: hidden;\r\n                opacity: 0;\r\n                transition: .3s;\r\n            }\r\n        }\r\n        .label-block {\r\n            position: absolute;\r\n            top: 24px;\r\n            left: 24px;\r\n            z-index: 2;\r\n            .product-badget {\r\n                background-color: var(--color-primary);\r\n                line-height: 1;\r\n                padding: 6px 10px 5px;\r\n                font-size: 12px;\r\n                font-weight: 700;\r\n                color: #FFFFFF;\r\n                border-radius: 4px;\r\n                box-shadow: 0 8px 16px 0 rgba(53, 119, 240, .3);\r\n            }\r\n            &.label-right {\r\n                left: auto;\r\n                right: -10px;\r\n            }\r\n        }\r\n    }\r\n    .sub-title {\r\n        margin-bottom: 10px;\r\n    }\r\n    .product-content {\r\n        margin-top: 25px;\r\n        position: relative;\r\n        margin-bottom: 30px;\r\n        .product-rating {\r\n            margin-bottom: 10px;\r\n            font-size: 13px;\r\n            transition: var(--transition);\r\n            i {\r\n                color: #FFDC60;\r\n            }\r\n            .rating-number {\r\n                margin-left: 5px;\r\n                font-weight: 500;\r\n            }\r\n        }\r\n        .inner {\r\n            transition: 0.3s;\r\n        }\r\n        .sub-title {\r\n            transition: var(--transition);  \r\n        }\r\n        .title {\r\n            color: var(--color-body);\r\n            font-size: 16px;\r\n            font-weight: 500;\r\n            margin-bottom: 10px;\r\n            transition: var(--transition);\r\n            a {\r\n                transition: 0.3s;\r\n            }\r\n        }\r\n        .product-price-variant {\r\n            margin: -4px;\r\n            transition: var(--transition);\r\n            transition-delay: 0.1s;\r\n            @media #{$large-mobile} {\r\n                justify-content: center;\r\n            }\r\n            span {\r\n                &.price {\r\n                    margin: 4px;\r\n                    color: var(--color-heading);\r\n                    font-weight: 700;\r\n                    font-size: 20px;\r\n                    font-family: var(--font-secondary);\r\n                    &.old-price {\r\n                        color: #d6d6d6;\r\n                        text-decoration: line-through;\r\n                        margin-left: 0;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .product-hover-action {\r\n        position: absolute;\r\n        bottom: 0;\r\n        left: 0;\r\n        right: 0;\r\n        opacity: 0;\r\n        visibility: hidden;\r\n        transition: 0.5s;\r\n      \r\n    }\r\n    .cart-action {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        @extend %liststyle;\r\n        margin: -5px;\r\n        li {\r\n            margin: 5px;\r\n            &.wishlist,\r\n            &.quickview {\r\n                a {\r\n                    width: 40px;\r\n                    height: 40px;\r\n                    line-height: 42px;\r\n                    border-radius: 4px;\r\n                    background-color: var(--color-white);\r\n                    display: block;\r\n                    text-align: center;\r\n                    transition: 0.3s;\r\n                    position: relative;\r\n                    font-size: 14px;\r\n                    color: var(--color-heading);\r\n                    box-shadow: 0 16px 32px 0 rgba(0,0,0,.06);\r\n                    position: relative;\r\n                    z-index: 1;\r\n                    &:before {\r\n                        content: \"\";\r\n                        height: 100%;\r\n                        width: 100%;\r\n                        background-color: var(--color-white);\r\n                        border-radius: 4px;\r\n                        position: absolute;\r\n                        top: 0;\r\n                        bottom: 0;\r\n                        left: 0;\r\n                        right: 0;\r\n                        z-index: -1;\r\n                        transition: transform .5s cubic-bezier(.165,.84,.44,1);\r\n                    }\r\n                    i {\r\n                        transition: all .3s ease-in-out;\r\n                    }\r\n                    &:hover {\r\n                        &:before {\r\n                            transform: scale(1.2);\r\n                        }\r\n                        i {\r\n                            animation: btnIconSlide 400ms;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            &.select-option {\r\n                a {\r\n                    position: relative;\r\n                    height: 40px;\r\n                    line-height: 39px;\r\n                    padding: 0 18px;\r\n                    display: block;\r\n                    border-radius: 4px;\r\n                    font-weight: 700;\r\n                    font-size: 14px;\r\n                    color: var(--color-white);\r\n                    background-color: var(--color-secondary);\r\n                    transition: 0.3s;\r\n                    box-shadow: 0 16px 32px 0 rgba(0,0,0,.06);\r\n                    position: relative;\r\n                    z-index: 1;\r\n                    &:before {\r\n                        content: \"\";\r\n                        height: 100%;\r\n                        width: 100%;\r\n                        background-color: var(--color-secondary);\r\n                        border-radius: 4px;\r\n                        position: absolute;\r\n                        top: 0;\r\n                        bottom: 0;\r\n                        left: 0;\r\n                        right: 0;\r\n                        z-index: -1;\r\n                        transition: transform .5s cubic-bezier(.165,.84,.44,1);\r\n                    }\r\n                    &:hover {\r\n                        background-color: var(--color-secondary);\r\n                        color: var(--color-white);\r\n                        &:before {\r\n                            transform: scale(1.1);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &:hover {\r\n        .thumbnail {\r\n            a {\r\n                img {\r\n                    transform: scale(1.1);\r\n                }\r\n                .hover-img {\r\n                    visibility: visible;\r\n                    opacity: 1;\r\n                }\r\n            }\r\n        }\r\n        .product-hover-action {\r\n            bottom: 30px;\r\n            opacity: 1;\r\n            visibility: visible;\r\n            transition-delay: 0.2s;\r\n        }\r\n    }\r\n    &.product-style-two {\r\n        text-align: center;\r\n        .product-content {\r\n            .color-variant,\r\n            .product-price-variant {\r\n                justify-content: center;\r\n                margin-top: 0;\r\n            }\r\n            .color-variant {\r\n                margin-bottom: 10px;\r\n            }\r\n            .product-price-variant {\r\n                margin-bottom: 15px;\r\n            }\r\n\r\n        }\r\n        .thumbnail a {\r\n            width: 276px;\r\n            height: 276px;\r\n            overflow: hidden;\r\n            border-radius: 50%;\r\n            margin: 0 auto;\r\n            @media #{$sm-layout} {\r\n                width: 200px;\r\n                height: 200px;\r\n            } \r\n            @media #{$large-mobile} {\r\n                width: 250px;\r\n                height: 250px;\r\n            }\r\n        }\r\n        .product-hover-action {\r\n            position: initial;\r\n            visibility: visible;\r\n            opacity: 1;\r\n        }\r\n    }\r\n    &.product-style-four {\r\n        text-align: center;\r\n        .product-content {\r\n            .product-price-variant {\r\n                justify-content: center;\r\n            }\r\n            .color-variant {\r\n                justify-content: center;\r\n            }\r\n        } \r\n    }\r\n    &.product-style-five {\r\n        border-radius: 6px;\r\n        text-align: center;\r\n        .thumbnail {\r\n            a {\r\n                border-radius: 6px 6px 0 0;\r\n                img {\r\n                    border-radius: 6px 6px 0 0;\r\n                }\r\n            }\r\n        }\r\n        .product-content {\r\n            padding: 25px 30px 30px;\r\n            margin: 0;\r\n            background-color: var(--color-white);\r\n            .cart-action {\r\n                padding-top: 10px;\r\n            }\r\n        }\r\n    }\r\n    &.product-style-six {\r\n        border: 1px solid #f1f1f1;\r\n        border-radius: 6px;\r\n        margin-bottom: 30px;\r\n        transition: .3s;\r\n        @media #{$large-mobile} {\r\n            text-align: left;\r\n        }\r\n        .thumbnail {\r\n            a {\r\n                border-radius: 6px 6px 0 0;\r\n                img {\r\n                    border-radius: 6px 6px 0 0;\r\n                    transition: transform 3s cubic-bezier(0.2, 0.96, 0.34, 1);\r\n                }\r\n            }\r\n        }\r\n        .product-content {\r\n            margin: 0;\r\n            padding: 35px 30px 30px;\r\n            z-index: 1;\r\n            .product-price-variant {\r\n                position: absolute;\r\n                top: -57px;\r\n                right: 25px;\r\n                z-index: -1;\r\n                background-color: rgba(255, 255, 255, .5);\r\n                border: 1px solid rgba(255, 255, 255, .5);\r\n                backdrop-filter: blur(25px);\r\n                box-shadow: 0 4px 30px rgba(0, 0, 0, .1);\r\n                padding: 5px 15px;\r\n                border-radius: 6px;\r\n                span {\r\n                    &.price {\r\n                        font-size: 18px;\r\n                        color: var(--color-white);\r\n                    }\r\n                }\r\n            }\r\n            .title {\r\n                margin-bottom: 15px;\r\n            }\r\n            .product-hover-action {\r\n                position: initial;\r\n                opacity: 1;\r\n                visibility: visible;\r\n                .cart-action {\r\n                    justify-content: flex-start;\r\n                    li {\r\n                        &.select-option {\r\n                            a {\r\n                                background-color: transparent;\r\n                                border: 1px solid #efefef;\r\n                                color: var(--color-heading);\r\n                                box-shadow: none;\r\n                                &:before {\r\n                                    display: none;\r\n                                }\r\n                                &:hover {\r\n                                    background-color: var(--color-primary);\r\n                                    color: var(--color-white);\r\n                                    border-color: var(--color-primary);\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n\r\n            }\r\n        }\r\n        &:hover {\r\n            box-shadow: var(--shadow-dark);\r\n            border-color: var(--color-white);\r\n            .thumbnail {\r\n                img {\r\n                    transform: scale(1.3);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &.product-style-seven {\r\n        &:before {\r\n            content: \"\";\r\n            height: 70%;\r\n            width: 100%;\r\n            background-color: #f7f7f7;\r\n            border-radius: 6px;\r\n            position: absolute;\r\n            top: 0;\r\n            left: 0;\r\n            right: 0;\r\n            transition: 0.3s;\r\n        }\r\n        .product-content {\r\n            margin: 0;\r\n            padding: 40px 30px 0;\r\n            position: relative;\r\n            .cart-btn {\r\n                position: absolute;\r\n                top: -20px;\r\n                right: 20px;\r\n                a {\r\n                    display: inline-block;\r\n                    text-align: center;\r\n                    height: 45px;\r\n                    width: 45px;\r\n                    line-height: 46px;\r\n                    background-color: var(--color-lighter);\r\n                    border: 2px solid var(--color-white);\r\n                    color: var(--color-heading);\r\n                    font-size: 18px;\r\n                    border-radius: 50%;\r\n                    transition: .3s;\r\n                    box-shadow: 0 16px 32px 0 rgba(103, 103, 103, .06);\r\n                    &:hover {\r\n                        background-color: var(--color-primary);\r\n                        border-color: var(--color-primary);\r\n                        color: var(--color-white);\r\n                        box-shadow: 0 8px 16px 0 rgba(53, 119, 240, .3);\r\n                    }\r\n                }\r\n            }\r\n            .product-rating {\r\n                margin-bottom: 0;\r\n                margin-top: 10px;\r\n            }\r\n            .title {\r\n                font-size: 20px;\r\n                color: var(--color-heading);\r\n            }\r\n            .product-price-variant {\r\n                .price {\r\n                    font-size: 16px;\r\n                }\r\n            }\r\n\r\n        }\r\n        .thumbnail {\r\n            a {\r\n                background-color: transparent;\r\n                overflow: visible;\r\n            }\r\n        }\r\n        &:hover {\r\n            .thumbnail {\r\n                img {\r\n                    transform: scale(.9);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &.product-style-eight {\r\n        .thumbnail {\r\n            a {\r\n                border-radius: 8px;\r\n            }\r\n        }\r\n        .label-block {\r\n            position: absolute;\r\n            z-index: 2;\r\n            &.label-left {\r\n                left: 10px;\r\n                top: 10px;\r\n            }\r\n            &.label-right {\r\n                left: auto;\r\n                right: 20px;\r\n                top: 20px;\r\n            }\r\n            .product-badget {\r\n                line-height: 1;\r\n                font-size: 12px;\r\n                font-weight: 500;\r\n                color: #FFFFFF;\r\n                border-radius: 4px;\r\n                background-color: var(--color-heading);\r\n                padding: 8px 10px;\r\n                box-shadow: none;\r\n                text-transform: uppercase;\r\n                &.sale {\r\n                    background-color: var(--color-white);\r\n                    color: var(--color-primary);\r\n                }\r\n                &.sold-out {\r\n                    background-color: var(--color-primary);\r\n                }\r\n            }\r\n        }\r\n        .cart-action {\r\n            display: block;\r\n            margin: 0 20px;\r\n            li.select-option {\r\n                a {\r\n                    text-align: center;\r\n                    display: flex;\r\n                    align-items: center;\r\n                    justify-content: center;\r\n                    border-radius: 8px;\r\n                    height: 42px;\r\n                    i {\r\n                        font-size: 21px;\r\n                        margin-right: 10px;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        .color-variant-wrapper {\r\n            margin-bottom: 12px;\r\n        }\r\n        .color-variant {\r\n            margin: -5px -2px;\r\n            li {\r\n                >span {\r\n                    height: 12px;\r\n                    width: 12px;\r\n                    border-width: 0;\r\n                    .color {\r\n                        height: 12px;\r\n                        width: 12px;\r\n                        transition: .3s;\r\n                    }\r\n                }\r\n                &.active {\r\n                    >span {\r\n                        border-width: 1px;\r\n                        .color {\r\n                            height: 6px;\r\n                            width: 6px;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        .product-content {\r\n            margin-bottom: 45px;\r\n            padding: 0 20px;\r\n            .title {\r\n                color: #26204B;\r\n                a {\r\n                    overflow: hidden;\r\n                    text-overflow: ellipsis;\r\n                    display: -webkit-box;\r\n                    -webkit-line-clamp: 3;\r\n                    -webkit-box-orient: vertical;\r\n                }\r\n            }\r\n            .product-rating {\r\n                font-size: 16px;\r\n                .rating-number {\r\n                    font-size: 12px;\r\n                }\r\n            }\r\n            .product-price-variant {\r\n                margin: 0;\r\n                line-height: 1.2;\r\n            }\r\n        }\r\n        &:hover {\r\n            .product-hover-action {\r\n                bottom: 20px;\r\n            }\r\n        }\r\n    }\r\n    &.product-list-style-3 {\r\n        display: flex;\r\n        align-items: center;\r\n        background-color: var(--color-lighter);\r\n        border-radius: 8px;\r\n        margin-bottom: 30px;\r\n        @media (max-width: 1199px) {\r\n            display: block;\r\n            padding: 40px 20px 20px;\r\n        }\r\n        .thumbnail {\r\n            padding: 26px 30px 26px 35px;\r\n            @media (max-width: 1199px) {\r\n                padding: 0 0 20px;\r\n            }\r\n            a {\r\n                position: relative;\r\n                z-index: 1;\r\n                &:before {\r\n                    content: \"\";\r\n                    height: 224px;\r\n                    width: 224px;\r\n                    border-radius: 50%;\r\n                    background-color: var(--color-white);\r\n                    position: absolute;\r\n                    bottom: 20px;\r\n                    left: 0;\r\n                    right: 0;\r\n                    margin: 0 auto;\r\n                    z-index: -1;\r\n                }\r\n            }\r\n        }\r\n        .product-content {\r\n            flex: 1;\r\n            margin: 0;\r\n            padding: 0 40px 0 0;\r\n            @media (max-width: 1199px) {\r\n                padding: 0;\r\n            }\r\n            .product-cate {\r\n                font-size: 12px;\r\n                font-weight: 500;\r\n                margin-bottom: 10px; \r\n            }\r\n            .title {\r\n                font-size: 20px;\r\n            }\r\n            .product-price-variant {\r\n                display: flex;\r\n                align-items: center;\r\n                .price-text {\r\n                    font-size: 20px;\r\n                    font-weight: 500;\r\n                    color: var(--color-heading);\r\n                    margin-right: 8px;\r\n                }\r\n                .price {\r\n                    font-size: 32px;\r\n                    font-weight: 700;\r\n                    color: var(--color-secondary);\r\n                }\r\n            }\r\n\r\n        }\r\n        &:hover {\r\n            .thumbnail {\r\n                a {\r\n                    img {\r\n                        transform: scale(1);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.color-variant {\r\n    @extend %liststyle;\r\n    margin: -5px -2px;\r\n    margin-top: 12px;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    @media #{$large-mobile} {\r\n        justify-content: center;\r\n    }\r\n    li {\r\n        margin: 5px 2px;\r\n        cursor: pointer;\r\n        >span {\r\n            border: 2px solid transparent;\r\n            width: 21px;\r\n            height: 21px;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            border-radius: 50%;\r\n            transition: var(--transition);\r\n            .color {\r\n                display: block;\r\n                width: 9px;\r\n                height: 9px;\r\n                line-height: 1;\r\n                border-radius: 50%;\r\n                border: none;\r\n            }\r\n        }\r\n        &.color-extra-01 {\r\n            span {\r\n                .color {\r\n                    background: #AAE6F8;\r\n                }\r\n            }\r\n            &.active {\r\n                span {\r\n                    border-color: #AAE6F8;\r\n                }\r\n            }\r\n        }\r\n        &.color-extra-02 {\r\n            span {\r\n                .color {\r\n                    background: #5F8AF7;\r\n                }\r\n            }\r\n            &.active {\r\n                span {\r\n                    border-color: #5F8AF7;\r\n                }\r\n            }\r\n        }\r\n        &.color-extra-03 {\r\n            span {\r\n                .color {\r\n                    background: #59C3C0;\r\n                }\r\n            }\r\n            &.active {\r\n                span {\r\n                    border-color: #59C3C0;\r\n                }\r\n            }\r\n        }\r\n        &.color-extra-04 {\r\n            span {\r\n                .color {\r\n                    background: #D3BBF3;\r\n                }\r\n            }\r\n            &.active {\r\n                span {\r\n                    border-color: #D3BBF3;\r\n                }\r\n            }\r\n        }\r\n        &.color-extra-05 {\r\n            span {\r\n                .color {\r\n                    background: #E8A2A5;\r\n                }\r\n            }\r\n            &.active {\r\n                span {\r\n                    border-color: #E8A2A5;\r\n                }\r\n            }\r\n        }\r\n        &.color-extra-06 {\r\n            span {\r\n                .color {\r\n                    background: #C3A03B;\r\n                }\r\n            }\r\n            &.active {\r\n                span {\r\n                    border-color: #C3A03B;\r\n                }\r\n            }\r\n        }\r\n        &.color-extra-07 {\r\n            span {\r\n                .color {\r\n                    background: #DFBF9B;\r\n                }\r\n            }\r\n            &.active {\r\n                span {\r\n                    border-color: #DFBF9B;\r\n                }\r\n            }\r\n        }\r\n        &.color-extra-08 {\r\n            span {\r\n                .color {\r\n                    background: #BADEFF;\r\n                }\r\n            }\r\n            &.active {\r\n                span {\r\n                    border-color: #BADEFF;\r\n                }\r\n            }\r\n        }\r\n        &.color-extra-09 {\r\n            span {\r\n                .color {\r\n                    background: #DBDEFF;\r\n                }\r\n            }\r\n            &.active {\r\n                span {\r\n                    border-color: #DBDEFF;\r\n                }\r\n            }\r\n        }\r\n        &.color-extra-10 {\r\n            span {\r\n                .color {\r\n                    background: #DBF8FF;\r\n                }\r\n            }\r\n            &.active {\r\n                span {\r\n                    border-color: #DBF8FF;\r\n                }\r\n            }\r\n        }\r\n        &.color-extra-11 {\r\n            span {\r\n                .color {\r\n                    background: #FFEDDC;\r\n                }\r\n            }\r\n            &.active {\r\n                span {\r\n                    border-color: #FFEDDC;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.range-variant {\r\n    display: flex;\r\n    align-items: center;\r\n    flex-wrap: wrap;\r\n    margin: -5px;\r\n    padding-left: 0;\r\n    li {\r\n        border: 2px solid #F6F7FB;\r\n        background: #fff;\r\n        padding: 5px 13px;\r\n        border-radius: 30px;\r\n        min-width: 44px;\r\n        min-height: 44px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        text-transform: uppercase;\r\n        font-weight: 500;\r\n        line-height: 24px;\r\n        margin: 5px;\r\n        cursor: pointer;\r\n        transition: 0.3s;\r\n        @media #{$sm-layout} {\r\n            font-size: 15px;\r\n        }\r\n        &.active {\r\n            border-color: #656973;\r\n        }\r\n        &:hover {\r\n            border-color: #656973;\r\n        }\r\n    }\r\n}\r\n\r\n.axil-product-list {\r\n    border: 1px solid #f1f1f1;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 30px;\r\n    border-radius: 6px;\r\n    margin-bottom: 30px;\r\n    transition: var(--transition);\r\n    @media #{$sm-layout} {\r\n       padding: 30px;\r\n    }\r\n    @media #{$large-mobile} {\r\n        display: block;\r\n        text-align: center;\r\n    }\r\n    .thumbnail {\r\n        margin-right: 30px;\r\n        @media #{$large-mobile} {\r\n            margin-right: 0;\r\n            margin-bottom: 20px;\r\n        }\r\n        a {\r\n            background-color: #f7f7f7;\r\n            border-radius: 6px;\r\n            display: block;\r\n            transition: var(--transition);\r\n            overflow: hidden;\r\n        }\r\n        img {\r\n            border-radius: 6px;\r\n            transition: var(--transition);\r\n        }\r\n    }\r\n    .product-content {\r\n        flex: 1;\r\n        position: relative;\r\n        padding-right: 60px;\r\n        @media #{$large-mobile} {\r\n            margin: 0;\r\n            padding: 0;\r\n        }\r\n        .product-title {\r\n            margin-bottom: 10px;\r\n            font-size: 16px;\r\n            color: var(--color-body);\r\n            a {\r\n                 transition: var(--transition);\r\n            }\r\n        }\r\n        .product-rating {\r\n            display: flex;\r\n            align-items: center;\r\n            margin-bottom: 8px;\r\n            font-size: 12px;\r\n            @media #{$large-mobile} {\r\n                justify-content: center;\r\n            }\r\n            .rating-number {\r\n                font-weight: 500;\r\n                margin-left: 10px;\r\n                display: inline-block;\r\n                span {\r\n                    font-weight: 700;\r\n                    color: var(--color-heading);\r\n                }\r\n            }\r\n            .rating-icon {\r\n                color: #ffa800;\r\n            }\r\n        }\r\n        .product-price-variant {\r\n            font-size: 20px;\r\n            font-weight: 700;\r\n            color: var(--color-heading);\r\n            .price {\r\n                &.old-price {\r\n                    color: #d6d6d6;\r\n                    text-decoration: line-through;\r\n                    margin-left: 10px;\r\n                }\r\n            }\r\n        }\r\n        .product-cart {\r\n            position: absolute;\r\n            top: 50%;\r\n            right: 0;\r\n            transform: translateY(-50%);\r\n            @media #{$large-mobile} {\r\n                position: inherit;\r\n                transform: translateY(0);\r\n                margin-top: 20px;\r\n            }\r\n            .cart-btn {\r\n                text-align: center;\r\n                display: block;\r\n                height: 40px;\r\n                width: 40px;\r\n                line-height: 40px;\r\n                border: 1px solid #efefef;\r\n                border-radius: 6px;\r\n                color: var(--color-heading);\r\n                font-size: 14px;\r\n                font-weight: 500;\r\n                transition: var(--transition);\r\n                margin-bottom: 10px;\r\n                @media #{$large-mobile} {\r\n                    margin: 0 5px;\r\n                    display: inline-block;\r\n                }\r\n                &:hover {\r\n                    background: var(--color-primary);\r\n                    border-color: var(--color-primary);\r\n                    color: var(--color-white);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &:hover {\r\n        border-color: var(--color-white);\r\n        box-shadow: var(--shadow-dark);\r\n        .thumbnail {\r\n            img {\r\n                transform: scale(1.1);\r\n            }\r\n        }\r\n    }\r\n    &.product-list-style-2 {\r\n        padding: 20px;\r\n        @media #{$large-mobile} {\r\n            padding: 30px;\r\n        }\r\n        .thumbnail {\r\n            margin-right: 20px;\r\n            max-width: 120px;\r\n            overflow: hidden;\r\n            border-radius: 6px;\r\n            @media #{$large-mobile} {\r\n                margin: 0 auto 20px;\r\n            }\r\n            img {\r\n                transition: .5s;\r\n            }\r\n        }\r\n        .product-content {\r\n            padding: 0;\r\n        }\r\n        .product-cart {\r\n            position: initial;\r\n            transform: translateY(0);\r\n            margin-top: 10px;\r\n            .cart-btn {\r\n                height: auto;\r\n                width: auto;\r\n                line-height: 1;\r\n                display: inline-block;\r\n                padding: 10px 15px;\r\n            }\r\n        }\r\n        &:hover {\r\n            img {\r\n               transform: scale(1.15);\r\n            } \r\n        }\r\n    }\r\n}\r\n\r\n.verified-icon {\r\n    color: #2081e2; \r\n    font-size: 14px;\r\n    padding-left: 2px; \r\n         \r\n}\r\n\r\n.product-transparent-layout {\r\n    .slick-list {\r\n        margin-top: -20px;\r\n    }\r\n    .slick-single-layout {\r\n        margin-top: 20px;\r\n    }\r\n}\r\n\r\n/* End Axil Single Product  */\r\n\r\n.product_list_widget {\r\n    list-style: none outside;\r\n    padding: 0;\r\n    margin: 0;\r\n    li {\r\n        display: flex;\r\n        align-items: center;\r\n        margin-bottom: 20px;\r\n        border-bottom: 1px solid #f3f3f3;\r\n        padding-bottom: 20px;\r\n        &:last-child {\r\n            margin-bottom: 0;\r\n            border-bottom: none;\r\n            padding-bottom: 0;\r\n        }\r\n        a {\r\n            display: block;\r\n        }\r\n        .thumbnail {\r\n            width: 120px;\r\n            margin-right: 20px;\r\n            min-width: 120px;\r\n            overflow: hidden;\r\n            margin-bottom: 0;\r\n            border-radius: 6px;\r\n            @media #{$small-mobile} {\r\n                width: 90px;\r\n                min-width: 90px;\r\n            }\r\n            a {\r\n                overflow: hidden;\r\n            }\r\n            img {\r\n                border-radius: 6px;\r\n                transition: .3s;\r\n            }\r\n        }\r\n        .title {\r\n            margin-bottom: 10px;\r\n            font-weight: 500;\r\n            font-size: 17px;\r\n            @media #{$small-mobile} {\r\n                font-size: 15px;\r\n            }\r\n        }\r\n        .content {\r\n            flex: 1;\r\n        }\r\n        .woocommerce-Price-amount.amount {\r\n            font-size: 17px;\r\n            line-height: 28px;\r\n            color: var(--color-heading);\r\n            font-weight: 500;\r\n            @media #{$sm-layout} {\r\n                font-size: 18px;\r\n            }\r\n            del {\r\n                padding-right: 8px;\r\n                color: #d6d6d6;\r\n            }\r\n        }\r\n        &:hover {\r\n            .thumbnail {\r\n                img {\r\n                    transform: scale(1.1);\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.btn-load-more {\r\n    min-width: 250px;\r\n}\r\n\r\n.filter-results {\r\n    font-size: 16px;\r\n    color: #000;\r\n    font-weight: 500;\r\n    margin: 0 20px 0 10px;\r\n    @media #{$sm-layout} {\r\n       margin: 10px;\r\n    }\r\n}\r\n\r\n.product-filter-mobile {\r\n    position: relative;\r\n    width: auto;\r\n    margin-top: 10px;\r\n    padding: 0;\r\n    border-radius: 6px;\r\n    font-size: var(--font-size-b2);\r\n    color: var(--color-dark);\r\n    font-weight: 500;\r\n    background-color: transparent;\r\n    &:after {\r\n        content: \"\";\r\n        height: 1px;\r\n        width: 100%;\r\n        background-color: var(--color-primary);\r\n        position: absolute;\r\n        bottom: 0;\r\n        left: 0;\r\n    }\r\n    i {\r\n        margin-right: 6px;\r\n    }\r\n    @media #{$sm-layout} {\r\n        margin-top: 20px;\r\n    }\r\n    &:hover {\r\n        color: var(--color-primary);\r\n    }\r\n}\r\n\r\n.axil-shop-sidebar {\r\n    padding-right: 20px;\r\n    position: relative;\r\n    @media only screen and (max-width: 991px) {\r\n        padding-right: 0;\r\n        position: fixed;\r\n        top: 0;\r\n        bottom: 0;\r\n        left: -300px;  \r\n        width: 280px;\r\n        background-color: var(--color-white);\r\n        z-index: 100;\r\n        padding: 100px 20px 50px;\r\n        overflow-y: auto;\r\n        transition: all 0.4s ease-in-out;\r\n\r\n    }\r\n    .toggle-list {\r\n        position: relative;\r\n        padding-bottom: 40px;\r\n        ul {\r\n            @extend %liststyle;\r\n        }\r\n        &.active {\r\n            .title {\r\n                &::before {\r\n                    content: \"\\f068\";\r\n                }\r\n                &::after {\r\n                    width: 100%;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    \r\n    .title {\r\n        font-size: 18px;\r\n        font-weight: 500;\r\n        color: var(--color-dark);\r\n        border-bottom: 2px solid var(--color-light);\r\n        padding-bottom: 10px;\r\n        margin-bottom: 20px;\r\n        cursor: pointer;\r\n        position: relative;\r\n        &::before {\r\n            content: \"\\f067\";\r\n            font-family: \"Font Awesome 5 Pro\";\r\n            position: absolute;\r\n            top: 0;\r\n            right: 0;\r\n            transition: 0.3s;\r\n            pointer-events: none;\r\n        }\r\n        &::after {\r\n            content: \"\";\r\n            position: absolute;\r\n            bottom: -2px;\r\n            left: 0;\r\n            width: 0;\r\n            height: 2px;\r\n            background: var(--color-primary);\r\n            transition: 0.3s;\r\n        }\r\n    }\r\n\r\n    .product-categories {\r\n        ul {\r\n            margin: -5px 0;\r\n            li {\r\n                margin: 0;\r\n                font-size: var(--font-size-b2);\r\n                font-weight: var(--s-medium);\r\n                padding: 6px 0;\r\n                a {\r\n                    position: relative;\r\n                    padding-left: 28px;\r\n                    color: var(--color-body);\r\n                    &::before {\r\n                        content: \"\";\r\n                        height: 16px;\r\n                        width: 16px;\r\n                        line-height: 15px;\r\n                        text-align: center;\r\n                        border: 1px solid var(--color-body);\r\n                        border-radius: 50%;\r\n                        position: absolute;\r\n                        top: 3px;\r\n                        left: 0;\r\n                        transition: var(--transition);\r\n                    }\r\n                }\r\n                &.current-cat,\r\n                &.chosen {\r\n                    a {\r\n                        &::before {\r\n                            content: \"\\f00c\";\r\n                            font-family: var(--font-awesome);\r\n                            font-size: 8px;\r\n                            font-weight: 700;\r\n                            color: var(--color-white);\r\n                            background: var(--color-primary);\r\n                            border-color: var(--color-primary);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .product-color {\r\n        ul {\r\n            display: flex;\r\n            align-items: center;\r\n            flex-wrap: wrap;\r\n        }\r\n        li {\r\n            margin: 0 8px 8px 0;\r\n            height: 30px;\r\n            width: 30px;\r\n            line-height: 32px;\r\n            border-radius: 50%;\r\n            text-align: center;\r\n            &.chosen {\r\n                border: 2px solid #906145;  \r\n            }\r\n            a {\r\n                display: inline-block;\r\n                height: 16px;\r\n                width: 16px;\r\n                border-radius: 50%;\r\n                &.color-extra-01 {\r\n                    background: #906145;\r\n                }\r\n                &.color-extra-02 {\r\n                    background: #FAB8C4;\r\n                }\r\n                &.color-extra-03 {\r\n                    background: #FFDC60;\r\n                }\r\n                &.color-extra-04 {\r\n                    background: #896BA7;\r\n                }\r\n                &.color-extra-05 {\r\n                    background: #DBDEFF;\r\n                }\r\n                &.color-extra-06 {\r\n                    background: #BADEFF;\r\n                }\r\n                &.color-extra-07 {\r\n                    background: #DFBF9B;\r\n                }\r\n                &.color-extra-08 {\r\n                    background: #BADEFF;\r\n                }\r\n                &.color-extra-09 {\r\n                    background: #DBDEFF;\r\n                }\r\n                &.color-extra-10 {\r\n                    background: #DBF8FF;\r\n                }\r\n                &.color-extra-11 {\r\n                    background: #FFEDDC;\r\n                }\r\n            }\r\n        }\r\n    } \r\n\r\n    .product-size {\r\n        li {\r\n            display: inline-block;\r\n            margin: 0 5px 10px 0;\r\n            a {\r\n                border: 2px solid #F6F7FB;\r\n                background: #fff;\r\n                padding: 5px 13px;\r\n                border-radius: 30px;\r\n                min-width: 44px;\r\n                min-height: 44px;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n                text-transform: uppercase;\r\n                font-weight: 500;\r\n                font-size: var(--font-size-b2);\r\n                color: var(--color-body);\r\n            }\r\n            &.chosen {\r\n                a {\r\n                    border-color: var(--color-primary);\r\n                    background-color: var(--color-primary);\r\n                    color: var(--color-white);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .product-price-range {\r\n        li {\r\n            display: inline-block;\r\n            margin: 0 15px 0 0;\r\n            a {\r\n                border: 2px solid #F6F7FB;\r\n                background: #fff;\r\n                padding: 5px 13px;\r\n                border-radius: 30px;\r\n                min-width: 50px;\r\n                min-height: 44px;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n                font-weight: 500;\r\n                font-size: var(--font-size-b2);\r\n                color: var(--color-body);\r\n            }\r\n            &.chosen {\r\n                a {\r\n                    border-color: var(--color-body);\r\n                }\r\n            }\r\n        }\r\n        .input-range,\r\n        .amount-range{\r\n            color: var(--color-heading);\r\n            font-size: var(--font-size-b2);\r\n        }\r\n    }\r\n    .axil-btn.btn-outline {\r\n        width: auto;\r\n        color: var(--color-body);\r\n    }\r\n\r\n    .filter-close-btn {\r\n        position: absolute;\r\n        top: 15px;\r\n        left: 20px;\r\n        height: 30px;\r\n        width: 30px;\r\n        background-color: var(--color-lighter);\r\n        border-radius: 10px;\r\n        color: var(--color-dark);\r\n        font-size: 14px;\r\n\r\n    }\r\n    &.open {\r\n        left: 0;\r\n    }\r\n}\r\n\r\n.product-area {\r\n    border-bottom: 2px solid var(--color-lighter);\r\n    &.pb--80 {\r\n        @media #{$sm-layout} {\r\n           padding-bottom: 60px !important; \r\n        }\r\n    }\r\n    &.pb--50 {\r\n        @media #{$sm-layout} {\r\n           padding-bottom: 30px !important; \r\n        }\r\n    }\r\n}\r\n\r\n\r\n.axil-new-arrivals-product-area {\r\n    &.fullwidth-container {\r\n        margin-left: calc((100% - 1320px) / 2);\r\n        overflow: hidden;\r\n        @media only screen and (max-width: 1349px) {\r\n           margin-left: auto;   \r\n        }\r\n        .slick-list {\r\n            overflow: visible;\r\n            @media only screen and (max-width: 1349px) {\r\n                overflow: hidden;   \r\n            }\r\n        }\r\n    }\r\n    &.flash-sale-area {\r\n        .arrow-top-slide {\r\n            .slide-arrow {\r\n                @media #{$sm-layout} {\r\n                    top: -180px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// Product Carousel Mobile Style\r\n@media #{$large-mobile} {\r\n    .product-slide-mobile {\r\n        .axil-product {\r\n            text-align: left;\r\n            display: inline-block;\r\n            width: 270px;\r\n            .product-content {\r\n                .product-price-variant {\r\n                    justify-content: flex-start;\r\n                }\r\n                .color-variant {\r\n                    justify-content: flex-start;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// Product Isotop Style\r\n.product-isotope-heading {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    @media #{$smlg-device} {\r\n      flex-direction: column;\r\n      align-items: flex-start;\r\n      margin-bottom: 40px;\r\n    }\r\n    .section-title-wrapper {\r\n        @media #{$smlg-device} {\r\n           margin-bottom: 30px;\r\n           padding-right: 0;\r\n        }\r\n        .title {\r\n            margin-bottom: 0;\r\n        }\r\n    }\r\n\r\n}\r\n\r\n.isotope-button {\r\n    display: flex;\r\n    align-items: center;\r\n    flex-wrap: wrap;\r\n    margin: -5px;\r\n    button {\r\n        margin: 5px;\r\n        padding: 10px 15px;\r\n        border-radius: 6px;\r\n        font-size: 15px;\r\n        font-weight: 500;\r\n        color: var(--color-heading);\r\n        background-color: transparent;\r\n        position: relative;\r\n        z-index: 1;\r\n        transition: .3s;\r\n        width: auto;\r\n        &:after {\r\n            content: \"\";\r\n            transform: scale(.7) perspective(1px);\r\n            position: absolute;\r\n            top: 0;\r\n            left: 0;\r\n            display: block;\r\n            height: 100%;\r\n            width: 100%;\r\n            opacity: 0;\r\n            transition: .3s;\r\n            background-color: var(--color-lighter);\r\n            border-radius: 6px;\r\n            z-index: -1;\r\n        }\r\n        &:hover {\r\n            &:after {\r\n                transform: scale(1.035) perspective(1px);\r\n                opacity: 1;\r\n            }\r\n        }\r\n        &.is-checked {\r\n            color: var(--color-white);\r\n            &:after {\r\n                transform: scale(1.035) perspective(1px);\r\n                opacity: 1;\r\n                background-color: var(--color-primary);\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// Product Collection Style\r\n.product-collection {\r\n    position: relative;\r\n    margin-bottom: 30px;\r\n    .collection-content {\r\n        position: absolute;\r\n        left: 30px;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n        right: 0;\r\n        z-index: 1;\r\n        .title {\r\n            font-size: 32px;\r\n            margin-bottom: 16px;\r\n            line-height: 1.2;\r\n        }\r\n        .price-warp {\r\n            margin-bottom: 30px;\r\n            .price-text {\r\n                font-size: 18px;\r\n                font-weight: 500;\r\n                color: var(--color-heading);\r\n                display: block;\r\n                margin-bottom: 8px;\r\n            }\r\n            .price {\r\n                font-size: 32px;\r\n                line-height: 1.2;\r\n                font-weight: 700;\r\n                color: var(--color-secondary);\r\n            }\r\n        }\r\n        .plus-btn {\r\n            position: absolute;\r\n            left: 38%;\r\n            top: 47%;\r\n            @media (max-width: 767px) {\r\n                left: 55%;\r\n            }\r\n            .plus-icon {\r\n                font-size: 20px;\r\n                color: var(--color-heading);\r\n                border: 2px solid var(--color-heading);\r\n                background-color: #ECF3FF;\r\n                border-radius: 50%;\r\n                height: 50px;\r\n                width: 50px;\r\n                line-height: 48px;\r\n                text-align: center;\r\n                display: block;\r\n                transition: 0.3s;\r\n                &:hover {\r\n                    color: var(--color-primary);\r\n                    border-color: var(--color-primary);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .label-block {\r\n        position: absolute;\r\n        z-index: 2;\r\n        &.label-right {\r\n            left: auto;\r\n            right: 20px;\r\n            top: 20px;\r\n        }\r\n        .product-badget {\r\n            line-height: 1;\r\n            font-size: 12px;\r\n            font-weight: 500;\r\n            color: #FFFFFF;\r\n            border-radius: 4px;\r\n            background-color: var(--color-heading);\r\n            padding: 8px 10px;\r\n            box-shadow: none;\r\n            text-transform: uppercase;\r\n        }\r\n    }\r\n    .collection-thumbnail {\r\n        position: relative;\r\n        img {\r\n            border-radius: 8px;\r\n            width: 100%;\r\n            @media (max-width: 991px) {\r\n                height: 370px;\r\n                object-fit: cover;\r\n                object-position: left;\r\n            }\r\n        }\r\n    }\r\n    &.product-collection-two {\r\n        .collection-content {\r\n            left: 50px;\r\n            .title {\r\n                margin-bottom: 20px;\r\n            }\r\n            .price-warp {\r\n                margin-bottom: 50px;\r\n                .price-text {\r\n                    margin-bottom: 0;\r\n                }\r\n            }\r\n            .plus-btn {\r\n                left: 40%;\r\n                top: 30%;\r\n                .plus-icon {\r\n                    background-color: var(--color-white);\r\n                    &:hover {\r\n                        background-color: var(--color-primary);\r\n                        color: var(--color-white);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n.product-collection-three {\r\n    display: flex;\r\n    background-color: var(--color-white);\r\n    border-radius: 8px;\r\n    padding: 30px 35px 0 20px;\r\n    margin-bottom: 30px;\r\n    min-height: 185px;\r\n    .collection-content {\r\n        flex: 1;\r\n        .title {\r\n            font-weight: 700;\r\n            a {\r\n                transition: 0.3s;\r\n            }\r\n        }\r\n        .price-warp {\r\n            .price-text {\r\n                font-size: 14px;\r\n                font-weight: 500;\r\n                display: block;\r\n            }\r\n            .price {\r\n                line-height: 1.2;\r\n                font-weight: 700;\r\n                color: var(--color-secondary);\r\n            }\r\n        }\r\n    }\r\n    .collection-thumbnail {\r\n        position: relative;\r\n        z-index: 1;\r\n        width: 70px;\r\n        &:before {\r\n            content: \"\";\r\n            height: 118px;\r\n            width: 118px;\r\n            background-color: var(--color-lighter);\r\n            border-radius: 50%;\r\n            position: absolute;\r\n            bottom: 10px;\r\n            right: -23px;\r\n            z-index: -1;\r\n        }\r\n    }\r\n}\r\n\r\n", "/*----------------------------\r\n    Product Details Styles  \r\n----------------------------*/\r\n.single-product-thumbnail {\r\n    &.thumbnail-badge {\r\n        .thumbnail {\r\n            padding-right: 45px;\r\n            position: relative;\r\n            @media #{$large-mobile} {\r\n                padding-right: 20px;\r\n            }                \r\n            .label-block {\r\n                position: absolute;\r\n                top: 30px;\r\n                &.label-right {\r\n                    right: 0;\r\n                }\r\n                .product-badget {\r\n                    background-color: var(--color-primary);\r\n                    line-height: 1;\r\n                    padding: 6px 10px 5px;\r\n                    font-size: 12px;\r\n                    font-weight: 700;\r\n                    color: #FFFFFF;\r\n                    border-radius: 4px;\r\n                    \r\n                }\r\n            } \r\n        }\r\n    }\r\n    .thumbnail {\r\n        img {\r\n            width: 100%;\r\n            border-radius: 6px;\r\n        }\r\n    }\r\n    &:hover {\r\n        .thumbnail {\r\n            a {\r\n                img {\r\n                    transform: scale(1);\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n.single-product-content {\r\n    .inner {\r\n        // Product Rating \r\n        .product-rating {\r\n            display: flex;\r\n            align-items: center;\r\n            margin-bottom: 20px;\r\n            line-height: 1;\r\n            padding-bottom: 20px;\r\n            border-bottom: 2px solid #F6F7FB;\r\n            .star-rating {\r\n                margin-right: 8px; \r\n                font-size: 14px;\r\n                color: #FFDC60;\r\n            }\r\n            .review-link {\r\n                a {\r\n                    font-size: 16px;\r\n                    line-height: 24px;\r\n                    color: var(--color-body);\r\n                    transition: var(--transition);\r\n                    &:hover {\r\n                        color: var(--color-heading);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        .product-title {\r\n            margin-bottom: 18px;\r\n            color: var(--color-dark);\r\n        }\r\n        .price-amount {\r\n            font-weight: 500;\r\n            font-size: 24px;\r\n            font-family: var(--font-secondary);\r\n            display: block;\r\n            margin-bottom: 20px;\r\n            color: var(--color-black);\r\n            @media #{$sm-layout} {\r\n                font-size: 20px;\r\n            }\r\n            &.price-offer-amount {\r\n                display: flex;\r\n                align-items: center;\r\n                margin: 0 -10px 20px;\r\n                @media #{$small-mobile} {\r\n                    margin: 0 -4px 20px;\r\n                }\r\n                span {\r\n                    display: inline-block;\r\n                    margin: 0 10px;\r\n                    @media #{$small-mobile} {\r\n                        margin: 0 4px;\r\n                    }\r\n                }\r\n                .old-price {\r\n                    color: var(--color-body);\r\n                    text-decoration: line-through;\r\n                }\r\n                .offer-badge {\r\n                    background-color: var(--color-chart03);\r\n                    height: 48px;\r\n                    line-height: 40px;\r\n                    padding: 5px 20px;\r\n                    font-size: 16px;\r\n                    color: var(--color-white);\r\n                    border-radius: 24px;\r\n                    font-family: var(--font-secondary);\r\n                }\r\n            }\r\n        }\r\n        .product-meta {\r\n            @extend %liststyle;\r\n            margin-bottom: 20px;\r\n            li {\r\n                color: var(--color-primary);\r\n                font-weight: 500;\r\n                font-size: 16px;\r\n                line-height: 24px;\r\n                display: flex;\r\n                align-items: center;\r\n                margin: 0;\r\n                i {\r\n                    padding-right: 15px;\r\n                    font-size: 18px;\r\n                }\r\n            }\r\n        }\r\n        .description {\r\n            margin-bottom: 30px;\r\n        }\r\n        .product-variation {\r\n            margin-bottom: 30px;\r\n            display: flex;\r\n            align-items: center;\r\n            .title {\r\n                font-weight: 500;\r\n                font-size: 20px;\r\n                margin-bottom: 0;\r\n                min-width: 114px;\r\n                @media #{$sm-layout} {\r\n                    font-size: 18px;\r\n                    min-width: 90px;\r\n                    \r\n                }\r\n            }\r\n            .color-variant {\r\n                margin: -5px !important;\r\n                li {\r\n                    margin: 5px;\r\n                    >span {\r\n                        .color {\r\n                            width: 11px;\r\n                            height: 11px;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            .variable-items-wrapper {\r\n                display: flex;\r\n                align-items: center;\r\n                @extend %liststyle;\r\n                &.color-variable-wrapper {\r\n                    li {\r\n                        &.color-variable-item {\r\n                            .variable-item-span {\r\n                                padding: 5px;\r\n                                border: 1px solid transparent;\r\n                                display: block;\r\n                                border-radius: 100%;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            &.product-size-variation {\r\n                align-items: flex-start;\r\n                .title {\r\n                    margin-top: 8px;\r\n                }\r\n\r\n            }\r\n        }\r\n        .product-features {\r\n            margin-bottom: 20px;\r\n            tr {\r\n                &:first-child {\r\n                    td {\r\n                        border-top: 1px solid var(--color-light);\r\n                    }\r\n                }\r\n                td {\r\n                    border-bottom: 1px solid var(--color-light);\r\n                    padding: 15px 20px;\r\n                    @media #{$small-mobile} {\r\n                        padding: 15px;\r\n                    }\r\n                    &:first-child {\r\n                        padding-left: 0;\r\n                    }\r\n                    &:last-child {\r\n                        padding-right: 0;\r\n                        text-align: right;\r\n                    }\r\n                }\r\n            }\r\n            .pro-qty {\r\n                .qtybtn {\r\n                    font-size: 20px;\r\n                }\r\n            }\r\n            .title {\r\n                margin-bottom: 0;\r\n                color: var(--color-black);\r\n                @media #{$small-mobile} {\r\n                    font-size: 17px;\r\n                }\r\n            }\r\n            .price-amount {\r\n                margin: 0;\r\n                padding: 0;\r\n                border: none;\r\n                color: var(--color-body);\r\n            }\r\n            .mini-btn {\r\n                display: inline-block;\r\n                width: auto;\r\n                font-size: var(--font-size-b3);\r\n                color: var(--color-white);\r\n                font-weight: var(--p-medium);\r\n                background-color: var(--color-body);\r\n                border-radius: 20px;\r\n                padding: 5px 14px;\r\n                min-width: 100px;\r\n                text-align: center;\r\n            }\r\n        }\r\n        .nft-short-meta {\r\n            border-bottom: 1px solid var(--color-border-light);\r\n            padding-bottom: 30px;\r\n            margin-bottom: 30px;\r\n            margin-top: 30px;\r\n        }\r\n        .nft-category, \r\n        .nft-verified-option {\r\n            display: flex;\r\n            align-items: center;\r\n            label {\r\n                font-size: 15px;\r\n                display: block;\r\n                margin-right: 10px;\r\n            }\r\n            .category-list {\r\n                a {\r\n                    transition: .3s;\r\n                }\r\n            }\r\n        }\r\n        .nft-category {\r\n            label {\r\n                font-size: 20px;\r\n                font-weight: 500;\r\n                color: var(--color-heading);\r\n            }\r\n        }\r\n        .nft-verified-option {\r\n            justify-content: flex-end;\r\n            @media #{$sm-layout} {\r\n                justify-content: flex-start; \r\n                margin-top: 20px;\r\n            }\r\n            .verify-btn {\r\n                width: auto;\r\n                padding: 12px 30px;\r\n            }\r\n        } \r\n    }\r\n    &.nft-single-product-content {\r\n        .inner {\r\n            .price-amount {\r\n                border-bottom: none;\r\n                &.price-offer-amount {\r\n                    padding-bottom: 10px;\r\n                }\r\n            }\r\n            .product-title {\r\n                margin-bottom: 20px;\r\n            }\r\n        }\r\n        .product-action-wrapper {\r\n            @media #{$small-mobile} {\r\n                flex-direction: row;\r\n            }\r\n            .product-action {\r\n                &.action-style-two {\r\n                    padding-right: 0;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.product-action-wrapper {\r\n    @media #{$small-mobile} {\r\n        flex-direction: column;\r\n        .pro-qty {\r\n            margin-bottom: 20px;\r\n        }\r\n    }\r\n    .product-action {\r\n        flex: 1;\r\n        .add-to-cart {\r\n            flex: 1;\r\n            .axil-btn {\r\n                width: 100%;\r\n                text-align: center;\r\n                display: block;\r\n                &:hover {\r\n                    &:before {\r\n                        transform: scale(1.05);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        &.action-style-two {\r\n            margin: 0 -10px;\r\n            padding-right: 220px;\r\n            @media #{$lg-layout} {\r\n                padding-right: 0;\r\n            }\r\n            @media #{$large-mobile} {\r\n                padding-right: 0;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.product-action {\r\n    list-style: none;\r\n    padding: 0;\r\n    li {\r\n        margin: 0 10px;\r\n        .axil-btn {\r\n            @media #{$lg-layout} {\r\n                padding: 16px 20px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.pro-qty {\r\n    width: 130px;\r\n    border-radius: 50px;\r\n    input {\r\n        width: 28px;\r\n        float: left;\r\n        border: none;\r\n        height: 32px;\r\n        line-height: 30px;\r\n        padding: 0;\r\n        text-align: center;\r\n        background-color: transparent;\r\n        font-size: 20px;\r\n        font-weight: 500;\r\n        margin: 0 12px;\r\n        color: #27272e;\r\n    }\r\n    .qtybtn {\r\n        width: 32px;\r\n        display: block;\r\n        float: left;\r\n        line-height: 26px;\r\n        cursor: pointer;\r\n        text-align: center;\r\n        font-size: 16px;\r\n        font-weight: 300;\r\n        color: #000;\r\n        height: 32px;\r\n        background: #F6F7FB;\r\n        border-radius: 50%;\r\n        transition: 0.3s;\r\n        border: 2px solid transparent;\r\n        &:hover {\r\n            border-color: var(--color-primary);\r\n        }\r\n    }\r\n    input::-webkit-outer-spin-button,\r\n    input::-webkit-inner-spin-button {\r\n      -webkit-appearance: none;\r\n      margin: 0;\r\n    }\r\n    input[type=number] {\r\n      -moz-appearance: textfield;\r\n    }\r\n}\r\n\r\n.product-quick-view {\r\n    a {\r\n        background: #fff;\r\n        width: 48px;\r\n        height: 48px;\r\n        display: flex !important;\r\n        align-items: center;\r\n        justify-content: center;\r\n        border-radius: 50%;\r\n        cursor: pointer;\r\n        transition: 0.3s;\r\n        &:hover {\r\n            background: var(--color-primary);\r\n            color: #fff;\r\n        }\r\n    }\r\n}\r\n\r\n.position-view {\r\n    position: absolute;\r\n    bottom: 47px;\r\n    right: 92px;\r\n    z-index: 4;\r\n    @media #{$large-mobile} {\r\n        bottom: 20px;\r\n        right: 40px;\r\n    }\r\n}\r\n\r\n.small-thumb-wrapper {\r\n    @media only screen and (max-width: 991px) {\r\n        margin-top: 10px; \r\n        .slick-list {\r\n            margin: 0 -10px;\r\n            .slick-slide {\r\n                margin: 10px;\r\n            }\r\n        }  \r\n    }\r\n    .small-thumb-img {\r\n        position: relative;\r\n        overflow: hidden;\r\n        border-radius: 10px;\r\n        margin-bottom: 20px;\r\n        cursor: pointer;\r\n        transition: all 0.4s;\r\n        img {\r\n            border-radius: 10px;\r\n            border: 2px solid transparent;\r\n            width: 80px;\r\n            height: auto;\r\n            transition: all 0.2s;\r\n        }\r\n        &:hover,\r\n        &.slick-current {\r\n            img {\r\n                border-color: var(--color-primary);\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.small-thumb-style-two {\r\n    .small-thumb-img {\r\n        img {\r\n            width: 60px;\r\n            height: auto;\r\n            border-radius: 50%;\r\n            display: inline-block;\r\n            @media #{$lg-layout} {\r\n                width: 50px;\r\n                height: 50px; \r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.single-product-thumb {\r\n    &.bg-vista-white {\r\n        .single-product-content {\r\n            .inner {\r\n                .product-rating {\r\n                    border-bottom-color: #e9e9e9;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.single-product-thumbnail-wrap {\r\n    position: relative;\r\n    .label-block {\r\n        position: absolute;\r\n        top: 30px;\r\n        right: 30px;\r\n        .product-badget {\r\n            background-color: var(--color-primary);\r\n            line-height: 1;\r\n            padding: 6px 10px 5px;\r\n            font-size: 12px;\r\n            font-weight: 700;\r\n            color: #FFFFFF;\r\n            border-radius: 4px;\r\n            box-shadow: 0 8px 16px 0 rgba(53, 119, 240, .30);\r\n            \r\n        }\r\n\r\n    }\r\n    .product-quick-view {\r\n        right: 30px;\r\n        bottom: 30px;\r\n    }\r\n}\r\n\r\n// product tabs\r\n.woocommerce-tabs {\r\n    &.wc-tabs-wrapper {\r\n        padding: 80px 0 35px;\r\n        @media #{$sm-layout} {\r\n            padding: 60px 0 15px;\r\n        }\r\n    }\r\n    ul.tabs {\r\n        margin: 0 -20px 60px;\r\n        @media #{$large-mobile} {\r\n            border-bottom: 1px solid #c7c7c7;\r\n            padding-bottom: 20px;\r\n        }\r\n        @media #{$large-mobile} {\r\n            flex-direction: column;\r\n            align-items: center;\r\n            margin: 0 0 60px;\r\n        }\r\n\r\n        li {\r\n            margin: 0 20px;\r\n            @media #{$large-mobile} {\r\n                margin: 10px 0;\r\n            }\r\n            a {\r\n                font-size: 24px;\r\n                line-height: 25px;\r\n                font-weight: 500;\r\n                display: block;\r\n                color: var(--color-body);\r\n                position: relative;\r\n                &:after {\r\n                    content: \"\";\r\n                    height: 2px;\r\n                    width: 0;\r\n                    background-color: var(--color-primary);\r\n                    position: absolute;\r\n                    bottom: -5px;\r\n                    right: 0;\r\n                    opacity: 0;\r\n                    transition: 0.5s;\r\n                }\r\n                &.active,\r\n                &:hover {\r\n                    color: var(--color-primary);\r\n                    &:after {\r\n                        width: 100%;\r\n                        left: 0;\r\n                        opacity: 1;\r\n                    }\r\n                }\r\n                @media #{$smlg-device} {\r\n                    font-size: 22px;\r\n                }\r\n                @media #{$sm-layout} {\r\n                    font-size: 20px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &.nft-info-tabs {\r\n        padding: 30px 15px;\r\n        margin-top: 30px;\r\n        border-radius: 6px;\r\n        @media #{$large-mobile} {\r\n            padding: 30px 0;\r\n        }\r\n         ul.tabs {\r\n            margin: 0 -5px 10px;\r\n            @media #{$large-mobile} {\r\n                margin: 0 0 30px;\r\n            }\r\n             li {\r\n                margin: 0 5px;\r\n                @media #{$large-mobile} {\r\n                    margin: 5px 0;\r\n                }\r\n                 a {\r\n                    font-size: 18px;\r\n                    background-color: rgba(255,255,255, 0.7);\r\n                    border-radius: 6px;\r\n                    padding: 10px 25px;\r\n                    position: relative;\r\n                    @media only screen and (min-width: 1200px) and (max-width: 1399px) {\r\n                        font-size: 15px;\r\n                    }\r\n                    @media #{$lg-layout} {\r\n                        font-size: 15px;\r\n                        padding: 5px 11px;\r\n                    }\r\n                    @media #{$sm-layout} {\r\n                        font-size: 15px;\r\n                        padding: 5px 11px;\r\n                    }\r\n                    &:before {\r\n                        content: \". . .\";\r\n                        color: var(--color-border-light);\r\n                        position: absolute;\r\n                        bottom: -2px;\r\n                        left: 50%;\r\n                        transform: translateX(-50%);\r\n                        visibility: hidden;\r\n                        opacity: 0;\r\n                        transition: 0.3s;\r\n                        z-index: 1;\r\n                        line-height: 1;\r\n                    }\r\n                    &:after {\r\n                        content: \"\";\r\n                        height: 12px;\r\n                        width: 100%;\r\n                        background-color: var(--color-white);\r\n                        position: absolute;\r\n                        bottom: -12px;\r\n                        left: 0;\r\n                        right: 0;\r\n                        visibility: hidden;\r\n                        opacity: 0;\r\n                        transition: 0.3s;\r\n                        @media #{$large-mobile} {\r\n                            display: none;\r\n                        }\r\n                    }\r\n                    &.active {\r\n                        border-radius: 6px 6px 0 0;\r\n                        background-color: rgba(255,255,255, 1);\r\n                        @media #{$large-mobile} {\r\n                            border-radius: 6px;\r\n                        }\r\n                        &:before {\r\n                            visibility: visible;\r\n                            opacity: 1;\r\n                        }\r\n                        &:after {\r\n                            visibility: visible;\r\n                            opacity: 1;\r\n                        }\r\n                    }\r\n                 }\r\n             }\r\n        }\r\n        .tab-content {\r\n            background-color: var(--color-white);\r\n            padding: 30px;\r\n            border-radius: 6px;\r\n            @media #{$small-mobile} {\r\n                padding: 20px;\r\n            }\r\n        }\r\n        .product-additional-info {\r\n            padding: 0;\r\n            margin-bottom: 0;\r\n            table tbody tr th, table tbody tr td {\r\n                padding: 10px 20px 10px;\r\n                min-width: 120px;\r\n            }\r\n        }\r\n    }\r\n    &.wc-tab-style-two {\r\n        padding: 80px 0 50px;\r\n        @media #{$sm-layout} {\r\n            padding: 60px 0 30px;\r\n        }\r\n        .tabs-wrap {\r\n            background-color: var(--color-white);\r\n            border-radius: 8px;\r\n            padding: 30px;\r\n            margin-bottom: 30px;\r\n            ul.tabs {\r\n                border-bottom: 2px solid #EBEBEB;\r\n                margin: 0 0 30px 0;\r\n                li {\r\n                    margin: 0;\r\n                    margin-right: 20px;\r\n                    a {\r\n                        font-size: 20px;\r\n                        font-weight: 700;\r\n                        padding: 10px 15px;\r\n                        &:after {\r\n                            bottom: -2px;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            .product-desc-wrapper {\r\n                .title {\r\n                    margin-bottom: 12px;\r\n                }\r\n                ul {\r\n                    list-style-type: disc;\r\n                    li {\r\n                        font-size: 18px;\r\n\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        .pro-des-commend-respond .form-group label {\r\n            background-color: var(--color-white);\r\n        }\r\n    }\r\n}\r\n\r\n.single-product-features {\r\n    .single-features {\r\n        background-color: var(--color-white);\r\n        margin-bottom: 30px; \r\n        display: flex;\r\n        align-items: center;\r\n        padding: 25px 30px;\r\n        border-radius: 8px;\r\n        .icon {\r\n            width: 77px;\r\n            height: 77px;\r\n            line-height: 77px;\r\n            margin-right: 16px;\r\n            font-size: 40px;\r\n            position: relative;\r\n            z-index: 1;\r\n            text-align: center;\r\n            color: var(--color-primary);\r\n            &:before {\r\n                content: \"\";\r\n                height: 100%;\r\n                width: 100%;\r\n                background-color: #F6F7FB;\r\n                border-radius: 50%;\r\n                position: absolute;\r\n                top: 0;\r\n                left: 0;\r\n                z-index: -1;\r\n\r\n            }\r\n            &.quality {\r\n                color: var(--color-secondary);\r\n            }\r\n            &.original {\r\n                color: var(--light-primary);\r\n            }\r\n        }\r\n        .content {\r\n            flex: 1;\r\n            .title {\r\n                margin-bottom: 5px;\r\n                font-weight: 700;\r\n            }\r\n            p {\r\n                font-size: 14px;\r\n\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.product-desc-wrapper {\r\n    .desc-heading {\r\n        @media #{$sm-layout} {\r\n            font-size: 24px;\r\n        }\r\n    }\r\n}\r\n\r\n.single-desc {\r\n    .title {\r\n        margin-bottom: 20px;\r\n    }\r\n}\r\n\r\n.pro-des-features {\r\n    padding: 0;\r\n    list-style: none;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    margin-left: -30px;\r\n    margin-right: -30px;\r\n    li {\r\n        padding: 15px 30px;\r\n        margin: 0;\r\n        font-size: 20px;\r\n        font-weight: 500;\r\n        color: var(--color-dark);\r\n        @media #{$sm-layout} {\r\n            font-size: 20px;\r\n        }\r\n    }\r\n    .icon {\r\n        width: 60px;\r\n        height: 60px;\r\n        background: #fff;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        border-radius: 50%;\r\n        margin: 20px 0;\r\n        img {\r\n            max-width: 30px;\r\n        }\r\n    }\r\n}\r\n\r\n.pro-desc-style-two {\r\n    margin: 0 -15px;\r\n    li {\r\n        padding: 15px;\r\n    }\r\n}\r\n\r\n.product-additional-info {\r\n    background: #fff;\r\n    padding: 50px;\r\n    border-radius: 6px;\r\n    margin-bottom: 40px;\r\n    @media #{$sm-layout} {\r\n        padding: 20px 15px 0;\r\n    }\r\n    table {\r\n        margin-bottom: 0;\r\n        tbody {\r\n            tr {\r\n                &:nth-child(odd) {\r\n                    background: var(--color-lighter);\r\n                }\r\n                th,\r\n                td {\r\n                    font-size: 16px;\r\n                    line-height: 24px;\r\n                    font-weight: 400;\r\n                    padding: 17px 30px 18px;\r\n                    min-width: 200px;\r\n                    @media #{$sm-layout} {\r\n                        padding: 15px;\r\n                        font-size: 15px;\r\n                    }\r\n                }\r\n                th {\r\n                    text-transform: capitalize;\r\n                    color: #292930;\r\n                    font-weight: 500;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.pro-desc-commnet-area {\r\n    padding-right: 110px;\r\n    @media #{$smlg-device} {\r\n        padding-right: 30px;\r\n    }\r\n    @media only screen and (max-width: 991px) {\r\n      padding-right: 0;\r\n        \r\n    }\r\n    .comment-list {\r\n        .comment {\r\n            .commenter {\r\n                margin-bottom: 0;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: space-between;\r\n                @media #{$large-mobile} {\r\n                    display: block;\r\n                }\r\n                .hover-flip-item-wrapper,\r\n                .commenter-rating {\r\n                    margin-bottom: 5px;\r\n                    a {\r\n                        font-size: 12px;\r\n                        i {\r\n                            color: #cecece;\r\n                            &:not(.empty-rating) {\r\n                                color: #ffca0f;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n                .commenter-rating {\r\n                    margin-left: 15px;\r\n                    @media #{$small-mobile} {\r\n                        display: block;\r\n                        margin-bottom: 5px;\r\n                        margin-left: 0;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.pro-des-commend-respond {\r\n    .form-group {\r\n        textarea,\r\n        input {\r\n            background-color: transparent;\r\n        }\r\n        label {\r\n            background-color: #f9f3f0;\r\n        }\r\n        textarea {\r\n            padding-left: 30px;\r\n            padding-top: 20px;\r\n            font-size: 14px;\r\n            line-height: 1.5;\r\n        }\r\n    }\r\n}\r\n\r\n.small-thumb-wrapper {\r\n    .slick-track {\r\n        margin-left: 0;\r\n    }\r\n}  \r\n\r\n.small-thumb-style-three {\r\n    margin: 30px 60px 0;\r\n    @media #{$lg-layout} {\r\n        margin: 30px 0 0;\r\n    }\r\n    @media #{$sm-layout} {\r\n        margin: 30px 0 0;\r\n    }\r\n\r\n    .small-thumb-img {\r\n        margin: 10px;\r\n    }\r\n}\r\n\r\n\r\n\r\n/* Quick View Modal */\r\n.quick-view-product {\r\n    .modal-dialog {\r\n        max-width: 1100px;\r\n    }\r\n    .modal-content {\r\n        border: none;\r\n    }\r\n    .modal-header {\r\n        padding: 30px 15px;\r\n        justify-content: flex-end;\r\n        .btn-close {\r\n            width: auto;\r\n            background-image: none;\r\n            font-size: 14px;\r\n            padding: 0 10px;\r\n            transition: var(--transition);\r\n            position: relative;\r\n            right: 10px;\r\n            z-index: 1;\r\n            &:after {\r\n                content: \"\";\r\n                height: 35px;\r\n                width: 35px;\r\n                background: var(--color-primary);\r\n                border-radius: 50%;\r\n                position: absolute;\r\n                top: -9px;\r\n                left: -3px;\r\n                transform: scale(0);\r\n                z-index: -1;\r\n                transition: var(--transition);\r\n            }\r\n            &:hover {\r\n                color: var(--color-white);\r\n                &:after {\r\n                    transform: scale(1);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .modal-body {\r\n        padding: 30px;\r\n    }\r\n}\r\n\r\n.mfp-wrap {\r\n    z-index: 1060;\r\n}\r\n.mfp-bg {\r\n    z-index: 1055;\r\n}\r\n\r\n\r\n\r\n// New Style\r\n.single-product-modern {\r\n    .single-product-content {\r\n        .inner {\r\n            .price-amount {\r\n                font-weight: 700;\r\n                color: var(--color-primary);\r\n                margin-bottom: 10px;\r\n            }\r\n            .product-rating {\r\n                border-bottom: none;\r\n                padding-bottom: 0;\r\n                margin-bottom: 30px;\r\n                .star-rating {\r\n                    color: #FACC15;\r\n                }\r\n                .review-number {\r\n                    font-size: 14px;\r\n                    font-weight: 700;\r\n                    color: var(--color-heading);\r\n                }\r\n                .total-answerd {\r\n                    font-size: 14px;\r\n                    border-left: 1px solid #D6D6D6;\r\n                    padding-left: 8px;\r\n                    margin-left: 8px;\r\n                }\r\n            }\r\n            .description {\r\n                list-style: disc;\r\n                li {\r\n                    font-weight: 500;\r\n                }\r\n            }\r\n            .product-variation {\r\n                display: block;\r\n                margin-bottom: 20px;\r\n                .title {\r\n                    margin-bottom: 10px;\r\n                    font-size: 18px;\r\n                }\r\n            }\r\n            .range-variant {\r\n                li {\r\n                    border-radius: 4px;\r\n                    background-color: var(--color-lighter);\r\n                    border: none;\r\n                    width: 40px;\r\n                    height: 40px;\r\n                    font-size: 14px;\r\n                    font-weight: 700;\r\n                    &:hover, &.active {\r\n                        color: var(--color-white);\r\n                        background-color: var(--color-primary);\r\n                    }\r\n                }\r\n            }\r\n            .color-variant {\r\n                @media (max-width: 575px) {\r\n                    justify-content: flex-start;\r\n                }\r\n                li {\r\n                    >span {\r\n                        border: 1px solid;\r\n                        height: 24px;\r\n                        width: 24px;\r\n                        .color {\r\n                            height: 24px;\r\n                            width: 24px;\r\n                        }\r\n                    }\r\n                    &.active {\r\n                        >span {\r\n                            .color {\r\n                                width: 12px;\r\n                                height: 12px;\r\n                            }\r\n                        }\r\n                    }\r\n                    &.color-extra-01 {\r\n                        >span {\r\n                            border-color: #AAE6F8;\r\n                        }\r\n                    }\r\n                    &.color-extra-02 {\r\n                        >span {\r\n                            border-color: #5F8AF7;\r\n                        }\r\n                    }\r\n                    &.color-extra-03 {\r\n                        >span {\r\n                            border-color: #59C3C0;\r\n                        }\r\n                    }\r\n                    &.color-extra-04 {\r\n                        >span {\r\n                            border-color: #D3BBF3;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            .quantity-variant-wrapper {\r\n                margin-bottom: 30px;\r\n                .pro-qty {\r\n                    min-height: 40px;\r\n                    width: auto;\r\n                    .qtybtn {\r\n                        width: 40px;\r\n                        height: 40px;\r\n                        line-height: 34px;\r\n                        border-radius: 8px;\r\n                        font-size: 20px;\r\n                    }\r\n                    input {\r\n                        border: 1px solid #D8D8D8;\r\n                        border-radius: 8px;\r\n                        height: 40px;\r\n                        width: 40px;\r\n                        font-size: 14px;\r\n                        margin: 0 10px;\r\n                    }\r\n                }\r\n            }\r\n\r\n            .product-action-wrapper {\r\n                .product-action {\r\n                    margin: -10px;\r\n                    width: 84%;\r\n                    @media (max-width: 1199px) {\r\n                        width: 100%;\r\n                    }\r\n                    @media (max-width: 575px) {\r\n                        display: block;\r\n                    }\r\n                    li {\r\n                        margin: 10px;\r\n                       a {\r\n                        padding: 12px 38px;\r\n                        i {\r\n                            font-size: 20px;\r\n                        }\r\n                       } \r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .single-product-thumbnail {\r\n        margin-bottom: 20px;\r\n        .thumbnail {\r\n            img {\r\n                border-radius: 8px;\r\n            }\r\n        }\r\n    }\r\n    .small-thumb-wrapper {\r\n        margin: 0 30px;\r\n        .small-thumb-img {\r\n            margin-bottom: 0;\r\n            border-radius: 8px;\r\n            img {\r\n                width: 100%;\r\n                border-radius: 8px;\r\n                border-width: 1px;\r\n            }\r\n        }\r\n        &.axil-slick-arrow {\r\n            .slide-arrow {\r\n                height: 40px;\r\n                width: 40px;\r\n                border-radius: 50%;\r\n                border: 2px solid var(--color-white);\r\n                font-size: 18px;\r\n                left: -25px;\r\n                &:before {\r\n                    border-radius: 50%;\r\n                }\r\n                &.next-arrow {\r\n                    right: -24px;\r\n                    left: auto;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}", "/*-----------------------\r\n    Checkout Styles  \r\n-------------------------*/\r\n.product-table-heading {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\t.title {\r\n\t\tmargin-bottom: 20px;\r\n\t\tfont-weight: 500;\r\n\t\tdisplay: inline-block;\r\n\t}\r\n\t.cart-clear {\r\n\t\tdisplay: inline-block;\r\n\t\tfont-size: 14px;\r\n\t\tcolor: var(--color-primary);\r\n\t\ttransition: var(--transition);\r\n\t\t&:hover {\r\n\t\t\tcolor: var(--color-black);\r\n\t\t}\r\n\t\t\r\n\t}\r\n}\r\n\r\n.axil-product-table {\r\n\tfont-family: var(--font-secondary);\r\n\tmargin: 0;\r\n\t@media #{$sm-layout} {\r\n\t\tmargin-top: 20px;\r\n\t}\r\n\tth, td {\r\n\t\t&:last-child {\r\n\t\t\ttext-align: right;\r\n\t\t\t@media #{$sm-layout} {\r\n\t\t\t\tborder-bottom: none;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\tthead {\r\n\t\tbackground-color: var(--color-lighter);\r\n\t\t@media #{$sm-layout} {\r\n\t\t\tdisplay: none;\r\n\t\t}\r\n\t\tth {\r\n\t\t\tfont-size: 20px;\r\n\t\t\ttext-transform: capitalize;\r\n\t\t\tborder: none;\r\n\t\t\tcolor: var(--color-heading);\r\n\t\t\tpadding: 18px 15px;\r\n\t\t\t@media only screen and (max-width: 991px) {\r\n\t\t\t\tfont-size: 18px;\r\n\t\t\t\tpadding: 18px 10px;\r\n\t\t\t}\r\n\t\t\t&:first-child {\r\n\t\t\t\tborder-radius: 6px 0 0 6px;\r\n\t\t\t}\r\n\t\t\t&:last-child {\r\n\t\t\t\tborder-radius: 0 6px 6px 0;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\ttbody {\r\n\t\tborder-top: none !important;\r\n\t\ttr {\r\n\t\t\t@media #{$sm-layout} {\r\n\t\t\t\tpadding-left: 120px;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\tborder-bottom: 1px solid var(--color-lighter);\r\n\t\t\t\tmargin-bottom: 30px;\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\tmargin-bottom: 0;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t@media #{$large-mobile} {\r\n\t\t\t\tpadding-left: 90px;\r\n\t\t\t}\r\n\t\t}\r\n\t\ttd {\r\n\t\t\tborder-top: none;\r\n\t\t\tborder-bottom: 2px solid var(--color-lighter);\r\n\t\t\tvertical-align: middle;\r\n\t\t\tpadding: 15px;\r\n\t\t\tfont-size: 20px;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tcolor: var(--color-body);\r\n\t\t\tmin-width: 150px;\r\n\t\t\t@media only screen and (max-width: 991px) {\r\n\t\t\t\tfont-size: 18px;\r\n\t\t\t\tpadding: 10px 10px;\r\n\t\t\t}\r\n\t\t\t@media #{$sm-layout} {\r\n\t\t\t\tfont-size: 16px;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\ttext-align: right;\r\n\t\t\t\tpadding: 10px 10px 10px 0;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\t&:before {\r\n\t\t\t\t\tcontent: attr(data-title) \" :\";\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tleft: 0;\r\n\t\t\t\t\tfont-size: 13px;\r\n\t\t\t\t\tcolor: var(--color-black);\t\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t&.product-remove {\r\n\t\t\t\tmin-width: auto;\r\n\t\t\t\t@media #{$sm-layout} {\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\ttop: 0;\r\n\t\t\t\t\tright: 0;\r\n\t\t\t\t\tborder-bottom: none;\r\n\t\t\t\t\tz-index: 1;\r\n\t\t\t\t}\r\n\t\t\t\t.remove-wishlist {\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\theight: 32px;\r\n\t\t\t\t\twidth: 32px;\r\n\t\t\t\t\tline-height: 30px;\r\n\t\t\t\t\tbackground-color: var(--color-lighter);\r\n\t\t\t\t\tborder: 2px solid var(--color-lighter);\r\n\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\tfont-size: 12px;\r\n\t\t\t\t\tcolor: var(--color-black);\r\n\t\t\t\t\ttransition: var(--transition);\r\n\t\t\t\t\t@media #{$sm-layout} {\r\n\t\t\t\t\t\theight: 25px;\r\n\t\t\t\t\t\twidth: 25px;\r\n\t\t\t\t\t\tline-height: 22px;\r\n\t\t\t\t\t\tfont-size: 10px;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t&:hover {\r\n\t\t\t\t\t\tborder-color: var(--color-primary);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t&:before {\r\n\t\t\t\t\tdisplay: none;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t&.product-thumbnail {\r\n\t\t\t\tmin-width: 130px;\r\n\t\t\t\twidth: 130px;\r\n\t\t\t\t@media #{$sm-layout} {\r\n\t\t\t\t\tmin-width: 80px;\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tleft: 0;\r\n\t\t\t\t\ttop: 0;\r\n\t\t\t\t\tborder-bottom: none;\r\n\t\t\t\t\twidth: 100px;\r\n\t\t\t\t}\r\n\t\t\t\t@media #{$large-mobile} {\r\n\t\t\t\t\twidth: 80px;\r\n\t\t\t\t}\r\n\t\t\t\ta {\r\n\t\t\t\t\tborder-radius: 10px;\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\timg {\r\n\t\t\t\t\t\tborder-radius: 10px;\r\n\t\t\t\t\t\theight: 80px;\r\n    \t\t\t\t\twidth: 80px;\r\n\t\t\t\t\t\tobject-fit: cover;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t&:before {\r\n\t\t\t\t\tdisplay: none;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t&.product-title {\r\n\t\t\t\twidth: 30%;\r\n\t\t\t\tcolor: var(--color-black);\r\n\t\t\t\t@media #{$sm-layout} {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\ttext-align: left;\r\n\t\t\t\t\tpadding-right: 40px;\r\n\t\t\t\t}\r\n\t\t\t\ta {\r\n\t\t\t\t\ttransition: var(--transition);\r\n\t\t\t\t}\r\n\t\t\t\t&:before {\r\n\t\t\t\t\tdisplay: none;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t&.product-add-cart {\r\n\t\t\t\t.btn-outline {\r\n\t\t\t\t\tborder-color: #efefef;\r\n\t\t\t\t\tpadding: 10px 20px;\r\n\t\t\t\t\tfont-size: 14px;\r\n\t\t\t\t\t&:hover {\r\n\t\t\t\t\t\tborder-color: var(--color-primary);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t&:before {\r\n\t\t\t\t\tdisplay: none;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t&.axil-cart-table {\r\n\t\tth, td {\r\n\t\t\t&:first-child {\r\n\t\t\t\tpadding-left: 0;\r\n\t\t\t}\r\n\t\t\t&:last-child {\r\n\t\t\t\tpadding-right: 50px;\r\n\t\t\t\t@media #{$sm-layout} {\r\n\t\t\t\t\tpadding-right: 10px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.pro-qty {\r\n\t\t\twidth: auto;\r\n\t\t\t@media #{$sm-layout} {\r\n\t\t\t\tdisplay: inline-flex;\r\n\t\t\t}\r\n\t\t\tinput {\r\n\t\t\t\tmargin: 0 5px;\r\n\t\t\t\tmin-width: 45px;\r\n\t\t\t\t@media #{$sm-layout} {\r\n\t\t\t\t\tmin-width: 30px;\r\n\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.qtybtn {\r\n\t\t\t\tfont-size: 20px;\r\n\t\t\t\tline-height: 27px;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\t@media #{$md-layout} {\r\n\t\t\t\t\tfont-size: 18px;\r\n\t\t\t\t}\r\n\t\t\t\t@media #{$sm-layout} {\r\n\t\t\t\t\tfont-size: 16px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.quantity-input {\r\n\t\t\t\t@media #{$md-layout} {\r\n\t\t\t\t\tfont-size: 18px;\r\n\t\t\t\t}\r\n\t\t\t\t@media #{$sm-layout} {\r\n\t\t\t\t\tfont-size: 16px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.axil-product-cart-wrap {\r\n\t.cart-update-btn-area {\r\n\t\tdisplay: grid;\r\n\t\tgrid-template-columns: repeat(2, 1fr);\r\n\t\tgap: 30px;\r\n\t\t@media #{$sm-layout} {\r\n\t\t\tdisplay: block;\r\n\t\t}\r\n\t}\r\n\t.product-cupon {\r\n\t\tflex-wrap: nowrap;\r\n\t\tinput {\r\n\t\t\twidth: 100%;\r\n\t\t\tpadding: 0;\r\n\t\t\tborder-bottom: 2px solid #efefef;\r\n\t\t\tborder-radius: 0;\r\n\t\t\t@media #{$sm-layout} {\r\n\t\t\t\theight: 46px;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.product-cupon-btn {\r\n\t\t\tmargin-left: 20px !important;\r\n\t\t}\r\n\t\t.axil-btn {\r\n\t\t\twidth: auto;\r\n\t\t\tborder-width: 2px;\r\n\t\t\tborder-color: #efefef;\r\n\t\t\tbackground-color: transparent;\r\n\t\t\t&:hover {\r\n\t\t\t\tborder-color: var(--color-primary);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.update-btn {\r\n\t\ttext-align: right;\r\n\t\t@media #{$sm-layout} {\r\n\t\t\ttext-align: left;\r\n\t\t\tmargin-top: 30px;\r\n\t\t}\r\n\t\t.axil-btn {\r\n\t\t\tborder-width: 2px;\r\n\t\t\tborder-color: #efefef;\r\n\t\t\t&:hover {\r\n\t\t\t\tborder-color: var(--color-primary);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.axil-order-summery {\r\n\tbackground-color: #f9f3f0;\r\n\tborder-radius: 6px;\r\n\tpadding: 40px;\r\n\t@media #{$small-mobile} {\r\n\t\tpadding: 30px 20px;\r\n\t}\r\n\t.title {\r\n\t\tfont-weight: var(--s-medium);\r\n\t\t@media only screen and (max-width: 991px) {\r\n\t\t\tfont-size: 20px;\t\r\n\t\t}\r\n\t}\r\n\t.summery-table {\r\n\t\ttbody {\r\n\t\t\tborder-top: none !important;\r\n\t\t\ttd {\r\n\t\t\t\tborder-bottom: 1px solid;\r\n\t\t\t\tborder-color: rgba(101,105,115,0.2);\r\n\t\t\t\tfont-size: var(--font-size-b1);\r\n\t\t\t\tfont-weight: var(--s-medium);\r\n\t\t\t\tcolor: #292930;\r\n\t\t\t\tpadding: 18px 15px 18px 0;\r\n\t\t\t\tmin-width: 180px;\r\n\t\t\t\t@media #{$small-mobile} {\r\n\t\t\t\t\tmin-width: 90px;\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\t\t}\r\n\t\t.order-shipping {\r\n\t\t\t.input-group {\r\n\t\t\t\tmargin-bottom: 10px;\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\tmargin-bottom: 0;\r\n\t\t\t\t}\r\n\t\t\t\tlabel {\r\n\t\t\t\t\tcolor: #292930;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t&:before {\r\n\t\t\t\t\t\tborder-width: 2px;\r\n\t\t\t\t\t\tborder-color: #D5D4D4;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t&:after {\r\n\t\t\t\t\t\tbackground-color: var(--color-primary);\r\n\t\t\t\t\t\tborder: none;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.order-total-amount {\r\n\t\t\tfont-size: 20px;\r\n\t\t\tfont-weight: var(--s-bold);\r\n\t\t\tcolor: var(--color-primary);\r\n\t\t}\r\n\t}\r\n\t&.order-checkout-summery {\r\n\t\t.summery-table-wrap {\r\n\t\t\tbackground-color: var(--color-white);\r\n\t\t\tborder-radius: 6px;\r\n\t\t\tpadding: 30px;\r\n\t\t\tmargin-bottom: 45px;\r\n\t\t\t@media #{$small-mobile} {\r\n\t\t\t\tpadding: 0;\r\n\t\t\t\tbackground-color: transparent;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.summery-table {\r\n\t\t\tth {\r\n\t\t\t\tfont-size: 20px;\r\n\t\t\t\tcolor: var(--color-heading);\r\n\t\t\t\ttext-transform: capitalize;\r\n\t\t\t\tpadding: 15px 0;\r\n\t\t\t}\r\n\t\t\ttd {\r\n\t\t\t\tpadding: 18px 0;\r\n\t\t\t\t@media #{$lg-layout} {\r\n\t\t\t\t\tmin-width: 155px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t@media #{$large-mobile} {\r\n\t\t\t\t\tmin-width: 100px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t\r\n\t\t\t\t&.order-total-amount {\r\n\t\t\t\t\tcolor: var(--color-black);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tth, td {\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\ttext-align: right;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\ttr {\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\ttd {\r\n\t\t\t\t\t\tborder-bottom: none;\r\n\t\t\t\t\t\tpadding-bottom: 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.order-shipping {\r\n\t\t\t\t.shipping-amount {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\tmargin-bottom: 12px;\r\n\t\t\t\t\t.title {\r\n\t\t\t\t\t\tdisplay: inline-block;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\ttd {\r\n\t\t\t\t\ttext-align: left;\r\n\t\t\t\t}\r\n\t\t\t\t.input-group {\r\n\t\t\t\t\tmargin-bottom: 5px;\r\n\t\t\t\t\tlabel {\r\n\t\t\t\t\t\tcolor: var(--color-body);\r\n\t\t\t\t\t\tpadding-left: 26px;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.order-total {\r\n\t\t\t\ttd {\r\n\t\t\t\t\tfont-size: 20px;\r\n\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\tcolor: var(--color-black);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.order-payment-method {\r\n\t\t\t.single-payment {\r\n\t\t\t\tborder-bottom: 1px solid var(--color-light);\r\n\t\t\t\tmargin-bottom: 20px;\r\n\t\t\t\tpadding-bottom: 20px;\r\n\t\t\t\t.input-group {\r\n\t\t\t\t\tmargin-bottom: 20px;\r\n\t\t\t\t\tlabel {\r\n\t\t\t\t\t\tfont-size: 20px;\r\n\t\t\t\t\t\tcolor: #292930;\r\n\t\t\t\t\t\t@media #{$sm-layout} {\r\n\t\t\t\t\t\t\tfont-size: 18px;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t&:before {\r\n\t\t\t\t\t\t\tbackground-color: transparent;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t&:after {\r\n\t\t\t\t\t\t\tcontent: \"\\f00c\";\r\n\t\t\t\t\t\t\tfont-family: var(--font-awesome);\r\n\t\t\t\t\t\t\tfont-size: 8px;\r\n\t\t\t\t\t\t\tcolor: var(--color-white);\r\n\t\t\t\t\t\t\tfont-weight: 900;\r\n\t\t\t\t\t\t\tline-height: 8px;\r\n\t\t\t\t\t\t\ttransform: rotate(0deg);\r\n\t\t\t\t\t\t\tbackground-color: transparent;\r\n\t\t\t\t\t\t\tborder: none;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tinput[type=\"radio\"]:checked ~ label::before {\r\n\t\t\t\t\t\tbackground-color: var(--color-primary);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tp {\r\n\t\t\t\t\tpadding-left: 28px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.checkout-btn {\r\n\t\twidth: 100%;\r\n\t\ttext-align: center;\r\n\t\t&:hover {\r\n\t\t\t&:before {\r\n\t\t\t\ttransform: scale(1.05);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.axil-checkout-billing {\r\n\t.title {\r\n\t\tfont-weight: var(--s-medium);\r\n\t}\r\n\t.form-group {\r\n\t\tlabel {\r\n\t\t\tspan {\r\n\t\t\t\tcolor: var(--color-chart03);\r\n\t\t\t}\r\n\t\t}\r\n\t\tinput {\r\n\t\t\theight: 60px;\r\n\t\t\tborder-color: var(--color-light);\r\n\t\t\tpadding: 0 30px;\r\n\t\t}\r\n\t\ttextarea {\r\n\t\t\tborder-color: var(--color-light);\r\n\t\t\tpadding: 15px 30px;\r\n\t\t\tline-height: var(--line-height-b2);\r\n\t\t}\r\n\t\tselect {\r\n\t\t\tborder-color: var(--color-light);\r\n\t\t}\r\n\t\t&.input-group {\r\n\t\t\tmargin-bottom: 40px;\r\n\t\t\tlabel {\r\n\t\t\t\ttop: 0;\r\n\t\t\t\tleft: 0;\r\n\t\t\t\tpointer-events: auto;\r\n\t\t\t\tcolor: #292930;\r\n\t\t\t\t\r\n\t\t\t\t&:after {\r\n\t\t\t\t\tborder-width: 2px;\r\n\t\t\t\t\ttop: 6px;\r\n\t\t\t\t\theight: 6px;\r\n\t\t\t\t\twidth: 11px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t&.different-shippng {\r\n\t\t\tmargin-bottom: 40px;\r\n\t\t\t.toggle-bar {\r\n\t\t\t\tlabel {\r\n\t\t\t\tposition: initial;\r\n\t\t\t\tfont-size: 24px;\r\n\t\t\t\tpadding-left: 0;\r\n\t\t\t\tpadding-right: 28px;\r\n\t\t\t\twidth: 100%;;\r\n\t\t\t\tcolor: #292930;\r\n\t\t\t\t@media #{$sm-layout} {\r\n\t\t\t\t\tfont-size: 20px;\r\n\t\t\t\t}\r\n\t\t\t\t@media #{$small-mobile} {\r\n\t\t\t\t\tfont-size: 17px;\r\n\t\t\t\t}\r\n\t\t\t\t&:before {\r\n\t\t\t\t\tleft: auto;\r\n\t\t\t\t\tright: 0;\r\n\t\t\t\t\ttop: 6px;\r\n\t\t\t\t}\r\n\t\t\t\t&:after {\r\n\t\t\t\t\tleft: auto;\r\n\t\t\t\t\tright: 3px;\r\n\t\t\t\t\ttop: 10px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tinput {\r\n\t\t\t\tcursor: pointer\r\n\t\t\t}\r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t\t.toggle-open {\r\n\t\t\t\tdisplay: none;\r\n\t\t\t\tpadding: 10px 0 0;\r\n\t\t\t\tmargin-top: 20px;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\r\n}\r\n\r\n.axil-checkout-notice {\r\n\tmargin-bottom: 40px;\r\n\t.axil-toggle-box {\r\n\t\tmargin-bottom: 20px;\r\n\t}\r\n\t.toggle-bar {\r\n\t\tbackground-color: var(--color-lighter);\r\n\t\tborder-radius: 6px;\r\n\t\tpadding: 17px 30px;\r\n\t\ti {\r\n\t\t\tmargin-right: 8px;\r\n\t\t}\r\n\t\ta {\r\n\t\t\tfont-weight: 500;\r\n\t\t\ttransition: var(--transition);\r\n\t\t\ti {\r\n\t\t\t\tcolor: var(--color-body);\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tmargin-left: 5px;\r\n\t\t\t}\r\n\t\t\t&:focus {\r\n\t\t\t\tcolor: var(--color-heading);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.toggle-open {\r\n\t\tdisplay: none;\r\n\t\tmargin-top: 20px;\r\n\t}\r\n\t.axil-checkout-coupon {\r\n\t\tborder: 1px solid var(--color-light);\r\n\t\tborder-radius: 16px;\r\n\t\tpadding: 30px;\r\n\t\tp {\r\n\t\t\tfont-size: var(--font-size-b2);\r\n\t\t\tmargin-bottom: 20px;\r\n\t\t}\r\n\t\tinput {\r\n\t\t\tborder: 1px solid var(--color-light);\r\n\t\t\twidth: auto;\r\n\t\t\theight: 50px;\t\r\n\t\t\tmargin-right: 10px;\r\n\t\t\tmargin-bottom: 10px;\r\n\t\t\tborder-radius: 6px !important;\r\n\t\t}\r\n\r\n\t\t.axil-btn {\r\n\t\t\tborder-width: 1px;\r\n\t\t\tpadding: 12px 40px;\r\n\t\t\tborder-color: var(--color-light);\r\n\t\t\t&:hover {\r\n\t\t\t\tborder-color: var(--color-primary);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.axil-checkout-login {\r\n\t\tborder: 1px solid var(--color-light);\r\n\t\tborder-radius: 16px;\r\n\t\tpadding: 30px;\r\n\t\tp {\r\n\t\t\tmargin-bottom: 30px;\r\n\t\t}\r\n\t\tinput {\r\n\t\t\tborder-color: var(--color-light);\r\n\t\t}\r\n\t\t.axil-btn {\r\n\t\t\twidth: auto;\r\n\t\t\tpadding: 11px 40px;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n", "/*-----------------------\r\n My Account Dashboard \r\n-------------------------*/\r\n.axil-signin-area {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tbottom: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\theight: 100%;\r\n\twidth: 100%;\r\n\toverflow: hidden;\r\n}\r\n\r\n.signin-header {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tpadding: 40px 100px;\r\n\tz-index: 1;\r\n\t@media only screen and (max-width: 991px) {\r\n\t\tpadding: 40px 30px;\t\r\n\t}\r\n\t@media #{$large-mobile} {\r\n\t\tpadding: 30px;\r\n\t\ttext-align: center;\r\n\t}\r\n\t.site-logo {\r\n\t\tdisplay: inline-block;\r\n\t\t@media #{$large-mobile} {\r\n\t\t\tmargin-bottom: 40px;\r\n\t\t}\r\n\t}\r\n\t.singin-header-btn {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: flex-end;\r\n\t\t@media #{$large-mobile} {\r\n\t\t\tjustify-content: center;\r\n\t\t\tflex-direction: column;\r\n\t\t}\r\n\t\tp {\r\n\t\t\tmargin-bottom: 0;\r\n\t\t\tcolor: #292930;\r\n\t\t\tfont-size: var(--font-size-b2);\r\n\t\t\tfont-weight: var(--p-medium);\r\n\t\t}\r\n\t\t.sign-up-btn {\r\n\t\t\tmargin-left: 40px;\r\n\t\t    @media #{$md-layout} {\r\n\t\t    \tmargin-left: 20px;\r\n\t\t    }\r\n\t\t    @media #{$large-mobile} {\r\n\t\t    \tmargin-left: 0;\r\n\t\t    \tmargin-top: 10px;\r\n\t\t    }\r\n\t\t}\r\n\t}\r\n\t.back-btn {\r\n\t\twidth: 40px;\r\n\t\theight: 40px;\r\n\t\tline-height: 40px;\r\n\t\tborder: 1px solid #CBD3D9;\r\n\t\tborder-radius: 4px;\r\n\t\tfont-size: 16px;\r\n\t\tdisplay: block;\r\n\t\ttext-align: center;\r\n\t\ttransition: var(--transition);\r\n\r\n\t\t&:hover {\r\n\t\t\tbackground-color: var(--color-primary);\r\n\t\t\tborder-color: var(--color-primary);\r\n\t\t\tcolor: var(--color-white);\r\n\t\t\ti {\r\n\t\t\t\tanimation: prevNavSlide 400ms;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.axil-signin-banner {\r\n\tmin-height: 900px;\r\n\theight: 100vh;\r\n\tpadding: 200px 50px 0 100px;\r\n\t@media #{$lg-layout} {\r\n\t\tmargin-right: 50px;\r\n\t}\r\n\t@media only screen and (max-width: 991px) {\r\n\t\tdisplay: none;\t\r\n\t}\r\n}\r\n\r\n.axil-signin-form-wrap {\r\n\twidth: 100%;\r\n\theight: calc(100vh - 180px);\r\n\toverflow-y: auto;\r\n\tdisplay: flex;\r\n\tmargin: 180px -30px -30px;\r\n\t@media only screen and (max-width: 991px) {\r\n\t\tjustify-content: center;\r\n\t\ttext-align: center;\r\n\t\tmargin: 150px 0 0;\r\n\t}\r\n\t@media #{$large-mobile} {\r\n\t\tmargin: 200px 0 0;\r\n\t\theight: calc(100vh - 200px);\r\n\t}\r\n}\r\n\r\n.axil-signin-form {\r\n\tmax-width: 450px;\r\n\twidth: 100%;\r\n\tpadding: 30px;\r\n\t.singin-form {\r\n\t\tpadding-bottom: 30px;\r\n\t}\r\n\t.title {\r\n\t\tmargin-bottom: 16px;\r\n\t}\r\n\tp {\r\n\t\tcolor: var(--color-gray);\r\n\t}\r\n\t.form-group {\r\n\t\tmargin-bottom: 35px;\r\n\t\t&:last-child {\r\n\t\t\tmargin-bottom: 0;\r\n\t\t}\r\n\t}\r\n\t.form-control {\r\n\t\theight: 60px;\r\n\t\tborder-color: var(--color-light);\r\n\t\tpadding: 0 30px;\r\n\t\tcolor: var(--color-body);\r\n\t}\r\n\t.submit-btn {\r\n\t\twidth: auto;\r\n\t}\r\n\t.forgot-btn {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: var(--color-primary);\r\n\t\ttransition: var(--transition);\r\n\t\t&:hover {\r\n\t\t\tcolor: var(--color-body);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.axil-dashboard-warp {\r\n\t.axil-dashboard-author {\r\n\t\tmargin-bottom: 50px;\r\n\t\t.media {\r\n\t\t\talign-items: center;\r\n\t\t}\r\n\t\t.thumbnail {\r\n\t\t\tmargin-bottom: 10px;\r\n\t\t\timg {\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.joining-date {\r\n\t\t\tfont-size: 14px;\r\n\t\t\tcolor: var(--color-body);\r\n\t\t\tfont-weight: var(--s-medium); \r\n\r\n\t\t}\r\n\t}\r\n\t.tab-content {\r\n\t\tpadding-left: 45px;\r\n\t\t@media only screen and (max-width: 1199px) {\r\n\t\t\tpadding-left: 0;\t\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.axil-dashboard-aside {\r\n\tborder: 1px solid var(--color-light);\r\n\tpadding: 40px 35px 30px;\r\n\tborder-radius: 6px;\r\n\t@media #{$md-layout} {\r\n\t\tpadding: 30px 15px 20px;\r\n\t}\r\n\t@media #{$sm-layout} {\r\n\t\tmargin-bottom: 40px;\r\n\t}\r\n\t.nav-tabs {\r\n\t\tborder-bottom: none;\r\n\t}\r\n\t.nav-link {\r\n\t\tfont-weight: 500;\r\n\t\tcolor: var(--color-body);\r\n\t\tfont-size: var(--font-size-b2);\r\n\t\tposition: relative;\r\n\t\tborder-radius: 6px;\r\n\t\tpadding: 9px 10px 9px 55px;\r\n\t\tmargin-bottom: 8px;\r\n\t\ttransition: var(--transition);\r\n\t\tborder: none;\r\n\t\twidth: 100%;\r\n\t\t@media #{$md-layout} {\r\n\t\t\tpadding: 9px 10px 9px 40px;\r\n\t\t}\r\n\t\ti {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 12px;\r\n\t\t\tleft: 24px;\r\n\t\t\tfont-size: 18px;\r\n\t\t\t@media #{$md-layout} {\r\n\t\t\t\tleft: 10px;\r\n\t\t\t}\r\n\t\t}\r\n\t\t&.active,\r\n\t\t&:hover {\r\n\t\t\tbackground-color: var( --color-lighter);\r\n\t\t\tcolor: var(--color-primary);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.axil-dashboard-overview {\r\n\t.welcome-text {\r\n\t\tcolor: var(--color-black);\r\n\t\tfont-size: 18px;\r\n\t\tmargin-bottom: 25px;\r\n\t\tspan {\r\n\t\t\tfont-weight: var(--p-bold);\r\n\t\t}\r\n\t\ta {\r\n\t\t\tcolor: var(--color-chart03);\r\n\t\t\ttransition: var(--transition);\r\n\t\t\t&:hover {\r\n\t\t\t\tcolor: var(--color-primary);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\tp {\r\n\t\tfont-size: var(--font-size-b1);\r\n\t}\r\n}\r\n\r\n.axil-dashboard-order {\r\n\t.table {\r\n\t\tfont-family: var(--font-secondary);\r\n\t\tthead {\r\n\t\t\tbackground-color: var(--color-lighter);\r\n\t\t\tth {\r\n\t\t\t\tborder: none;\r\n\t\t\t\tpadding: 18px 20px;\r\n\t\t\t\tvertical-align: middle;\r\n\t\t\t\tfont-size: 20px;\r\n\t\t\t\tfont-weight: var(--p-medium);\r\n\t\t\t\ttext-transform: capitalize;\r\n\t\t\t\t&:first-child {\r\n\t\t\t\t\tborder-radius: 6px 0 0 6px;\r\n\t\t\t\t}\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\tborder-radius: 0 6px 6px 0;\r\n\t\t\t\t\tpadding-right: 30px;\r\n\t\t\t\t\ttext-align: right;\r\n\t\t\t\t}\r\n\t\t\t\t@media #{$smlg-device} {\r\n\t\t\t\t\tfont-size: 18px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\ttbody {\r\n\t\t\tborder-top: none;\r\n\t\t\ttr {\r\n\t\t\t\ttd, th {\r\n\t\t\t\t\tpadding: 20px 20px;\r\n\t\t\t\t\tvertical-align: middle;\r\n\t\t\t\t\tfont-weight: var(--p-medium);\r\n\t\t\t\t\tfont-size: var(--font-size-b2);\r\n\t\t\t\t\tcolor: var(--color-heading);\r\n\t\t\t\t\tborder-top: none;\r\n\t\t\t\t\tborder-bottom: 2px solid var(--color-lighter);\r\n\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\ttext-align: right;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tth {\r\n\t\t\t\t\tcolor: var(--color-chart03);\r\n\t\t\t\t}\r\n\t\t\t\ttd {\r\n\t\t\t\t\tmin-width: 150px;\r\n\t\t\t\t}\r\n\t\t\t\t&:first-child {\r\n\t\t\t\t\ttd, th {\r\n\t\t\t\t\t\tborder-top: none;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.view-btn {\r\n\t\t\t\tpadding: 9px 20px;\r\n\t\t\t\tborder: 1px solid var(--color-body);\r\n\t\t\t\tbackground-color: transparent;\r\n\t\t\t\tcolor: var(--color-dark);\r\n\t\t\t\t&:before {\r\n\t\t\t\t\tdisplay: none;\r\n\t\t\t\t}\r\n\t\t\t\t&:after {\r\n\t\t\t\t\tdisplay: none;\r\n\t\t\t\t}\r\n\t\t\t\t&:hover {\r\n\t\t\t\t\tbackground-color: var(--color-primary);\r\n\t\t\t\t\tborder-color: var(--color-primary);\r\n\t\t\t\t\tcolor: var(--color-white);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.axil-dashboard-address {\r\n\t.notice-text {\r\n\t\tfont-size: var(--font-size-b2);\r\n\t\tcolor: #292930;\r\n\t}\r\n\t.addrss-header {\r\n\t\tborder-bottom: 1px solid var(--color-light);\r\n\t\tpadding-bottom: 20px;\r\n\t\tmargin-bottom: 20px;\r\n\t}\r\n\t.title {\r\n\t\tfont-weight: var(--p-medium);\r\n\t\tcolor: #292930;\r\n\t\t@media #{$lg-layout} {\r\n\t\t\tfont-size: 26px;\r\n\t\t}\r\n\t}\r\n\t.address-edit {\r\n\t\tfont-size: var(--font-size-b2);\r\n\t\tcolor: #292930;\r\n\t\ttransition: var(--transition);\r\n\t\t&:hover {\r\n\t\t\tcolor: var(--color-primary);\r\n\t\t}\r\n\t}\r\n\tul {\r\n\t\t@extend %liststyle;\r\n\t\tli {\r\n\t\t\tfont-size: var(--font-size-b2); \r\n\t\t\tline-height: var(--line-height-b2);\r\n\t\t\tcolor: #292930;\r\n\t\t\tfont-family: var(--font-primary);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.axil-dashboard-account {\r\n\t.form-group {\r\n\t\t.form-control {\r\n\t\t\theight: 60px;\r\n\t\t\tpadding: 10px 30px;\r\n\t\t\tborder-color: var(--color-light);\r\n\t\t\tcolor: var(--color-body);\r\n\t\t}\r\n\t\tselect {\r\n\t\t\toption:hover {\r\n\t\t\t\tbackground: red !important;\r\n\t\t\t\tcolor: red;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}", "/*----------------------------\r\nBlog List  \r\n----------------------------*/\r\n.axil-post-wrapper {\r\n    .content-blog {\r\n        border-top: 1px solid #f3f3f3;\r\n        padding-top: 60px;\r\n        &:first-child {\r\n            margin-top: 0 !important;\r\n            border-top: none;\r\n            padding-top: 0;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n/* Main Blog  */\r\n\r\n.content-blog {\r\n    .thumbnail {\r\n        margin-bottom: 30px;\r\n        a {\r\n            display: block;\r\n            border-radius: var(--radius);\r\n            img {\r\n                width: 100%;\r\n                border-radius: var(--radius);\r\n            }\r\n        }\r\n    }\r\n    .content {\r\n        .title {\r\n            font-weight: 500;\r\n            line-height: 1.3;\r\n            a {\r\n                @extend %transition;\r\n            }\r\n        }\r\n        p {\r\n            margin-bottom: 30px;\r\n        }\r\n        .read-more-btn {\r\n            .axil-btn {\r\n                display: inline-flex;\r\n            }\r\n        }\r\n    }\r\n    &.sticky {\r\n        .inner {\r\n            background: #f9f3f0;\r\n            border-left: 6px solid var(--color-tertiary);\r\n            border-radius: var(--radius);\r\n            padding: 50px;\r\n            @media #{$small-mobile} {\r\n                padding: 30px 15px;\r\n            }\r\n        }\r\n    }\r\n    &.format-quote {\r\n        .inner {\r\n            background: #f9f3f0;\r\n            border-left: 6px solid var(--color-tertiary);\r\n            border-radius: var(--radius);\r\n            padding: 50px 40px 30px;\r\n            @media #{$large-mobile} {\r\n                padding: 30px 20px 10px;\r\n            }\r\n            .content {\r\n                blockquote {\r\n                    .title {\r\n                        font-weight: 700;\r\n                        line-height: 1.31;\r\n                        font-size: 35px;\r\n                        font-style: italic;\r\n                        @media #{$sm-layout} {\r\n                            font-size: 28px;\r\n                        }\r\n                        @media #{$large-mobile} {\r\n                            font-size: 24px;\r\n                        }\r\n                        a {\r\n                            @extend %transition;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &.format-video {\r\n        .thumbnail {\r\n            position: relative;\r\n            text-align: center;\r\n            &:after {\r\n                content: \"\";\r\n                height: 100%;\r\n                width: 100%;\r\n                background-color: var(--color-black);\r\n                opacity: .2;\r\n                border-radius: 4px;\r\n                position: absolute;\r\n                top: 0;\r\n                left: 0;\r\n                right: 0;\r\n                bottom: 0;\r\n                z-index: 1;\r\n            }\r\n            .popup-video {\r\n                position: absolute;\r\n                top: 50%;\r\n                left: 0;\r\n                right: 0;\r\n                transform: translateY(-50%);\r\n                z-index: 2;\r\n                .play-btn {\r\n                    height: 150px;\r\n                    width: 150px;\r\n                    display: flex;\r\n                    align-items: center;\r\n                    justify-content: center;\r\n                    background-color: rgba(0,0,0,.8);\r\n                    border-radius: 50%;\r\n                    margin: 0 auto;\r\n                    font-size: 32px;\r\n                    color: var(--color-white);\r\n                    transition: var(--transition);\r\n                    &:hover {\r\n                        background-color: rgba(0,0,0,1);;\r\n                    }\r\n                    @media only screen and (max-width: 767px) {\r\n                        height: 80px;\r\n                        width: 80px;\r\n                        font-size: 24px;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    // Post Sidebar \r\n    &.post-list-view {\r\n        display: flex;\r\n        align-items: center;\r\n        border-bottom: 1px solid #f3f3f3;\r\n        padding-bottom: 20px;\r\n        @media #{$small-mobile} {\r\n            align-items: flex-start;\r\n        }\r\n        &:last-child {\r\n            margin-bottom: 0;\r\n            border-bottom: none;\r\n            padding-bottom: 0;\r\n        }\r\n        .thumbnail {\r\n            width: 120px;\r\n            margin-right: 20px;\r\n            min-width: 120px;\r\n            overflow: hidden;\r\n            margin-bottom: 0;\r\n            @media #{$lg-layout} {\r\n                width: 70px;\r\n                margin-right: 10px;\r\n                min-width: 70px;\r\n            }\r\n            @media #{$small-mobile} {\r\n                width: 80px;\r\n                min-width: 80px;\r\n            }\r\n            a {\r\n                border-radius: 6px;\r\n                overflow: hidden;\r\n                img {\r\n                    width: 100%;\r\n                    border-radius: 6px;\r\n                    transition: 0.5s;\r\n                    object-fit: contain;\r\n                }\r\n            }\r\n        }\r\n        .content {\r\n            flex: 1;\r\n            .title {\r\n                font-size: 17px;\r\n                margin-bottom: 10px;\r\n                @media #{$sm-layout} {\r\n                    font-size: 16px;\r\n                }\r\n                a {\r\n                    display: -webkit-box;\r\n                    -webkit-line-clamp: 2;\r\n                    -webkit-box-orient: vertical;\r\n                    overflow: hidden;\r\n                    text-overflow: ellipsis;\r\n                }\r\n            }\r\n            .axil-post-meta {\r\n                margin-bottom: 0;\r\n            }\r\n        }\r\n        &:hover {\r\n            .thumbnail {\r\n                img {\r\n                    transform: scale(1.1);\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n/*---------------------\r\n    Blog Meta \r\n-----------------------*/\r\n\r\n.axil-post-meta {\r\n    display: flex;\r\n    margin-bottom: 20px;\r\n    .post-author-avatar {\r\n        min-width: 50px;\r\n        max-height: 50px;\r\n        margin-right: 20px;\r\n        width: 50px;\r\n        img {\r\n            border-radius: 100%;\r\n            width: 100%;\r\n            height: 100%;\r\n            object-fit: contain;\r\n        }\r\n    }\r\n    .post-meta-content {\r\n        .author-title {\r\n            margin-bottom: 5px;\r\n            font-size: 16px;\r\n            a {\r\n                @extend %transition;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.post-meta-list {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    padding: 0;\r\n    margin: 0;\r\n    list-style: none;\r\n    margin: 0 -15px;\r\n    li {\r\n        color: var(--color-body);\r\n        font-size: 14px;\r\n        padding: 0 15px;\r\n        position: relative;\r\n        margin-top: 0;\r\n        margin-bottom: 0;\r\n        &::after {\r\n            position: absolute;\r\n            content: \"\";\r\n            background: #CBD3D9;\r\n            width: 1px;\r\n            height: 14px;\r\n            right: 0;\r\n            top: 50%;\r\n            transform: translateY(-50%);\r\n        }\r\n        &:last-child {\r\n            &::after {\r\n                display: none;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n/*---------------------\r\n    Blog Grid \r\n-----------------------*/\r\n.blog-grid {\r\n    border: 1px solid #f1f1f1;\r\n    border-radius: 6px;\r\n    padding: 20px;\r\n    .thumbnail {\r\n        margin-bottom: 25px;\r\n        overflow: hidden;\r\n        border-radius: 6px;\r\n        position: relative;\r\n        img {\r\n            transition: .5s;\r\n        }\r\n        .blog-category {\r\n            position: absolute;\r\n            bottom: 20px;\r\n            right: 20px;\r\n            a {\r\n                background-color: rgba(255, 255, 255, 0.5);\r\n                border: 1px solid rgba(255, 255, 255, 0.5);\r\n                backdrop-filter: blur(25px);\r\n                box-shadow: 0 4px 30px rgba(0, 0, 0,.1);\r\n                padding: 2px 10px;\r\n                border-radius: 4px;\r\n                color: var(--color-white);\r\n                font-size: 14px;\r\n            }\r\n        }\r\n    }\r\n    .content {\r\n        .title {\r\n            margin-bottom: 20px;\r\n        }\r\n        .axil-btn {\r\n            padding: 0;\r\n            align-items: center;\r\n            color: var(--color-heading);\r\n            i {\r\n                padding-left: 6px;\r\n                top: 1px;\r\n                color: var(--color-heading);\r\n                transition: var(--transition);\r\n            }\r\n            &:after {\r\n                content: \"\";\r\n                height: 1px;\r\n                width: 0;\r\n                background-color: var(--color-primary);\r\n                position: absolute;\r\n                bottom: 0;\r\n                right: 0;\r\n                transition: var(--transition);\r\n            }\r\n            &:hover {\r\n                color: var(--color-primary);\r\n                &:after {\r\n                    width: 100%;\r\n                    left: 0;\r\n                }\r\n                i {\r\n                    color: var(--color-primary);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &:hover {\r\n        .thumbnail {\r\n            img {\r\n                transform: scale(1.1);\r\n            }\r\n        }\r\n    }\r\n}", "/*----------------------------\r\nBlog Single  \r\n----------------------------*/\r\n.axil-single-post {\r\n    .post-content {\r\n        padding: 0 75px;\r\n        @media #{$smlg-device} {\r\n            padding: 0;\r\n        }\r\n    }\r\n    &.post-formate {\r\n        .content-block {\r\n            .post-thumbnail img {\r\n                border-radius: 6px;\r\n            }\r\n        }\r\n    }\r\n    &.post-video {\r\n        .format-video {\r\n            .thumbnail {\r\n                padding-top: 0;\r\n                margin-bottom: 0;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.post-single-wrapper {\r\n    padding-top: 80px;\r\n    @media only screen and (max-width: 991px) {\r\n        padding-top: 30px;\r\n    }\r\n    .content-blog {\r\n        border-top: none;\r\n        padding-top: 0;\r\n    }\r\n}\r\n\r\n.axil-post-wrapper {\r\n    .audio-player {\r\n        margin-bottom: 20px;\r\n        audio {\r\n            width: 100%;\r\n        }\r\n    }\r\n    .post-heading {\r\n        border-bottom: 1px solid var(--color-border-light);\r\n        margin-bottom: 45px;\r\n        padding-bottom: 30px;\r\n    }\r\n}\r\n\r\n\r\n.axil-single-widget:first-child,\r\n.widget-sidebar:first-child {\r\n    margin-top: 0 !important;\r\n}\r\n\r\n.newsletter-inner {\r\n    &::before {\r\n        content: \"\";\r\n        position: absolute;\r\n        left: 30px;\r\n        background: url(../images/send-mail.png) no-repeat;\r\n        z-index: 2;\r\n        top: 17px;\r\n        width: 25px;\r\n        height: 25px;\r\n    }\r\n}\r\n\r\n.wp-block-columns {\r\n    display: flex;\r\n    margin-bottom: 28px;\r\n    margin: 0 -15px;\r\n    @media #{$sm-layout} {\r\n        flex-wrap: wrap;\r\n    }\r\n}\r\n\r\n.wp-block-column {\r\n    flex-grow: 1;\r\n    min-width: 0;\r\n    word-break: break-word;\r\n    overflow-wrap: break-word;\r\n    padding-right: 15px;\r\n    padding-left: 15px;\r\n    .wp-block-image {\r\n        img {\r\n            border-radius: 6px;\r\n        }\r\n    }\r\n}\r\n\r\n.post-details figure,\r\n.entry-content figure {\r\n    margin-bottom: 40px;\r\n    @media #{$large-mobile} {\r\n        margin-bottom: 20px;\r\n    }\r\n}\r\n\r\n.post-details__social-share {\r\n    .share-on-text {\r\n        display: inline-block;\r\n        margin-bottom: 10px;\r\n        margin-right: -5px;\r\n        @media #{$smlg-device} {\r\n            margin-right: -18px;\r\n        }\r\n    }\r\n    .social-share {\r\n        flex-direction: column;\r\n        align-items: center;\r\n        @media only screen and (max-width: 992px) {\r\n            flex-direction: row;\r\n            margin-bottom: 15px;\r\n        }\r\n    }\r\n}\r\n\r\n.sticky-top {\r\n    z-index: 0 !important;\r\n    top: 100px;\r\n}\r\n\r\n\r\n", "/*---------------------\r\nAxil Comment  \r\n----------------------*/\r\n\r\n.axil-total-comment-post {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 30px 0;\r\n    @media #{$large-mobile} {\r\n        display: block;\r\n    }\r\n    .add-comment-button {\r\n        @media #{$large-mobile} {\r\n            margin-top: 20px;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n/*---------------------------\r\n  Comment Form Styles  \r\n----------------------------*/\r\n\r\n.comment-respond {\r\n    margin: 50px 0 0;\r\n    .title {\r\n        margin-bottom: 20px;\r\n    }\r\n    .comment-notes {\r\n        color: var(--color-gray);\r\n        margin-bottom: 40px;\r\n    }\r\n    .comment-form-cookies-consent {\r\n        margin-bottom: 20px;\r\n    }\r\n}\r\n\r\n\r\n/* --------------------------\r\n  Comments Styles  \r\n-----------------------------*/\r\n\r\n.comment-list {\r\n    @extend %liststyle;\r\n    ul {\r\n        &.children {\r\n            @extend %liststyle;\r\n            padding-left: 75px;\r\n            @media #{$sm-layout} {\r\n                padding-left: 30px;\r\n            }\r\n        }\r\n    }\r\n    .comment {\r\n        margin-top: 0;\r\n        margin-bottom: 0;\r\n        .single-comment {\r\n            padding: 15px 0;\r\n            display: flex;\r\n            .comment-img {\r\n                margin-bottom: 15px;\r\n                min-width: 60px;\r\n                margin-right: 20px;\r\n                img {\r\n                    border-radius: 100%;\r\n                    width: 100%;\r\n                }\r\n            }\r\n        }\r\n        .commenter {\r\n            line-height: 33px;\r\n            margin-bottom: 6px;\r\n            a {\r\n                .hover-flip-item {\r\n                    span {\r\n                        &::before {\r\n                            color: var(--color-heading);\r\n                        }\r\n                        &::after {\r\n                            color: var(--color-primary);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        .comment-meta {\r\n            display: flex;\r\n            align-items: center;\r\n            margin-bottom: 8px;\r\n            flex-wrap: wrap;\r\n        }\r\n        .time-spent {\r\n            color: var(--color-extra01);\r\n            font-size: 16px;\r\n            line-height: 24px;\r\n        }\r\n        .reply-edit {\r\n            a {\r\n                &.comment-reply-link {\r\n                    font-size: 16px;\r\n                    line-height: 24px;\r\n                    display: flex;\r\n                    color: var(--color-primary);\r\n                    margin-left: 8px;\r\n                    padding-left: 8px;\r\n                    position: relative;\r\n                    font-weight: 500;\r\n                    overflow: visible;\r\n                    @extend %transition;\r\n                    .hover-flip-item {\r\n                        span {\r\n                            &::before {\r\n                                color: var(--color-heading);\r\n                            }\r\n                            &::after {\r\n                                color: var(--color-primary);\r\n                            }\r\n                        }\r\n                    }\r\n                    &:hover {\r\n                        color: var(--color-primary);\r\n                    }\r\n                    &::before {\r\n                        position: absolute;\r\n                        content: \"\";\r\n                        top: 50%;\r\n                        transform: translateY(-50%);\r\n                        left: -2px;\r\n                        width: 4px;\r\n                        height: 4px;\r\n                        background: var(--color-extra01);\r\n                        border-radius: 100%;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.form-group label {\r\n    position: absolute;\r\n    top: -11px;\r\n    left: 20px;\r\n    pointer-events: none;\r\n    z-index: 4;\r\n    background: #fff;\r\n    padding: 0 10px;\r\n}", "/*-------------------------\r\n Blog Sidebar  \r\n---------------------------*/\r\n.axil-sidebar-area {\r\n    @media only screen and (max-width: 991px) {\r\n        margin-top: 60px;   \r\n    }\r\n    .axil-single-widget {\r\n        &:first-child {\r\n            margin-top: 0 !important;\r\n        }\r\n    }\r\n}\r\n\r\n.axil-single-widget {\r\n    border: 1px solid #f3f3f3;\r\n    border-radius: 6px;\r\n    padding: 30px;\r\n    .widget-title {\r\n        font-weight: 500;\r\n        margin-bottom: 30px;\r\n        color: var(--color-dark);\r\n    }\r\n    @media #{$small-mobile} {\r\n        padding: 20px;\r\n    }\r\n}\r\n\r\n\r\n/*---------------------\r\n    Tag Cloud \r\n-----------------------*/\r\n\r\n.tagcloud {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    margin: -5px;\r\n    a {\r\n        border: 2px solid var(--color-border-light);\r\n        font-size: var(--font-size-b2) !important;\r\n        color: var(--color-body);\r\n        height: 40px;\r\n        padding: 0 20px;\r\n        margin: 5px;\r\n        display: inline-block;\r\n        line-height: 35px;\r\n        border-radius: 500px;\r\n        @extend %transition;\r\n        font-family: var(--font-secondary);\r\n\r\n        &:hover {\r\n            background: var(--color-primary);\r\n            color: #ffffff;\r\n            border-color: var(--color-primary);\r\n        }\r\n    }\r\n}\r\n\r\n\r\n\r\n/*-----------------------\r\n    Blog Search  \r\n------------------------*/\r\n.blog-search,\r\n.wp-block-search {\r\n    position: relative;\r\n    input {\r\n        height: 50px;\r\n        border: 1px solid #F0F2F5;\r\n        background-color: #F0F2F5;\r\n        padding: 0 20px;\r\n        color: var(--color-heading);\r\n        padding-left: 50px;\r\n        font-size: 16px;\r\n        border-radius: var(--radius);\r\n        font-family: var(--font-secondary);\r\n    }\r\n    .search-button {\r\n        position: absolute;\r\n        left: 20px;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n        border: 0 none;\r\n        padding: 0;\r\n        background-color: transparent;\r\n        width: auto;\r\n        i {\r\n            color: var(--color-body);\r\n            font-weight: 400;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n\r\n/*---------------------\r\n    Widget Ress  \r\n-----------------------*/\r\n\r\n.widget_rss {\r\n    ul {\r\n        li {\r\n            a {\r\n                color: var(--color-heading);\r\n                text-decoration: none;\r\n                @extend %transition;\r\n                display: block;\r\n                &:hover {\r\n                    color: var(--color-primary);\r\n                }\r\n            }\r\n            span {\r\n                &.rss-date{\r\n                    font-size: 14px;\r\n                }\r\n            }\r\n            .rssSummary {\r\n                margin-top: 9px;\r\n            }\r\n            cite {\r\n                margin-top: 4px;\r\n                display: inline-block;\r\n                font-weight: 500;\r\n                font-size: 14px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n/*---------------------\r\n    Widget Archives  \r\n-----------------------*/\r\n.widget_archive {\r\n    ul {\r\n        margin-bottom: 0;\r\n        list-style: disc;\r\n        li {\r\n            &::marker {\r\n                font-size: 18px;\r\n                color: #CED0D4;\r\n                transition: var(--transition);\r\n            }\r\n            a {\r\n                color: #65676B;\r\n                transition: var(--transition);\r\n            }\r\n            &:hover {\r\n                &::marker {\r\n                    color: var(--color-primary);\r\n                }\r\n                a {\r\n                    color: var(--color-black);\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n/*---------------------\r\n    Widget Archives Dropdown \r\n-----------------------*/\r\n.widget_archive_dropdown {\r\n    select {\r\n        border-radius: 4px;\r\n        height: 50px;\r\n        padding: 0 20px;\r\n    }\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n", "/*------------------------\r\n    Footer Styles  \r\n-------------------------*/\r\n\r\n.footer-top {\r\n    padding: 80px 0 40px;\r\n    position: relative;\r\n    @media #{$sm-layout} {\r\n        padding: 60px 0 20px;\r\n    }\r\n    &.separator-top {\r\n        &::after {\r\n            position: absolute;\r\n            top: 0;\r\n            width: 1290px;\r\n            height: 2px;\r\n            background-color: #F6F7FB;\r\n            content: \"\";\r\n            left: 0;\r\n            right: 0;\r\n            margin: 0 auto;\r\n            border-radius: 100px;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n/*----------------------\r\n    Footer Widget  \r\n----------------------*/\r\n\r\n.axil-footer-widget {\r\n    margin-bottom: 40px;\r\n    .widget-title {\r\n        font-size: 18px;\r\n        font-weight: 500;\r\n        letter-spacing: -0.025em;\r\n        margin-bottom: 20px;\r\n    }\r\n    .inner {\r\n        ul {\r\n            padding-left: 0;\r\n            >li {\r\n                &:first-child {\r\n                    margin-top: 0;\r\n                }\r\n            }\r\n        }\r\n        ul {\r\n            list-style: none;\r\n            li {\r\n                margin-top: 12px;\r\n                margin-bottom: 12px;\r\n                a {\r\n                    color: var(--color-body);\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                    transition: 0.3s;\r\n                    position: relative;\r\n                    &:after {\r\n                        content: \"\";\r\n                        height: 2px;\r\n                        width: 0;\r\n                        background-color: var(--color-black);\r\n                        position: absolute;\r\n                        bottom: -2px;\r\n                        right: 0;\r\n                        opacity: 0;\r\n                        transition: 0.5s;\r\n                    }\r\n                    &:hover {\r\n                        color: var(--color-heading);\r\n                        &:after {\r\n                            width: 100%;\r\n                            opacity: 1;\r\n                            left: 0;\r\n\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .logo {\r\n        img {\r\n           height: 35px;\r\n           width: auto;\r\n        }\r\n    }\r\n    .support-list-item {\r\n        margin-bottom: 0;\r\n        li {\r\n            padding-left: 26px;\r\n            position: relative;\r\n            a {\r\n                position: initial!important;\r\n                font-weight: 400 !important;\r\n                &:after {\r\n                    display: none;\r\n                }\r\n            }\r\n            i {\r\n                padding-right: 5px;\r\n                position: absolute;\r\n                top: 5px;\r\n                left: 0;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n/*-----------------------\r\n    Copyright Area  \r\n------------------------*/\r\n\r\n.copyright-default {\r\n    padding: 15px 0;\r\n    .quick-link {\r\n        @extend %liststyle;\r\n        display: flex;\r\n        margin: -15px;\r\n        li {\r\n            padding: 15px;\r\n            position: relative;\r\n            color: var(--color-body);\r\n            font-weight: 500;\r\n            font-size: 14px;\r\n            &::after {\r\n                position: absolute;\r\n                content: \"\";\r\n                background: var(--color-lightest);\r\n                width: 5px;\r\n                height: 5px;\r\n                border-radius: 100%;\r\n                right: -3px;\r\n                top: 50%;\r\n                transform: translateY(-50%);\r\n                @media #{$large-mobile} {\r\n                    display: none;\r\n                }\r\n            }\r\n            a {\r\n                color: var(--color-body);\r\n                font-weight: 500;\r\n                font-size: 14px;\r\n                letter-spacing: -0.025em;\r\n                transition: 0.5s;\r\n                position: relative;\r\n                &:after {\r\n                    content: \"\";\r\n                    height: 2px;\r\n                    width: 0;\r\n                    background-color: var(--color-black);\r\n                    position: absolute;\r\n                    bottom: -2px;\r\n                    right: 0;\r\n                    opacity: 0;\r\n                    transition: 0.5s;\r\n                }\r\n                &:hover {\r\n                    color: var(--color-heading);\r\n                    &:after {\r\n                        width: 100%;\r\n                        opacity: 1;\r\n                        left: 0;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        &.payment-icons-bottom {\r\n            margin: -15px -10px;\r\n            li {\r\n                padding: 15px 10px;\r\n                &::after {\r\n                    display: none;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &.separator-top {\r\n        position: relative;\r\n        &::after {\r\n            position: absolute;\r\n            content: \"\";\r\n            background-color: #F6F7FB;\r\n            border-radius: 100px;\r\n            height: 2px;\r\n            width: 1290px;\r\n            left: 0;\r\n            right: 0;\r\n            margin: 0 auto;\r\n            top: 0;\r\n        }\r\n    }\r\n    .copyright-right {\r\n        span {\r\n            &.card-text {\r\n                color: var(--color-body);\r\n                font-size: 14px;\r\n                font-weight: 500;\r\n                display: inline-block;\r\n                margin: 10px 20px;\r\n                letter-spacing: -0.025em;\r\n                @media #{$large-mobile} {\r\n                    margin-left: 0;   \r\n                }\r\n            }\r\n        }\r\n    }\r\n    .copyright-left {\r\n       @media #{$smlg-device} {\r\n            text-align: center;  \r\n       }\r\n       @media #{$large-mobile} {\r\n            flex-direction: column;  \r\n            align-items: center;\r\n       }\r\n\r\n        ul+ul {\r\n            margin-left: 15px;\r\n            @media #{$large-mobile} {\r\n                margin-left: -15px;   \r\n            }\r\n            li {\r\n                &::after {\r\n                    display: none;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// Footer Dark Styles\r\n.footer-dark {\r\n    background-color: var(--color-heading);\r\n    .axil-footer-widget {\r\n        p {\r\n            color: #acacac;\r\n        }\r\n    }\r\n    .social-share {\r\n        a {\r\n            color: #acacac;\r\n            &:hover {\r\n                color: var(--color-white);\r\n            }\r\n        }\r\n    }\r\n    .axil-footer-widget {\r\n        .widget-title {\r\n            color: #c8c8c8;\r\n        }\r\n        .inner {\r\n            ul {\r\n                li {\r\n                    a {\r\n                        color: #acacac;\r\n                        &:after {\r\n                            background-color: #acacac;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .copyright-default {\r\n        &.separator-top {\r\n            &:after {\r\n                background-color: #454545;\r\n            }\r\n        }\r\n        .quick-link {\r\n            li {\r\n                color: #acacac;\r\n                &:after {\r\n                    background: #acacac;\r\n                }\r\n                a {\r\n                    color: #acacac;\r\n                    &:after {\r\n                        background-color: #acacac;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        .copyright-right {\r\n            span.card-text {\r\n                color: #acacac;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// Footer Style 2\r\n.footer-style-2 {\r\n    .footer-top {\r\n        padding: 50px 0 0;\r\n        &.separator-top {\r\n            &:after {\r\n                background-color: #F6F7FB;\r\n                height: 2px;\r\n            }\r\n        }\r\n    }\r\n    .axil-footer-widget {\r\n        .widget-title {\r\n            font-weight: 600;\r\n        }\r\n        .inner {\r\n            .download-btn-group {\r\n                display: flex;\r\n                align-items: center;\r\n                margin-top: 15px;\r\n                .qr-code {\r\n                    margin-right: 20px;\r\n                    img {\r\n                        @media #{$lg-layout} {\r\n                            height: 80px;\r\n                        }\r\n                    }\r\n                }\r\n                .app-link {\r\n                    flex: 1;\r\n                    a {\r\n                        margin-bottom: 15px;\r\n                        display: block;\r\n                        &:last-child {\r\n                            margin-bottom: 0;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .copyright-default {\r\n        .quick-link {\r\n            li {\r\n                &:last-child {\r\n                    &:after {\r\n                        display: none;\r\n                    }\r\n                }\r\n            }\r\n            &.payment-icons-bottom {\r\n                li {\r\n                    padding: 10px;\r\n                    img {\r\n                       height: 20px;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        .social-share {\r\n            @media #{$smlg-device} {\r\n                justify-content: center;\r\n                margin-top: 0;\r\n                margin-bottom: 0;\r\n            }\r\n            a {\r\n                font-size: 16px;\r\n                color: var(--color-body);\r\n                line-height: normal;\r\n                &:after {\r\n                    height: 35px;\r\n                    width: 35px;\r\n                }\r\n                &:hover {\r\n                    color: var(--color-white);\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// Footer Style 3\r\n.footer-style-3 {\r\n    .footer-top {\r\n        padding: 35px 0 0 0;\r\n    }\r\n    .footer-widget-warp {\r\n        border-bottom: 1px solid rgba(119, 119, 119, .4);\r\n        padding-top: 30px;\r\n        &:last-child {\r\n            padding-bottom: 30px;\r\n        }\r\n    }\r\n    .footer-middle {\r\n        padding: 28px 0;\r\n    }\r\n    .payment-method {\r\n        display: flex;\r\n        align-items: center;\r\n        .title {\r\n            color: var(--color-white);\r\n            margin-bottom: 0;\r\n            padding-right: 24px;\r\n            text-align: right;\r\n            font-size: 14px;\r\n            line-height: 1.5;\r\n            min-width: 90px;\r\n        }\r\n        ul {\r\n            border-left: 1px solid rgba(119, 119, 119, .4);\r\n            margin-bottom: 0;\r\n            padding-left: 18px;\r\n            list-style: none;\r\n            margin: -6px;\r\n            li {\r\n                text-align: center;\r\n                display: inline-block;\r\n                height: 40px;\r\n                width: 40px;\r\n                line-height: 40px;\r\n                border-radius: 50%;\r\n                background-color: var(--color-white);\r\n                margin: 6px;\r\n                img {\r\n                    max-width: 28px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .footer-social-link {\r\n        @media (max-width: 991px) {\r\n            margin-top: 20px;\r\n        }\r\n        ul {\r\n            list-style: none;\r\n            border-left-color: rgba(51, 120, 240, .4);\r\n            li {\r\n                background-color: transparent;\r\n                a {\r\n                    height: 40px;\r\n                    width: 40px;\r\n                    line-height: 40px;\r\n                    background-color: var(--color-primary);\r\n                    border-radius: 50%;\r\n                    display: block;\r\n                    text-align: center;\r\n                    font-size: 18px;\r\n                    color: var(--color-white);\r\n                    &:hover {\r\n                        background-color: var(--color-secondary);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .copyright-area {\r\n        border-top: 1px solid rgba(119, 119, 119, .1);\r\n        .quick-link {\r\n            li {\r\n                a {\r\n                    font-weight: 400;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.axil-footer-widget {\r\n    &.widget-flex {\r\n        display: flex;\r\n        margin-bottom: 30px;\r\n        .widget-title {\r\n            border-right: 1px solid rgba(119, 119, 119, .4);\r\n            padding-right: 22px;\r\n            margin-right: 22px;\r\n            font-size: 14px;\r\n            margin-bottom: 0;\r\n            min-width: 90px;\r\n            font-weight: 700;\r\n            text-align: right;\r\n        }\r\n        .inner {\r\n            flex: 1;\r\n            ul {\r\n                margin-bottom: 0;\r\n                li {\r\n                    margin: 10px 0;\r\n                    &:first-child {\r\n                        margin-top: 0;\r\n                    }\r\n                    &:last-child {\r\n                        margin-bottom: 0;\r\n                    }\r\n                    a {\r\n                        font-size: 14px;\r\n                        color: #D6D6D6;\r\n                        font-family: var(--font-secondary);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &.footer-widget-newsletter {\r\n        padding-right: 50px;\r\n        .input-group {\r\n            input {\r\n                background-color: #49495F;\r\n                height: 46px;\r\n                border-radius: 8px 0 0 8px;\r\n                padding: 0 20px;\r\n                border: none;\r\n                color: #D6D6D6;\r\n                &:focus {\r\n                    background-color: #49495F;\r\n                    box-shadow: none;\r\n                    color: #D6D6D6;\r\n                }\r\n                &::placeholder {\r\n                    color: #D6D6D6;\r\n                    /* Firefox */\r\n                    opacity: 1;\r\n                }\r\n                &:-ms-input-placeholder {\r\n                    /* Internet Explorer 10-11 */\r\n                    color: #D6D6D6;\r\n                }\r\n                &::-ms-input-placeholder {\r\n                    /* Microsoft Edge */\r\n                    color: #D6D6D6;\r\n                }\r\n            }\r\n            button {\r\n                width: auto;\r\n                background-color: var(--color-primary);\r\n                font-size: 14px;\r\n                font-weight: 700;\r\n                border-radius: 0 8px 8px 0;\r\n                color: var(--color-white);\r\n                padding: 0 24px;\r\n                &:hover {\r\n                    background-color: var(--color-secondary);\r\n                }\r\n            }\r\n        }\r\n        .widget-title {\r\n            color: var(--color-white);\r\n            font-size: 24px;\r\n            margin-bottom: 8px;\r\n        }\r\n        p {\r\n            color: #D6D6D6;\r\n            font-size: 14px;\r\n            margin-bottom: 16px;\r\n            span {\r\n                color: #FE497C;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n/*-----------------------\r\n   Offer Popup Modal Area  \r\n------------------------*/\r\n.offer-popup-modal {\r\n    position: fixed;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translateY(-50%) translateX(-50%) scale(.8);\r\n    z-index: 101;\r\n    visibility: hidden;\r\n    opacity: 0;\r\n    transition: 0.3s;\r\n    .offer-popup-wrap {\r\n        background-color: var(--color-white);\r\n        border-radius: 6px;\r\n        padding: 50px;\r\n        width: 730px;\r\n        height: 450px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        overflow: auto;\r\n        background-image: url('../../assets/images/others/popup-bg.png');\r\n        background-repeat: no-repeat;\r\n        background-position: center right;\r\n        @media only screen and (max-width: 767px) {\r\n            width: 100%;   \r\n            padding: 40px 30px;\r\n            max-height: 400px;\r\n            background-image: none;\r\n        }\r\n        @media #{$small-mobile} {\r\n            padding: 30px 20px;\r\n            max-height: 370px;\r\n        }\r\n        .popup-close {\r\n            height: 40px;\r\n            width: 40px;\r\n            font-size: 18px;\r\n            color: var(--color-white);\r\n            background-color: var(--color-primary);\r\n            border-radius: 50%;\r\n            position: absolute;\r\n            top: -30px;\r\n            right: -30px;\r\n            &:hover {\r\n                background-color: var(--color-secondary);\r\n            }\r\n            @media only screen and (max-width: 767px){\r\n                height: 30px;\r\n                width: 30px;\r\n                font-size: 12px;     \r\n                top: -30px;\r\n                right: -15px;\r\n            }\r\n            @media #{$small-mobile} {\r\n                top: -20px;\r\n                right: -10px;\r\n            }\r\n        }\r\n        .card-body {\r\n            position: relative;\r\n            padding: 0;\r\n            display: flex;\r\n            align-items: center;\r\n            .section-title-wrapper {\r\n                margin-bottom: 0;\r\n                @media only screen and (max-width: 767px) {\r\n                    padding-right: 0;\r\n                }\r\n                .title {\r\n                    font-size: 40px;\r\n                    line-height: 1.2;\r\n                    @media only screen and (max-width: 767px) {\r\n                        font-size: 28px;\r\n                    }\r\n                }\r\n            }\r\n            .countdown {\r\n                margin-bottom: 38px;\r\n                .countdown-section {\r\n                    background-color: var(--color-lighter);\r\n                }\r\n                \r\n            }\r\n            .axil-btn {\r\n                i {\r\n                    margin-right: 0;\r\n                    margin-left: 8px;\r\n                    position: relative;\r\n                    top: 1px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n.offer-popup-modal.open {\r\n    visibility: visible;\r\n    opacity: 1;\r\n    transform: translate(-50%, -50%) scale(1);\r\n    transition: all .3s cubic-bezier(0.29, 1.39, 0.86, 1.15);\r\n}", "/*----------------------\r\nSpacing\r\n-----------------------*/\r\n\r\n.slick-dotted.slick-slider {\r\n    margin-bottom: 0;\r\n}\r\n\r\n.axil-section-gap {\r\n    padding: 80px 0;\r\n    @media #{$sm-layout} {\r\n        padding: 60px 0;\r\n    }\r\n}\r\n\r\n.axil-section-gapcommon {\r\n    padding: 80px 0 50px;\r\n    @media #{$sm-layout} {\r\n        padding: 60px 0 30px;\r\n    }\r\n}\r\n\r\n.section-gap-80-35 {\r\n    padding: 80px 0 35px;\r\n    @media #{$sm-layout} {\r\n        padding: 60px 0 15px;\r\n    }\r\n}\r\n\r\n.axil-section-gapBottom {\r\n    padding-bottom: 80px;\r\n    @media #{$md-layout} {\r\n        padding-bottom: 80px; \r\n    }\r\n    @media #{$sm-layout} {\r\n        padding-bottom: 60px ;\r\n    }\r\n}\r\n\r\n.pb--165,\r\n.pb--85 {\r\n    @media #{$md-layout} {\r\n        padding-bottom: 80px; \r\n    }\r\n    @media #{$sm-layout} {\r\n        padding-bottom: 60px ;\r\n    }\r\n}\r\n.axil-section-gapTop {\r\n    padding-top: 80px;\r\n    @media #{$md-layout} {\r\n        padding-top: 80px; \r\n    }\r\n    @media #{$sm-layout} {\r\n        padding-top: 60px ;\r\n    }\r\n}\r\n\r\n.axilil-service-area {\r\n    &.axil-section-gap {\r\n        &.layout-2  {\r\n            padding-bottom: 160px;\r\n            padding-top: 120px;\r\n        \r\n            @media #{$md-layout} {\r\n                padding-top: 80px; \r\n                padding-bottom: 80px;\r\n            }\r\n            @media #{$sm-layout} {\r\n                padding-top: 60px; \r\n                padding-bottom: 60px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.pl--0 {\r\n    padding-left: 0 !important;\r\n}\r\n.pr--0 {\r\n    padding-right: 0 !important;\r\n}\r\n.pt--0 {\r\n    padding-top: 0 !important;\r\n}\r\n.pb--0 {\r\n    padding-bottom: 0 !important;\r\n}\r\n.mr--0 {\r\n    margin-right: 0 !important;\r\n}\r\n.ml--0 {\r\n    margin-left: 0 !important;\r\n}\r\n.mt--0 {\r\n    margin-top: 0 !important;\r\n}\r\n.mb--0 {\r\n    margin-bottom: 0 !important;\r\n}\r\n\r\n.ptb---100{\r\n    padding: 100px 0;\r\n    @media #{$md-layout} {\r\n        padding: 80px 0;\r\n    }\r\n    @media #{$sm-layout} {\r\n        padding: 60px 0;\r\n    }\r\n}\r\n\r\n@for $i from 1 through 40 {\r\n    .ptb--#{5 * $i} { padding: 5px *$i 0; }\r\n    .plr--#{5 * $i} { padding: 0 5px *$i; }\r\n    .pt--#{5 * $i} { padding-top: 5px *$i; }\r\n    .pb--#{5 * $i} { padding-bottom: 5px *$i; }\r\n    .pl--#{5 * $i} { padding-left: 5px *$i;}\r\n    .pr--#{5 * $i} { padding-right: 5px *$i;}\r\n    .mt--#{5 * $i} {margin-top: 5px *$i;}\r\n    .mb--#{5 * $i} {margin-bottom: 5px *$i;}\r\n    .mr--#{5 * $i} {margin-right: 5px *$i;}\r\n    .ml--#{5 * $i} {margin-left: 5px *$i;}\r\n}\r\n\r\n@media only screen and (min-width: 1350px) {\r\n    .ml--xxl-0 {\r\n        margin-left: 0;\r\n    }   \r\n}\r\n\r\n@media #{$laptop-device} {\r\n    @for $i from 1 through 20 {\r\n        .ptb_lp--#{5 * $i} {\r\n            padding: 5px *$i 0;\r\n        }\r\n\r\n        .plr_lp--#{5 * $i} {\r\n            padding: 0 5px *$i;\r\n        }\r\n\r\n        .pt_lp--#{5 * $i} {\r\n            padding-top: 5px *$i;\r\n        }\r\n\r\n        .pb_lp--#{5 * $i} {\r\n            padding-bottom: 5px *$i;\r\n        }\r\n\r\n        .pl_lp--#{5 * $i} {\r\n            padding-left: 5px *$i;\r\n        }\r\n\r\n        .pr_lp--#{5 * $i} {\r\n            padding-right: 5px *$i;\r\n        }\r\n\r\n        .mt_lp--#{5 * $i} {\r\n            margin-top: 5px *$i;\r\n        }\r\n\r\n        .mb_lp--#{5 * $i} {\r\n            margin-bottom: 5px *$i;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n@media #{$lg-layout} {\r\n    @for $i from 1 through 20 {\r\n        .ptb_lg--#{5 * $i} {\r\n            padding: 5px *$i 0;\r\n        }\r\n        .plr_lg--#{5 * $i} {\r\n            padding: 0 5px *$i;\r\n        }\r\n        .pt_lg--#{5 * $i} {\r\n            padding-top: 5px *$i;\r\n        }\r\n        .pb_lg--#{5 * $i} {\r\n            padding-bottom: 5px *$i;\r\n        }\r\n\r\n        .pl_lg--#{5 * $i} {\r\n            padding-left: 5px *$i;\r\n        }\r\n\r\n        .pr_lg--#{5 * $i} {\r\n            padding-right: 5px *$i;\r\n        }\r\n\r\n        .mt_lg--#{5 * $i} {\r\n            margin-top: 5px *$i;\r\n        }\r\n\r\n        .mb_lg--#{5 * $i} {\r\n            margin-bottom: 5px *$i;\r\n        }\r\n        .ml_lg--#{5 * $i} {\r\n            margin-left: 5px *$i;\r\n        }\r\n\r\n    }\r\n}\r\n\r\n@media #{$md-layout} {\r\n\r\n    .ptb_md--0{\r\n        padding: 0 !important;\r\n    }\r\n    .pl_md--0 {\r\n        padding-left: 0 !important;\r\n    }\r\n    .pr_md--0 {\r\n        padding-right: 0 !important;\r\n    }\r\n    .pt_md--0 {\r\n        padding-top: 0 !important;\r\n    }\r\n    .pb_md--0 {\r\n        padding-bottom: 0 !important;\r\n    }\r\n    .mr_md--0 {\r\n        margin-right: 0 !important;\r\n    }\r\n    .ml_md--0 {\r\n        margin-left: 0 !important;\r\n    }\r\n    .mt_md--0 {\r\n        margin-top: 0 !important;\r\n    }\r\n    .mb_md--0 {\r\n        margin-bottom: 0 !important;\r\n    }\r\n    .ptb_md--250{\r\n        padding: 250px 0 !important;\r\n    }\r\n    \r\n    @for $i from 1 through 20 {\r\n        .ptb_md--#{5 * $i} {\r\n            padding: 5px *$i 0;\r\n        }\r\n\r\n        .plr_md--#{5 * $i} {\r\n            padding: 0 5px *$i;\r\n        }\r\n\r\n        .pt_md--#{5 * $i} {\r\n            padding-top: 5px *$i;\r\n        }\r\n\r\n        .pb_md--#{5 * $i} {\r\n            padding-bottom: 5px *$i;\r\n        }\r\n\r\n        .pl_md--#{5 * $i} {\r\n            padding-left: 5px *$i;\r\n        }\r\n\r\n        .pr_md--#{5 * $i} {\r\n            padding-right: 5px *$i;\r\n        }\r\n\r\n        .mt_md--#{5 * $i} {\r\n            margin-top: 5px *$i;\r\n        }\r\n\r\n        .mb_md--#{5 * $i} {\r\n            margin-bottom: 5px *$i;\r\n        }\r\n        \r\n    }\r\n}\r\n\r\n@media #{$sm-layout} {\r\n    .ptb_sm--250{\r\n        padding: 250px 0 !important;\r\n    }\r\n    .ptb_sm--0{\r\n        padding: 0 !important;\r\n    }\r\n    .pl_sm--0 {\r\n        padding-left: 0 !important;\r\n    }\r\n    .pr_sm--0 {\r\n        padding-right: 0 !important;\r\n    }\r\n    .pt_sm--0 {\r\n        padding-top: 0 !important;\r\n    }\r\n    .pb_sm--0 {\r\n        padding-bottom: 0 !important;\r\n    }\r\n    .mr_sm--0 {\r\n        margin-right: 0 !important;\r\n    }\r\n    .ml_sm--0 {\r\n        margin-left: 0 !important;\r\n    }\r\n    .mt_sm--0 {\r\n        margin-top: 0 !important;\r\n    }\r\n    .mb_sm--0 {\r\n        margin-bottom: 0 !important;\r\n    }\r\n    .pt_sm--150 {\r\n        padding-top: 150px !important;\r\n    }\r\n    .pb_sm--110 {\r\n        padding-bottom: 110px !important;\r\n    }\r\n    @for $i from 1 through 20 {\r\n        .ptb_sm--#{5 * $i} {\r\n            padding: 5px *$i 0;\r\n        }\r\n        .plr_sm--#{5 * $i} {\r\n            padding: 0 5px *$i;\r\n        }\r\n        .pt_sm--#{5 * $i} {\r\n            padding-top: 5px *$i;\r\n        }\r\n\r\n        .pb_sm--#{5 * $i} {\r\n            padding-bottom: 5px *$i;\r\n        }\r\n\r\n        .pl_sm--#{5 * $i} {\r\n            padding-left: 5px *$i;\r\n        }\r\n\r\n        .pr_sm--#{5 * $i} {\r\n            padding-right: 5px *$i;\r\n        }\r\n\r\n        .mt_sm--#{5 * $i} {\r\n            margin-top: 5px *$i;\r\n        }\r\n        \r\n        .ml_sm--#{5 * $i} {\r\n            margin-left: 5px *$i;\r\n        }\r\n\r\n        .mr_sm--#{5 * $i} {\r\n            margin-right: 5px *$i;\r\n        }\r\n\r\n        .mb_sm--#{5 * $i} {\r\n            margin-bottom: 5px *$i;\r\n        }\r\n    }\r\n\r\n    .pl_sm--0 {\r\n        padding-left: 0;\r\n    }\r\n    .pr_sm--0 {\r\n        padding-right: 0;\r\n    }\r\n    .pt_sm--0 {\r\n        padding-top: 0;\r\n    }\r\n    .pb_sm--0 {\r\n        padding-bottom: 0;\r\n    }\r\n    .mr_sm--0 {\r\n        margin-right: 0;\r\n    }\r\n    .ml_sm--0 {\r\n        margin-left: 0;\r\n    }\r\n    .mt_sm--0 {\r\n        margin-top: 0;\r\n    }\r\n    .mb_sm--0 {\r\n        margin-bottom: 0;\r\n    }\r\n    \r\n}\r\n\r\n\r\n@media #{$large-mobile}{\r\n    @for $i from 1 through 20 {\r\n        .ptb_mobile--#{5 * $i} {\r\n            padding: 5px *$i 0;\r\n        }\r\n        .plr_mobile--#{5 * $i} {\r\n            padding: 0 5px *$i;\r\n        }\r\n        .pt_mobile--#{5 * $i} {\r\n            padding-top: 5px *$i;\r\n        }\r\n        .pb_mobile--#{5 * $i} {\r\n            padding-bottom: 5px *$i;\r\n        }\r\n        .pl_mobile--#{5 * $i} {\r\n            padding-left: 5px *$i;\r\n        }\r\n        .pr_mobile--#{5 * $i} {\r\n            padding-right: 5px *$i;\r\n        }\r\n        .mt_mobile--#{5 * $i} {\r\n            margin-top: 5px *$i;\r\n        }\r\n        .mb_mobile--#{5 * $i} {\r\n            margin-bottom: 5px *$i;\r\n        }\r\n    }\r\n}\r\n\r\n"]}
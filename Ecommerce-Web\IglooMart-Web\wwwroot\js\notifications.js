﻿
$(function () {
    var token = document.getElementById("AuthToken").value;
    var connection = new signalR.HubConnectionBuilder()
        .withUrl(BASE_URL + "notificationHub", {
            accessTokenFactory: () => token,
            transport: signalR.HttpTransportType.WebSockets | signalR.HttpTransportType.LongPolling
        })
        .build();


    setTimeout(() => {
        connection.start()


        connection.on("ReceiveNotification", function (data) {
            toastr.success(data.message, data.title, {
                positionClass: 'toast-top-right',
                timeOut: 5000,
                closeButton: true,
            });
            getUserNotifications();
        });


    }, 3000)



})
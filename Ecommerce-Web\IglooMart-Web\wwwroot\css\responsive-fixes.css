/* Responsive fixes for mobile devices */

/* Service box mobile-friendly styles */
.service-box.mobile-friendly {
    padding: 20px 15px;
    margin-bottom: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.service-box.mobile-friendly .icon {
    margin-bottom: 15px;
}

.service-box.mobile-friendly .icon img {
    max-height: 50px;
    width: auto;
}

.service-box.mobile-friendly .title {
    font-size: 14px;
    line-height: 1.4;
    text-align: center;
    word-wrap: break-word;
}

/* Coupon section responsive styles */
.product-slide-mobile {
    margin: 0 -5px;
}

.product-slide-mobile .slick-single-layout {
    padding: 0 1px;
}

.card {
    overflow: hidden;
}

.card .main {
    flex-wrap: wrap;
}

.card .vertical {
    display: none;
}

.mobile-friendly-card {
    position: relative;
    border-radius: 8px !important;
    transition: all 0.3s ease;
    margin: 0 auto;
    max-width: 500px;
}

.mobile-friendly-card .price-text {
    white-space: normal;
    word-break: break-word;
}

.slick-single-layout {
    /*display: flex;*/
    justify-content: center;
}

.axil-product.product-style-six {
    width: 100%;
    display: flex;
    justify-content: center;
}

/* Media queries for different screen sizes */
@media only screen and (max-width: 767px) {
    .image-container-small {
        width: 100%;
        overflow: hidden;
    }



    .axil-why-choose-area {
        padding-top: 30px;
        padding-bottom: 20px !important;
    }

    .section-title-wrapper.section-title-center {
        margin-bottom: 20px;
    }

    .title-highlighter {
        font-size: 16px;
    }

    /* Coupon card mobile styles */
    .card {
        height: auto;
        padding: 15px 10px;
    }

    .card::before, .card::after {
        display: none;
    }

    .mobile-friendly-card {
        max-width: 100%;
    }

    .mobile-friendly-card .main {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        padding: 0 5px;
    }

    .mobile-friendly-card .co-img {
        width: 40%;
        text-align: center;
    }

    .mobile-friendly-card .co-img img {
        width: 70px;
        height: 70px;
        margin-bottom: 10px;
    }

    .mobile-friendly-card .content {
        width: 60%;
        text-align: right;
        padding-right: 0;
    }

    .mobile-friendly-card .content p {
        font-size: 14px;
        margin-bottom: 5px;
    }

    .mobile-friendly-card .content h1 {
        font-size: 28px;
        margin: 5px 0;
    }

    .mobile-friendly-card .content h2 {
        font-size: 14px;
        text-align: right;
        margin-top: 10px !important;
    }

    .mobile-friendly-card .buy-btn {
        width: 100%;
        margin-top: 5px;
        padding: 8px;
    }
    .offer-image {
        object-fit: inherit !important; 
    }
}

@media only screen and (max-width: 575px) {
    .service-box.mobile-friendly {
        padding: 15px 10px;
    }

    .service-box.mobile-friendly .icon img {
        max-height: 40px;
    }

    .service-box.mobile-friendly .title {
        font-size: 12px;
    }

    /* Smaller coupon card for very small screens */
    .mobile-friendly-card {
        padding: 12px 8px;
    }

    .mobile-friendly-card .main {
        padding: 0 3px;
    }

    .mobile-friendly-card .co-img {
        width: 35%;
    }

    .mobile-friendly-card .co-img img {
        width: 60px;
        height: 60px;
        margin-bottom: 8px;
    }

    .mobile-friendly-card .content {
        width: 65%;
    }

    .mobile-friendly-card .content p {
        font-size: 13px;
    }

    .mobile-friendly-card .content h1 {
        font-size: 24px;
    }

    .mobile-friendly-card .content h2 {
        font-size: 12px;
        margin-top: 8px !important;
    }

    .mobile-friendly-card .buy-btn {
        font-size: 12px;
        padding: 6px;
    }
}

@media only screen and (max-width: 479px) {
    .row.g-3 > .col {
        padding-left: 8px;
        padding-right: 8px;
    }

    /* Adjust coupon slider for very small screens */
    .product-slide-mobile {
        margin: 0 -3px;
    }

    .product-slide-mobile .slick-single-layout {
        padding: 0 3px;
    }

    /* Extra small screen coupon adjustments */
    .mobile-friendly-card {
        padding: 10px 5px;
    }

    .mobile-friendly-card .main {
        flex-direction: column;
    }

    .mobile-friendly-card .co-img,
    .mobile-friendly-card .content {
        width: 100%;
        text-align: center;
    }

    .mobile-friendly-card .content h2,
    .mobile-friendly-card .content p {
        text-align: center;
    }

    .mobile-friendly-card .co-img img {
        width: 80px;
        height: 80px;
        margin-bottom: 10px;
    }

    .mobile-friendly-card .content h1 {
        font-size: 32px;
        margin: 10px 0;
    }

    .mobile-friendly-card .content h1 span {
        font-size: 20px !important;
    }

    .mobile-friendly-card .content h2 {
        font-size: 14px;
        margin-top: 10px !important;
    }
}

/* RTL Fixes for Coupon Slider */
[dir="rtl"] .product-slide-mobile .slick-slide {
    float: right;
}

[dir="rtl"] .product-slide-mobile .slick-track {
    display: flex;
    flex-direction: row-reverse;
}

[dir="rtl"] .product-slide-mobile .slick-prev {
    right: auto;
    left: 0;
    transform: rotate(180deg);
}

[dir="rtl"] .product-slide-mobile .slick-next {
    left: auto;
    right: 0;
    transform: rotate(180deg);
}

[dir="rtl"] .product-slide-mobile .slick-prev:before {
    content: "\f054"; /* Font Awesome right arrow */
}

[dir="rtl"] .product-slide-mobile .slick-next:before {
    content: "\f053"; /* Font Awesome left arrow */
}

[dir="rtl"] .axil-product.product-style-six .content {
    text-align: right;
}

[dir="rtl"] .axil-product.product-style-six .buy-btn.co-bu {
    margin-right: auto;
    margin-left: 0;
}

/* Global Mobile Font Size Adjustments */
@media (max-width: 767px) {
    /* Base font size reduction */
    body {
        font-size: 14px;
    }

    /* Headings */
    h1, .h1 {
        font-size: 24px !important;
    }

    h2, .h2 {
        font-size: 20px !important;
    }

    h3, .h3 {
        font-size: 18px !important;
    }

    h4, .h4 {
        font-size: 16px !important;
    }

    h5, .h5 {
        font-size: 15px !important;
    }

    h6, .h6 {
        font-size: 14px !important;
    }

    /* Navigation and menu items */
    .nav-link, 
    .navbar-nav .nav-link,
    .menu-item {
        font-size: 13px !important;
    }

    /* Buttons */
    .btn {
        font-size: 13px !important;
        padding: 6px 12px !important;
    }

    /* Product titles and prices */
    .product-title,
    .product-name {
        font-size: 14px !important;
    }

    .price {
        font-size: 13px !important;
    }

    /* Form elements */
    input, 
    select, 
    textarea {
        font-size: 13px !important;
    }

    label {
        font-size: 13px !important;
    }

    /* Section titles */
    .section-title {
        font-size: 18px !important;
    }

    .title-highlighter {
        font-size: 14px !important;
    }

    /* Cards and boxes */
    .card-title {
        font-size: 15px !important;
    }

    .card-text {
        font-size: 13px !important;
    }
}

/* Mobile Responsive Fixes for Coupons Widget */
@media (max-width: 767px) {
    /* Main Slider Mobile Fixes */
    .main-slider-style-1 .swiper-container {
        /*height: 180px !important;*/
    }

    .main-slider-style-1 .image-container {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .main-slider-style-1 .image-slider {
        width: 100%;
        height: 100%;
        object-fit: contain;
        object-position: center;
    }

    .main-slider-style-1 .swiper-button-next,
    .main-slider-style-1 .swiper-button-prev {
        width: 25px;
        height: 25px;
    }

    .main-slider-style-1 .swiper-button-next:after,
    .main-slider-style-1 .swiper-button-prev:after {
        font-size: 16px;
    }

    .main-slider-style-1 .swiper-pagination {
        bottom: 5px;
    }

    /* Existing Coupon Widget Mobile Styles */
    .axil-best-seller-product-area {
        padding: 10px 0;
    }

    .slick-single-layout {
        padding: 0 5px;
    }

    .axil-product.product-style-six .card {
        width: 100%;
        min-height: 110px;
        margin: 0 auto;
    }

    .axil-product.product-style-six .main {
        padding: 4px;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .axil-product.product-style-six .co-img {
        width: 100%;
        max-width: 80px;
        margin-bottom: 2px;
    }

    .axil-product.product-style-six .co-img img {
        width: 100%;
        height: auto;
        max-height: 40px;
        object-fit: contain;
    }

    .axil-product.product-style-six .buy-btn.co-bu {
        width: 100%;
        max-width: 80px;
        margin-top: 2px;
        padding: 2px 4px;
        font-size: 10px;
    }

    .axil-product.product-style-six .content {
        width: 100%;
        text-align: center;
        padding: 1px 0;
    }

    .axil-product.product-style-six .content p {
        font-size: 10px;
        margin-bottom: 1px;
    }

    .axil-product.product-style-six .content h1 {
        font-size: 14px;
        margin: 1px 0;
    }

    .axil-product.product-style-six .content .currency-symbol {
        font-size: 18px;
        color: #343434;
    }

    .axil-product.product-style-six .content h2 {
        font-size: 9px;
        margin-top: 1px;
        text-align: end;
    }

    /* Hide vertical divider on mobile */
    .axil-product.product-style-six .vertical {
        display: none;
    }

    /* Adjust slick slider arrows for mobile */
    .product-slide-mobile .slick-prev,
    .product-slide-mobile .slick-next {
        width: 20px;
        height: 20px;
        top: -30px;
    }

    .product-slide-mobile .slick-prev {
        left: 0;
    }

    .product-slide-mobile .slick-next {
        right: 0;
    }

    /* Mobile-specific title adjustments */
    .title-highlighter.highlighter-primary {
        font-size: 14px !important;
    }
}

/* Desktop styles for coupons */
@media (min-width: 768px) {
    .axil-product.product-style-six .content p {
        font-size: 16px;
    }

    .axil-product.product-style-six .content h1 {
        font-size: 24px;
    }

    .axil-product.product-style-six .content .currency-symbol {
        font-size: 30px;
    }

    .axil-product.product-style-six .content h2 {
        font-size: 14px;
    }

    .axil-product.product-style-six .buy-btn.co-bu {
        font-size: 14px;
    }

    .title-highlighter.highlighter-primary {
        font-size: 18px;
    }
   

   
}

/* Tablet Responsive Fixes */
@media (min-width: 768px) and (max-width: 991px) {
    .axil-product.product-style-six .card {
        min-height: 200px;
    }

    .axil-product.product-style-six .co-img {
        max-width: 120px;
    }
}




